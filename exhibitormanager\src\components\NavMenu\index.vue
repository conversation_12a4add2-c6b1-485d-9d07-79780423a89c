<template>
  <div class="NavMenu" style="height: 60px; border-bottom: 1px solid #ebeef5">
    <div class="log" style="margin: 0 49px 0 104px">
      <img
        src="@/img/content_9_2.svg"
        alt=""
        style="width: 200px; height: 41px"
      />
    </div>
    <div class="user" v-if="userInfo && userInfo != ''">
      <div style="display: flex; margin-right: 8px" v-if="userInfo.avatar_url">
        <el-avatar size="large" :src="userInfo.avatar_url" />
      </div>
      <div style="padding-right: 10px">{{ userInfo.login }}</div>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import {
  getActityMsg,
  getatom,
  access_token,
  contactInfo,
} from "@/utils/apiManage";
import { getAccesToken, setAccesToken,removeAccesToken } from "@/utils/auth";
import { REDIRECT_URL } from "@/utils/request";
import { MessageBox} from "element-ui";
const  msgbox = MessageBox
moment.locale("zh-cn");
export default {
  name: "NavMenu",
  data() {
    return {
      userInfo: "",
      isShow: false,
    };
  },
  watch: {
    userInfo: {
      handler(val, oldvalue) {
        if (val.length !== oldvalue.length) {
          this.userInfo = val;
        }
      },
    },
  },
  mounted() {
    if (window.location.href.indexOf("/auth/login?code=") > -1) {
      let code =
        window.location.href.split("=").length >= 2
          ? window.location.href.split("=")[1]
          : null;
      if (!getAccesToken() && code) {
        this.accessToken({ code: code });
      }
    } else {
      const queryString = window.location.search;

      const urlParams = new URLSearchParams(queryString);
      if (urlParams.get("activityNo")) {
   
        localStorage.setItem("activityNo", urlParams.get("activityNo"));
        if (!getAccesToken()) {
          setTimeout(() => {
            if (!this.isShow) {
                 removeAccesToken();
              msgbox("您未登录，现在去登录？", "系统提示", {
                confirmButtonText: "确定",
              }).then(()=>{
                   window.location.href = REDIRECT_URL;
              })
            }
          }, 500);
        }
      } else {
         localStorage.removeItem("activityNo")
        msgbox("请输入有效的活动编号", "系统提示", {
          confirmButtonText: "确定",
          callback: (action) => {
            return;
          },
        });
      }
    }
    if (getAccesToken()) {
      this.getUser();
    }
  },
  methods: {
    accessToken(data) {
      //获取accesToken
      return access_token(data).then((res) => {
        if (res.accessToken) {
          this.isShow = true;
          setAccesToken(res.accessToken);
          this.$message({
            message: "登录成功",
            type: "success",
            offset: 60,
          });
          setTimeout(() => {
            this.getUser();
            if (
              localStorage.getItem("activityNo") &&
              localStorage.getItem("activityNo") != ""
            ) {
              window.location.href =
                window.location.origin +
                "/apply/index?activityNo=" +
                localStorage.getItem("activityNo");
            }else{
                window.location.href =
                window.location.origin +
                "/apply/index?activityNo="
            }
          }, 500);
        } else {
          this.$message({
            message: JSON.stringify(res),
            offset: 60,
            type: "warning",
          });
        }
      });
    },
    getUser() {
      return getatom().then((res) => {
        if (res) {
          localStorage.setItem("userInfo", JSON.stringify(res));
          this.userInfo = res;
        } else {
          this.$message({
            message: JSON.stringify(res),
            type: "warning",
            offset: 60,
          });
        }
      });
    },
  },
};
</script>

<style>
.NavMenu {
  display: flex;
  align-items: center;
  position: relative;
  background-color: #fff;
}
.el-menu-item {
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0px;
  text-align: center;
}
.el-submenu__title {
  font-family: PingFang SC;
  font-size: 16px !important;
  font-weight: 400;
  letter-spacing: 0px;
  text-align: center;
}
.user {
  position: absolute;
  right: 60px;
  display: flex;
  color: #909399;
  align-items: center;
}
</style>

