import Vue from "vue";
import Router from "vue-router";
import { develop } from "@/utils/baseKey.js";
Vue.use(Router);

/* Layout */
import Layout from "@/layout";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/loginFree",
    component: () => import("@/views/loginFree"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  {
    path: "/regView",
    component: () => import("@/views/regView"),
    hidden: true,
  },
  {
    path: "",
    component: Layout,
    redirect: "index",
    hidden: false,
    children: [
      {
        path: "index",
        component: () => import("@/views/index"),
        name: "Index",
        meta: { title: "首页", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/sign",
    component: Layout,
    hidden: true,
    redirect: "sign",
    children: [
      {
        path: "",
        component: () => import("@/views/sign/signPage/signPage.vue"),
        name: "signPage",
        meta: { title: "签到页面", icon: "skill" },
      },
    ],
  },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "Profile",
        meta: { title: "个人中心", icon: "user" },
      },
    ],
  },
  {
    path:"/question",
    component: Layout,
    hidden: false,
    redirect: "noredirect",
    children:[
      {
        path: "questionSeting",
        component: () => import("@/views/question/questionSeting"),
        name: "questionSeting",
        meta: { title: "问卷设置", icon: "link" },
      }
    ]
  },
  {
    path:"/guest",
    component: Layout,
    hidden: false,
    redirect: "noredirect",
    children:[
      {
        path: "guestIndex",
        component: () => import("@/views/guest/guestIndex"),
        name: "guestIndex",
        meta: { title: "嘉宾管理", icon: "link" },
      }
    ]
  },
  {
    path:"/exhibitor",
    component: Layout,
    hidden: false,
    redirect: "noredirect",
    children:[
      {
        path: "exhibitoTodo",
        component: () => import("@/views/exhibitor/exhibitorIndex"),
        name: "exhibitoTodo",
        meta: { title: "展商审批", icon: "link" },
      },
      {
        
        path: "exhibitToDo",
        component: () => import("@/views/exhibitor/exhibitToDo"),
        name: "exhibitToDo",
        meta: { title: "展品审批", icon: "link" },
      },
      {
        path:"exhibitorList",
        component: () => import("@/views/exhibitor/exhibitorList"),
        name: "exhibitorList",
        meta: { title: "展商列表", icon: "link" },
      },
      {
        path:"booth",
        component: () => import("@/views/exhibitor/boothIndex"),
        name: "boothIndex",
        meta: { title: "展位管理", icon: "link" },
      },
      {
        path:"committee",
        component: () => import("@/views/exhibitor/committee"),
        name: "committee",
        meta: { title: "组委会配置", icon: "link" },
      }
    ]
  },
  {
    path:"/allPerson",
    component: Layout,
    hidden: false,
    redirect: "noredirect",
    children:[
      {
        path: "allPersonIndex",
        component: () => import("@/views/allPerson/allPersonIndex"),
        name: "allPersonIndex",
        meta: { title: "人员库", icon: "link" },
      },
    
    ]
  },
  {
    path: "/activity",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "post",
        component: () => import("@/views/system/post/index"),
        name: "formCreat",
        meta: { title: "活动管理", icon: "link" },
      },
      {
        path: "publicize",
        component: () => import("@/views/system/publicize/index"),
        name: "publicize",
        meta: { title: "活动宣传", icon: "international" },
      },
      {
        path: "formCreat",
        component: () => import("@/views/activity/formCreat/formList"),
        name: "formCreat",
        meta: { title: "报名表单管理", icon: "excel" },
      },

      {
        path: "approval",
        component: () => import("@/views/system/approval/index"),
        name: "approval",
        meta: { title: "报名审批管理", icon: "edit" },
      },
      {
        path: "personnel",
        component: () => import("@/views/system/personnel/index"),
        name: "personnel",
        meta: { title: "报名人员管理", icon: "user" },
      },
      {
        path: "privacy",
        component: () => import("@/views/system/privacy/index"),
        name: "privacy",
        meta: { title: "隐私设置", icon: "user" },
      },
      {
        path: "ticketType",
        component: () => import("@/views/ticket/ticketType"),
        name: "ticketType",
        meta: { title: "票种设置", icon: "money" },
      },
      {
        path: "preferential",
        component: () => import("@/views/ticket/preferential"),
        name: "preferential",
        meta: { title: "优惠卷设置", icon: "money" },
      },
      {
        path: "order",
        component: () => import("@/views/ticket/order"),
        name: "order",
        meta: { title: "订单管理", icon: "build" },
      },
      {
        path: "signManage",
        component: () => import("@/views/sign/signManage/index"),
        name: "signManage",
        meta: { title: "签到管理", icon: "skill" },
      },
      {
        path: "statistical",
        component: () => import("@/views/statistical/index"),
        name: "statistical",
        meta: { title: "统计与数据分析", icon: "link" },
      },
    ],
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:user:edit"],
    children: [
      {
        path: "role/:userId(\\d+)",
        component: () => import("@/views/system/user/authRole"),
        name: "AuthRole",
        meta: { title: "分配角色", activeMenu: "/system/user" },
      },
    ],
  },
  {
    path: "/system/role-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:role:edit"],
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/system/role/authUser"),
        name: "AuthUser",
        meta: { title: "分配用户", activeMenu: "/system/role" },
      },
    ],
  },
  {
    path: "/system/dict-data",
    component: Layout,
    hidden: true,
    permissions: ["system:dict:list"],
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/system/dict/data"),
        name: "Data",
        meta: { title: "字典数据", activeMenu: "/system/dict" },
      },
    ],
  },
  {
    path: "/monitor/job-log",
    component: Layout,
    hidden: true,
    permissions: ["monitor:job:list"],
    children: [
      {
        path: "index/:jobId(\\d+)",
        component: () => import("@/views/monitor/job/log"),
        name: "JobLog",
        meta: { title: "调度日志", activeMenu: "/monitor/job" },
      },
    ],
  },
  {
    path: "/tool/gen-edit",
    component: Layout,
    hidden: true,
    permissions: ["tool:gen:edit"],
    children: [
      {
        path: "index/:tableId(\\d+)",
        component: () => import("@/views/tool/gen/editTable"),
        name: "GenEdit",
        meta: { title: "修改生成配置", activeMenu: "/tool/gen" },
      },
    ],
  },
];

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch((err) => err);
};
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch((err) => err);
};

export default new Router({
  mode: "history", // 去掉url中的#
  // base:'/atomgit/',
  // base:'atomgitTest',
  base: develop() == "prew" ? "adminTest" : "admin",
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
});
