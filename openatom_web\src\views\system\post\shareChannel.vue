<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" label-width="68px">
        <el-form-item style="margin-left: 10px">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            @click="add()"
            >新增推广渠道</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="tableData" style="width: 90%" v-loading="loading">
      <el-table-column prop="seq" label="序号" width="60">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="活动名称"
        :show-overflow-tooltip="true"
      >
        <div>{{ this.activityName }}</div>
      </el-table-column>
      <el-table-column
        prop="channelName"
        label="渠道"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="channelCode"
        label="标签"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column prop="address" label="操作" width="120">
        <template slot-scope="{ row }">
          <el-button
            v-if="row.channelCode && row.channelCode != ''"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            @click="detail(row)"
            type="text"
            icon="el-icon-search"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="searchForm.pageNum"
      :limit.sync="searchForm.pageSize"
      @pagination="getData"
    />
    <el-dialog title="推广渠道" :visible.sync="visible" width="30%">
      <el-form
        ref="form"
        :model="form"
        label-width="80px"
        :rules="rules"
        @submit.native.prevent
      >
        <el-form-item label="推广渠道" prop="channelName">
          <el-input
            v-model="form.channelName"
            type="text"
            placeholder="请填写推广渠道"
            maxlength="20"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="addCancel">取 消</el-button>
        <el-button
          size="mini"
          type="primary"
          @click="confirm"
          :loading="btnLoading"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog title="查看" :visible.sync="open" width="30%">
      <el-form :model="form" label-width="80px">
        <el-form-item label="推广渠道:" style="margin-bottom: 0">
          {{ this.form.channelName }}
        </el-form-item>
        <el-form-item label="P C 报名:">
          <div>
            <!-- <div style="width: 60px">报名链接:</div> -->
            <div>
              {{ qrPCCodeUrl }}
            </div>
          </div>
        </el-form-item>
        <el-form-item label="手机报名:">
          <div>
            <!-- <div style="width: 60px">报名链接:</div> -->
            <div>
              {{ qrCodeUrl }}
            </div>
          </div>
          <vue-qr
            class="qr-code"
            :logoSrc="imageUrl"
            :text="qrCodeUrl"
            :size="200"
            :whiteMargin="true"
            :autoColor="true"
            :correctLevel="3"
          />
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="tuiguangCancel">取 消</el-button>
      </span>
    </el-dialog>
    <div
      slot="footer"
      class="dialog-footer"
      style="text-align: center; margin-top: 30px"
    >
      <el-button @click="goBack">取 消</el-button>
    </div>
  </div>
</template>

<script>
const defaultForm = {
  content: "",
};
const defaultSearchForm = {
  hasSign: false,
  pageNum: 1,
  pageSize: 10,
};
import request from "@/utils/request";
import { develop } from "@/utils/baseKey.js";
import vueQr from "vue-qr";
export default {
  name: "shareChannel",
  components: { vueQr },
  data() {
    return {
      tableData: [],
      remark: "",
      visible: false,
      activityId: "",
      id: "",
      searchForm: { ...defaultSearchForm },
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      total: 0,
      rules: {
        channelName: [
          { required: true, message: "推广渠道不能为空", trigger: "blur" },
        ],
      },
      form: {
        channelName: undefined,
      },
      loading: false,
      qrCodeUrl: "",
      qrPCCodeUrl: "",
      imageUrl: require("../../../assets/images/yuanzi.png"),
      open: false,
      btnLoading: false,
    };
  },

  beforeCreate() {},
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activityId = activeInfo.activityId;
    this.form.activityId = activeInfo.activityId;
  },
  mounted() {
    this.getData();
  },
  update() {},
  methods: {
    goBack() {
      this.$parent.HandleChannelCancel();
      this.form = {
        activityId: undefined,
        channelName: undefined,
      };
    },
    resetSearch() {
      this.searchForm = { ...defaultSearchForm };
      this.signTimes = [];
      this.getData();
    },
    detail(row) {
      let keyWords = develop() == "test" ? "Test" : "";
      if (row.channelCode && row.channelCode != "") {
        this.qrCodeUrl =
          window.location.origin +
          "/registration_mobile" +
          keyWords +
          "/?code=" +
          row.channelCode +
          "&activityNo=" +
          this.activityId;
        this.qrPCCodeUrl =
          window.location.origin +
          "/registration" +
          keyWords +
          "/?code=" +
          row.channelCode +
          "&activityNo=" +
          this.activityId;
      } else {
        this.qrCodeUrl =
          window.location.origin +
          "/registration_mobile" +
          keyWords +
          "/?activityNo=" +
          this.activityId;
        this.qrPCCodeUrl =
          window.location.origin +
          "/registration" +
          keyWords +
          "/?activityNo" +
          this.activityId;
      }

      this.open = true;
      this.form.channelName = row.channelName;
    },
    add() {
      this.visible = true;
      //   this.searchForm.pageNum = 1
      //   this.getData()
    },
    addCancel() {
      this.visible = false;
      this.form.channelName = undefined;
      this.form.id = undefined;
    },
    tuiguangCancel() {
      this.open = false;
      this.form.channelName = undefined;
      this.form.id = undefined;
    },
    getData() {
      this.loading = true;
      request({
        url: "/channel/list",
        method: "post",
        data: {
          activityId: this.activityId,
        },
      })
        .then((response) => {
          let obj = {
            channelCode: "",
            channelName: "默认",
          };
          this.tableData.push(obj);
          response.data.unshift(obj);
          this.tableData = response.data;
          this.loading = false;
        })
        .catch((err) => {
          console.log(err);
          this.loading = false;
        });
    },
    cancel() {
      this.visible = false;
      this.form.channelName = undefined;
      this.form.id = undefined;
    },
    handleUpdate(row) {
      this.visible = true;
      this.form.channelName = row.channelName;
      this.form.id = row.id;
      //  this.form.activityId
    },
    confirm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          let url = "";
          if (this.form.id && this.form.id != "") {
            url = "/channel/update";
          } else {
            url = "/channel/save";
          }
          request({
            url: url,
            method: "post",
            data: {
              ...this.form,
            },
          })
            .then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.cancel();
              this.getData();
              this.btnLoading = false;
            })
            .catch((err) => {
              console.log(err);
              this.loading = false;
              this.btnLoading = false;
            });
        }
      });
    },
  },
};
</script>
<style lang="scss"></style>
