<template>
  <div id="app" style="max-width: 600px;
    margin: auto;
    text-align: center;">
    <div v-if="isWeichatBrowser" style="color: red;height: 100px;">
      点击右上角按钮，然后在弹出的菜单中，点击在浏览器打开
    </div>
    <div v-else>
      <router-view/>
      <div style="position:fixed;right:1px;bottom:100px;border-radius: 25px;overflow: hidden;opacity: 0.8;" v-if="dictContactData">
        <van-popover
          v-model="showPopover"
          placement="left"
          trigger="click"
        >
          <div style="margin: auto;text-align: left; padding: 10px 15px; line-height: 32px;">
            <div style="line-height: 32px;">
              <van-image
              round
              cover
              width="2.5rem"
              height="2.5rem"
              :src="customerService"
              style="float: left;"
              />
              <div style="line-height: 35px;padding-top: 5px; height: 32px; float: left;padding-left: 10px;">联系客服</div>
              <div style="clear: both;"></div>
            </div>
            <div v-for="item in dictContactData">
              <span v-if="item && item.dictLabel">{{ item && item.dictValue ? (item.dictLabel+":"+item.dictValue) : "" }}</span>
            </div>
          </div>
          <template #reference>
            <van-icon name="service" color="#ffffff" size="30" style="padding: 5px;background:rgb(22, 119, 255)"/>
          </template>
        </van-popover>
      </div>
    </div>
  </div>
</template>

<script>
import{dictContact,getatom} from '@/utils/apiManage'
import customerService from '@/assets/customerService.svg'
import{getAccesToken} from '@/utils/auth'
export default {
  name: 'App',
  data(){
    return{
      showPopover:false,
      dictContactData:null,
      customerService,
      isWeichatBrowser:false,
    }
  },
  
  mounted(){
    if(this.dictContactData === null || !this.dictContactData || (this.dictContactData && this.dictContactData.length<=0)){
      this.getDictContact()
    }
    if(!localStorage.getItem("userInfo") && getAccesToken()){
      this.getUser()
    }
  },
  methods:{
    
    getDictContact(){
      return dictContact().then(res => {
        if(res && res.data && res.data.length>0){
         this.dictContactData= res.data
        }
       
      })
    },
    getUser(){
      return getatom().then(res => {
        if(res){
          localStorage.setItem("userInfo", JSON.stringify(res))
        }else{
          Notify({ type: 'warning', message: JSON.stringify(res) });
        }
        
      })
    },
  }
}
</script>

<style>
html,body{
  height: 100%;
  width:100%;
  overflow: auto;
  text-align:center;
  margin: 0px;
  background-color: #eaeaf9 !important;
}
html{
  overflow-y: auto;
}
#app {
  line-height: 22px;
  font-family: system-ui;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  height: 100%;
}
.calback{
  padding-top: 8px;
  text-align: left;
  font-weight: bold;
  padding-bottom: 20px;
  width: 100%;
  background-color: #fff;
}
.pageTile{
  width: 100%;
  margin: auto;
  text-align: center;
  top: 8px;
  font-weight: bold;
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 120px;
  height: 120px;
}
</style>
