<template>

  <div class="registEnter">

    <van-overlay :show="showLoading" style="z-index: 99999; position: absolute;height: 100%;">
      <div class="wrapper">
        <van-loading type="spinner" color="#1989fa" class="block" />
      </div>
    </van-overlay>

      <div class="registBaner" :style="bannerStyle">
        <div style="padding: 50px 10px 0px 10px;">
          {{ activeMsg ? activeMsg.activityName:"" }}
        </div>
      </div>
      <div style="position: relative;top: -30px; width: 80%;margin: auto;text-align: center;">

        <div class="intrMsg" style="margin-bottom: 20px; font-size: 14px; text-indent:2em; white-space: pre-wrap;">{{ activeMsg ? activeMsg.activityDescription:"" }}</div>

        <div class="intrMsg" style="text-align: center;line-height: 25px;background: #feebd7;color: #f89a37;">
          <div style="font-weight: bold;font-size: 16px;">活动时间/Event Time</div>
          <div style="font-size: 14px;">{{ activeMsg ? activeMsg.startDate+" - "+activeMsg.endDate : "" }}</div>
        </div>

        <div class="registerNow">
          <el-button v-if="activeMsg && activeMsg.activityStatus === 2" type="primary" size="large" @click="registerNow(1)" style="width: 100%;">立即报名/Sign Up</el-button>
          <el-button v-if="activeMsg && activeMsg.activityStatus === 1" type="info" size="large" style="width: 100%;padding: 15px 20px;">活动未开始</el-button>
          <el-button v-if="activeMsg && activeMsg.activityStatus === 3" type="info" size="large" style="width: 100%;padding: 15px 20px;">活动已结束</el-button><br>
          <el-link type="primary" @click="registerNow(2)" style="margin-top: 10px; margin-bottom: 10px;">报名查询/Registration Inquiry</el-link>
        </div>

      </div>

  </div>
</template>

<script>
import { access_token,getActityMsg,getatom,getWxOpenid,getActivityImage} from '@/utils/apiManage'
import{getAccesToken,setAccesToken} from '@/utils/auth'
import{ REDIRECT_URL } from '@/utils/request'
import {Dialog,Notify} from "vant"
import moment from 'moment'
moment.locale("zh-cn");
export default {
  name: 'RegistEnter',
  props: {
  },
  data(){
    return {
      activeMsg:null,
      showLoading:false,
      bannerStyle: {backgroundImage: `url(${require("../assets/bg.png")})`},
    }
  },
  mounted(){
    localStorage.removeItem("code")
    if(window.location.href.indexOf("/mobile/auth/login?code=")>-1){
      let code=window.location.href.split("=").length>=2?window.location.href.split("=")[1]:null
      if((!getAccesToken()) && code){
        this.accessToken({code:code})
      }else{
        this.getActityData()
      }
    }else{
      const queryString = window.location.search;
      const urlParams = new URLSearchParams(queryString);
      if(urlParams.get("activityNo")){
        localStorage.setItem("activityNo",urlParams.get("activityNo"))
      }
      if(urlParams.get("code")){
        localStorage.setItem("channelCode",urlParams.get("code"))
      }
      this.getActityData()
    }

  },
  methods:{
       //获取活动背景图片
    getActivityImageFront(activityNo) {
      return getActivityImage(activityNo,3).then(
        (res) => {
          if (res.code == 200 && res.data) {
            this.bannerStyle =
              res.data.imageUrl && res.data.imageUrl != ""
                ? {backgroundImage:`url(${res.data.imageUrl})`}
                : {backgroundImage: `url(${require("../assets/bg.png")})`};
          } else {
            this.bannerStyle = {backgroundImage: `url(${require("../assets/bg.png")})`};
          }
        }
      );
    },
    getUser(){
      return getatom().then(res => {
        if(res){
          localStorage.setItem("userInfo", JSON.stringify(res))
        }else{
          Notify({ type: 'warning', message: JSON.stringify(res) });
        }

      })
    },
    registerNow(type){

      if(getAccesToken()){
        if(type === 1){
          if(localStorage.getItem("activityNo")){
            localStorage.setItem("redirectType",1)
            this.$router.replace({ path: '/UserRegist'})
          }else{
            Notify({ type: 'warning', message: "活动编号不存在" });
          }

        }else{
          localStorage.setItem("showOrder",false)
          localStorage.setItem("redirectType",3)
          this.$router.replace({ path: '/RegistView'})

        }
      }else{
        Dialog.alert({
          title: '系统提示/Tips',
          message: '您未登录，现在去登录？/You are not logged in, would you like to log in now?',
          confirmButtonText: '确认/Confirm',
        }).then(() => {
          window.location.href=REDIRECT_URL
        });
      }
    },

    accessToken(data){//获取accesToken
      return access_token(data).then(res => {
        if(res.accessToken){
          setAccesToken(res.accessToken)
          Notify({ type: 'success', message: '登录成功' });
          this.getActityData()
          this.getUser()
        }else{
          Notify({ type: 'warning', message: JSON.stringify(res) });
        }
      })
    },
    getOpenid(data){
      return getWxOpenid(data).then(res => {
        if(res){
          localStorage.setItem("openId",res.openId)
        }else{
          Notify({ type: 'warning', message: JSON.stringify(res) });
        }
      })
    },
    getActityData(){
      if(localStorage.getItem("activityNo")){
       this.getActivityImageFront(localStorage.getItem("activityNo"))
        this.showLoading=true
        return getActityMsg({"activityId":localStorage.getItem("activityNo")}).then(res => {
          this.showLoading=false
          if(res.code === 200 && res.data){
            res.data.startDate = moment(res.data.startDate).format('YYYY.MM.DD')
            res.data.endDate = moment(res.data.endDate).format('YYYY.MM.DD')
            localStorage.setItem("activity",JSON.stringify(res.data))

            this.activeMsg = res.data
          }else{
            this.activeMsg = null
            Notify({ type: 'warning', message: JSON.stringify(res) });
          }
        })
      }else{
        Notify({ type: 'warning', message: "活动编号不存在" });
      }
    },


  },


}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
body{
  padding-right: 0px !important;
  overflow: hidden;
}
h3 {
  margin: 40px 0 0;
}
a {
  color: #42b983;
}
.registEnter{
  margin: 0px;
  /* padding: 0px 0px 10px 0px; */
}
.registBaner{
  height: 150PX;
  width: 100%;
  color: #535353;
  font-weight: bold;
  font-size: 18px;
  /* background-image: url("../assets/bg.png"); */
}
.intrMsg{
  color: #535353;
  min-height: 50px;
  margin: auto;
  padding: 15px 20px;
  overflow-wrap: anywhere;
  /* border:1px solid #cccccc; */
  border-radius: 15px;
  background-color: #fff;
  text-align: left;

}
.registerNow{
  padding-top: 20px;
  font-size: 14px !important;
}

</style>
