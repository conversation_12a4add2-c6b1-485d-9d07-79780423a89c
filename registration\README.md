## 开发

```bash
# 安装依赖
node v18.14.0
npm  v9.3.1
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建生产环境
npm run build
```

## 项目文件介绍

# 项目图片
    assets
# 订单核算&支付
    OrderPay
# 订单15分钟过期倒计时组件
    PayTimer
# 活动报名入口  首页面
    RegistEnter
# 个人中心,订单列表,报名信息，入场票
    RegistViewNew
# 报名流程 （选票->填写信息->提交订单->支付）
    UserRegist
# 路由配置
    router
# 工具包
    utils
# 接口API
    apiManage.js
# 开发环境配置
    baseKey.js
# axios封装
    request.js
# 静态资源
    city.js
# 工具方法
    auth.js ,registration.js