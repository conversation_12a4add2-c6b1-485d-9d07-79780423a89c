import request from "@/utils/request";

// 获取订单
export function getPreferentialList(data) {
  return request({
    url: "/order/manage/list",
    method: "get",
    params:data,
  });
}

// 删除订单
export function delOrder(id) {
  return request({
    url: "/order/manage/del/" + id,
    method: "delete",
  });
}

// 查询订单对应人员信息
export function getOrderPersonList(data) {
  return request({
    url: "/order/manage/reg/list",
    method: "post",
    data,
  });
}
// 获取流水
export function getTradeFlow(pageNum,pageSize,data) {
  // console.log((pageNum,pageSize,data)
  return request({
    url: "/tradeFlow/list?pageNum="+pageNum+"&pageSize="+pageSize,
    method: "post",
    data:data,
  });
}
export default { getPreferentialList, delOrder, getOrderPersonList,getTradeFlow };
