<template>
  <div class="app-container">
    <div>
      <el-form
        ref="activeform"
        :model="flowform"
        :rules="flowRules"
        label-width="110px"
        class="selfForm"
      >
        <el-form-item label="活动编号:" prop="activityId">
          <div>{{ activeform.activityId }}</div>
        </el-form-item>
        <el-form-item label="活动名称:" prop="activityName">
          <div>{{ activeform.activityName }}</div>
        </el-form-item>
         <el-form-item label="是否自动审批:" prop="activityName">
          <div>{{activeform.isAutoApproval== 1
                ? "是"
                : "否" }}</div>
        </el-form-item>
        <el-form-item label="活动地点:" prop="activityPlace">
          <div>{{ activeform.activityPlace }}</div>
        </el-form-item>
        <el-form-item label="活动简介:" prop="activityDescription">
          <div class="activeDec">{{ activeform.activityDescription }}</div>
        </el-form-item>
        <el-form-item label="活动周期:" prop="stratEndTime">
          <el-date-picker
            :disabled="true"
            v-model="activeform.stratEndTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="签到人员" prop="adminActiveUser">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="addActive"
            >新增签到人员</el-button
          >
          <el-table
            v-loading="Activeloading"
            :data="activeList"
            @selection-change="handleSelectionChange"
            size="mini"
            height="250"
          >
            <el-table-column
              label="用户"
              align="center"
              prop="userName"
              :show-overflow-tooltip="true"
            />

            <el-table-column
              label="角色"
              align="center"
              prop="userRole"
              :show-overflow-tooltip="true"
            />
            <el-table-column label="创建时间" align="center" prop="createTime">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建人" align="center" prop="createBy">
            </el-table-column>

            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
              width="50"
            >
              <!-- v-hasPermi="['system:post:edit']" -->
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDeleteUser(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="activetotal > 0"
            :total="activetotal"
            :page.sync="activeQuery.pageNum"
            :limit.sync="activeQuery.pageSize"
            @pagination="getSignInAdminList"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="Activecancel">返回</el-button>
      </div>
    </div>
    <el-dialog
      custom-class="selfDialog"
      :title="'选择活动签到人员'"
      :visible.sync="sysUserOpen"
      width="500px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-form
        ref="sysUserform"
        :model="sysUserListform"
        label-width="100px"
        class="selfForm"
        :inline="true"
          @submit.native.prevent
      >
        <el-form-item label="用户名称/账号" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户名称"
            clearable
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="getuserList"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
      <div style="max-height: 500px; overflow: auto">
        <el-table
          v-loading="loading"
          :data="userList"
          @selection-change="handleSelectionChange"
          size="mini"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            :selectable="enableSelect"
          />

          <el-table-column
            label="用户名称"
            align="center"
            key="user_name"
            prop="user_name"
            v-if="columns[0].visible"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            label="手机号码"
            align="center"
            key="phonenumber"
            prop="phonenumber"
            v-if="columns[1].visible"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            label="邮箱"
            align="center"
            key="email"
            prop="email"
            v-if="columns[2].visible"
            :show-overflow-tooltip="true"
          />
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getuserList"
          :page-sizes="[10, 50, 100, 500]"
          :page-size="10"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sysuserSubForm" :loading="btnloading">确 定</el-button>
        <el-button @click="sysUsercancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
import moment from "moment";
moment.locale("zh-cn");
export default {
  name: "editSign",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchParam: undefined,
        // postCode: undefined,
        // postName: undefined,
        // status: undefined,
      },
      // 表单参数
      form: {
        activityName: undefined,
        activityDescription: undefined,
        activityPlace: undefined,
      },
      // 表单校验
      rules: {
        activityName: [
          { required: true, message: "活动名称不能为空", trigger: "blur" },
        ],
        activityDescription: [
          { required: true, message: "活动简介不能为空", trigger: "blur" },
        ],
        stratEndTime: [
          { required: true, message: "活动周期不能为空", trigger: "blur" },
        ],
      },
      flowRules: {
        flowno: [
          { required: true, message: "请选择报名流程", trigger: "blur" },
        ],
      },
      flowOpen: false,
      flowform: { flowno: undefined },
      flowOptions: [],
      flowDisabled: false,
      activeOpen: false,
      activeform: {},
      activeList: [],
      Activeloading: false,
      activeQuery: {
        pageNum: 1,
        pageSize: 10,
      },
      activetotal: 0,
      sysUserOpen: false,
      sysUserListform: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
      },
      // 列信息
      columns: [
        { key: 1, label: `用户名称`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 6, label: `邮箱`, visible: true },
      ],
      userList: [],
      selectedUser: [],
      btnloading:false
    };
  },
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activeform = activeInfo;
    this.activeform.stratEndTime = [];
    this.activeform.stratEndTime.push(new Date(activeInfo.startDate));
    this.activeform.stratEndTime.push(new Date(activeInfo.endDate));

    this.activeQuery.activityId = activeInfo.activityId;
  },
  mounted() {
    this.getSignInAdminList();
  },
  methods: {
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedUser = [...selection];
    },
    Activecancel() {
      //   this.activeOpen = false;
      this.activeform = {};
      this.$parent.shareSignEditCancel();
    },
    sysUsercancel() {
      this.sysUserOpen = false;
      this.sysUserform = {};
    },

    getSignInAdminList() {
      this.Activeloading = true;
      let formData = new window.FormData();
      Object.keys(this.activeQuery).map((item) => {
        if (this.activeQuery[item]) {
          formData.append(item, this.activeQuery[item]);
        }
      });
      request({
        url: "activityManage/getSignInAdminList",
        method: "post",
        // data: this.activeQuery,
        data: formData,
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            this.activeList = response.rows;
            this.activetotal = response.total;
            this.Activeloading = false;
          } else {
            this.Activeloading = false;
            this.activeList = [];
            this.activetotal = 0;
          }
        })
        .catch((err) => {
          this.Activeloading = false;
          console.log(err);
        });
    },
    addActive(row) {
      this.sysUserOpen = true;
      this.sysUserform = row;
      this.getuserList();
    },
    /** 查询用户列表 */
    getuserList() {
      this.loading = true;
      request({
        url:
          "system/user/signAdminList",
        method: "get",
         params:{...this.queryParams}
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            this.userList = response.rows;
            this.total = response.total;
            this.loading = false;
          } else {
            this.userList = [];
            this.total = 0;
            this.loading = false;
          }
        })
        .catch((err) => {
          this.Activeloading = false;
          console.log(err);
        });
    },
    sysuserSubForm() {
      if (this.selectedUser.length > 0) {
        this.btnloading=true
        this.selectedUser.map((item) => {
          item.activityId = this.activeform.activityId;
          item.userName=item.user_name
        });
        request({
          url: "activityManage/saveSignInAdmin",
          method: "post",
          data: { userList: this.selectedUser },
        })
          .then((response) => {
            if (response.code == 200) {
              this.$modal.msgSuccess("操作成功");
              this.sysUserOpen = false;
              this.getSignInAdminList();
               this.btnloading=false
            }
          })
          .catch((err) => {
            console.log(err);
            this.btnloading=false
          });
      } else {
        this.$message.error("请选择要分配的签到人员");
      }
    },
    enableSelect(row, index) {
      let useStr = "";
      this.activeList.map((item) => {
        useStr += item.userName + ",";
      });
      if (useStr.indexOf(row.user_name) == -1 && row.user_id != 1) {
        return true;
      }
    },
    handleDeleteUser(row) {
      this.$modal
        .confirm('是否确认删除用户为"' + row.userName + '"的活动签到人员？')
        .then(() => {
          request({
            url: "activityManage/deleteSignInAdmin",
            method: "post",
            data: { id: row.id },
          })
            .then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("操作成功");
                this.sysUserOpen = false;
                this.getSignInAdminList();
              }
            })
            .catch((err) => {
              console.log(err);
            });
          // return delPost(obj);
        });
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss">
.activeDec {
  height: 150px;
  overflow: auto;
  border: 1px solid #ccc;
  border-radius: 3px;
  padding: 5px;
}
.selfForm .el-form-item {
  margin-bottom: 10px;
}
.selfDialog .el-dialog__body {
  padding: 10px;
}
// ::v-deep .el-tooltip__popper{
//   min-width: 10px !important;
//   max-width: 300px !important;
// }
.el-tooltip__popper.is-dark {
  min-width: 10px !important;
  max-width: 300px !important;
}
</style>
  
