<template>
  <div class="app-containers">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输人员姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="姓名" prop="contactName">
        <el-input
          v-model="queryParams.contactName"
          placeholder="请输人员姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="手机号" prop="contactPhone">
        <el-input
          v-model="queryParams.contactPhone"
          placeholder="请输人员手机号"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <!-- <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          :disabled="selected.length > 0 ? false : true"
          >批量审批</el-button
        > -->
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
       :data="
        approvalList.slice(
          (queryParams.pageNum - 1) * queryParams.pageSize,
          queryParams.pageNum * queryParams.pageSize
        )
      "
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="50" align="center" /> -->
      <el-table-column
        label="公司名称"
        align="center"
        prop="companyName"
        width="150px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="公司组织性质"
        align="center"
        prop="companyType"
           width="150px"
        :show-overflow-tooltip="true"
      >
      </el-table-column>

      <el-table-column
        label="公司地址"
        align="center"
        prop="companyAddress"
            width="150px"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        label="公司网站"
        align="center"
        prop="companyPortal"
            width="150px"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        label="意向面积"
        align="center"
        prop="areaSize"
          width="150px"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        label="主营业务"
        align="center"
        prop="mainBusiness"
           width="150px"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        label="姓名"
        align="center"
        prop="contactName"
           width="150px"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        label="部门"
        align="center"
        prop="contactDept"
           width="150px"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        label="职务"
        align="center"
        prop="contactDuties"
           width="150px"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        label="手机"
        align="center"
        prop="contactPhone"
           width="150px"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        label="邮箱"
        align="center"
        prop="contactEmail"
           width="150px"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <!-- <el-table-column
        label="传真"
        align="center"
        prop="contactFax"
        :show-overflow-tooltip="true"
      >
      </el-table-column> -->
        <el-table-column
        prop="applyUser"
        label="申请人"
        width="100px"
        :show-overflow-tooltip="true"
      />
        <el-table-column
        prop="approveStatus"
        label="审核状态"
        :show-overflow-tooltip="true"
         width="150px"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="申请时间"
        :show-overflow-tooltip="true"
         width="150px"
      ></el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="100"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >审批</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="margin-top: 10px"
      align="right"
      v-show="approvalList.length > 0"
      :total="approvalList.length"
      :current-page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="10"
      layout="total, sizes, prev, pager,next"
      @size-change="handleSizeChangeSys"
      @current-change="handleCurrentChangeSys"
    />
    <el-dialog
      custom-class="selfDialog"
      :title="'审批'"
      :visible.sync="UserOpen"
      width="800px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-collapse v-model="activepanel">
        <el-collapse-item title="审批信息" name="useInfo">
          <div style="height: 300px; overflow: auto">
            <el-form :model="stepsForm" ref="stepsForm" label-width="100px">
              <p>公司信息</p>
              <el-form-item label="公司名称" prop="companyName">
                {{ stepsForm.companyName }}
              </el-form-item>
              <el-form-item label="公司组织性质" prop="companyType">
                {{ stepsForm.companyType }}
              </el-form-item>
              <el-form-item label="公司地址" prop="companyAddress">
                {{ stepsForm.companyAddress }}
              </el-form-item>
              <el-form-item label="公司网站" prop="companyPortal">
                {{ stepsForm.companyPortal }}
              </el-form-item>
              <el-form-item label="意向面积" prop="areaSize">
                {{ stepsForm.areaSize }}<span>㎡</span>
              </el-form-item>
              <el-form-item label="主营业务" prop="mainBusiness">
                {{ stepsForm.mainBusiness }}
              </el-form-item>
              <p>联系人信息</p>
              <el-form-item label="姓名" prop="contactName">
                {{ stepsForm.contactName }}
              </el-form-item>
              <el-form-item label="部门" prop="contactDept">
                {{ stepsForm.contactDept }}
              </el-form-item>
              <el-form-item label="职务" prop="contactDuties">
                {{ stepsForm.contactDuties }}
              </el-form-item>
              <el-form-item label="手机" prop="contactPhone">
                {{ stepsForm.contactPhone }}
              </el-form-item>
              <el-form-item label="邮箱" prop="contactEmail">
                {{ stepsForm.contactEmail }}
              </el-form-item>
              <el-form-item label="传真" prop="contactFax">
                {{ stepsForm.contactFax }}
              </el-form-item>
            </el-form>
            <form-create
              :rule="formCreateRule"
              :value.sync="formValue"
              :option="option"
            ></form-create>
          </div>
        </el-collapse-item>
        <el-collapse-item title="审批流程" name="approval">
          <el-form
            ref="appform"
            :model="appform"
            :rules="apprule"
            label-width="100px"
          >
            <el-form-item label="审批意见" prop="remarks">
              <el-input type="textarea" v-model="appform.remarks" />
            </el-form-item>
            <el-form-item label="审批结果" prop="action">
              <el-select v-model="appform.action" @change="actionChange">
                <el-option value="同意">同意</el-option>
                <el-option value="驳回">驳回</el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="展区展位1" prop="bus_info" v-if="appform.action=='同意'">
              <el-select v-model="appform.bus_info">
                <el-option
                  v-for="(item, idx) in selectList"
                  :key="idx"
                  :value="item.positionNumber"
                  :label="item.positionNumber+' ('+item.positionAreaSize+'㎡)'"
                ></el-option>
              </el-select>
            </el-form-item> -->
          </el-form>
          <el-table v-loading="apploading" :data="waitList">
            <el-table-column
              label="序号"
              align="center"
              prop="key"
              type="index"
              :show-overflow-tooltip="true"
              width="100px"
            />
            <el-table-column
              label="审批人"
              align="center"
              prop="oper_user"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="操作"
              align="center"
              prop="oper_info"
              :show-overflow-tooltip="true"
            >
            </el-table-column>

            <el-table-column
              label="说明"
              align="center"
              prop="remarks"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              label="时间"
              align="center"
              prop="oper_date"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="SubForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="selfDialog"
      :title="'批量审批'"
      :visible.sync="batchOpen"
      width="800px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-form
        ref="batchform"
        :model="batchform"
        :rules="batchrule"
        label-width="100px"
      >
        <el-form-item label="审批意见" prop="remarks">
          <el-input type="textarea" v-model="batchform.remarks" />
        </el-form-item>
        <el-form-item label="审批结果" prop="action">
          <el-select v-model="batchform.action" @change="batchChange">
            <el-option value="同意">同意</el-option>
            <el-option value="驳回">驳回</el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="batchSubForm" :loading="btnloading"
          >确 定</el-button
        >
        <el-button @click="batchOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
<script>
import request from "@/utils/request";
import formCreate from "@form-create/iview";
export default {
  name: "haveApproval",
  data() {
    return {
      batchrule: {
        action: [
          { required: true, message: "请选择审批结果", trigger: "blur" },
        ],
        remarks: [
          { required: false, message: "请填写审批意见", trigger: "blur" },
        ],
      },
      btnloading: false,
      batchform: {},
      activeName: "first",
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: undefined,
      },
      approvalList: [],
      loading: false,
      total: 0,
      UserOpen: false,
      activepanel: "approval",
      formParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: undefined,
      },
      formCreateRule: [],
      formValue: {},
      flowform: {},
      option: {
        form: { labelPosition: "top" },
        submitBtn: { show: false },
      },
      appform: {},
      apprule: {
        action: [
          { required: true, message: "请选择审批结果", trigger: "blur" },
        ],
        remarks: [
          { required: false, message: "请填写审批意见", trigger: "blur" },
        ],
      },
      waitList: [],
      apploading: false,
      stepsForm: {
        // 公司信息
        companyName: "",
        companyType: "",
        companyAddress: "",
        companyPortal: "",
        areaSize: "",
        mainBusiness: "",
        // 联系人信息
        contactName: "",
        contactDept: "",
        contactDuties: "",
        contactPhone: "",
        contactEmail: "",
        contactFax: "",
      },
      selected: [],
      batchOpen: false,
      selectList: [],
    };
  },
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.queryParams.activityId = activeInfo.activityId;
    this.queryParams.active_Name = activeInfo.activityName;
    this.getSelect()
  },
  watch: {
    "queryParams.activityId": {
      handler(val, oldval) {
        if (val != oldval) {
          this.getList();
          this.queryParams.activityId = val;
        }
      },
      immediate: true,
    },
  },
  methods: {
          handleCurrentChangeSys(val) {
      this.queryParams.pageNum = val;
    },
    // 当当前页改变
    handleSizeChangeSys(val) {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = val;
    },
    getSelect() {
      const data = {
        activityId: this.queryParams.activityId,
        status: "0",
      };
      (this.queryParams.approveStatus = "审批中"),
        request({
          url: "/display/position/manage/list",
          method: "get",
          params: data,
        })
          .then((response) => {
            this.selectList = response.data;
            this.loading = false;
          })
          .catch((err) => {
            console.log(err);
          });
    },

    batchChange(val) {
      if (val == "驳回") {
        this.batchrule.remarks[0].required = true;
      } else {
        this.batchrule.remarks[0].required = false;
      }
    },
    actionChange(val) {
      if (val == "驳回") {
        this.apprule.remarks[0].required = true;
      } else {
        this.apprule.remarks[0].required = false;
      }
    },
    handleSelectionChange(selection) {
      this.selected = [...selection];
    },
    batchSubForm() {
      this.$refs["batchform"].validate((valid) => {
        if (valid) {
          this.btnloading = true;
          let bus_pk = [];
          this.selected.map((item, index) => {
            console.log(item);
            bus_pk.push(item.busPk);
            if (index == this.selected.length - 1) {
            //   this.batchform.bus_info = this.batchform.action;
              this.batchform.buspklist = bus_pk;
              this.batchform.activityNo = this.queryParams.activityId;
              this.batchform.flowno = "3";
              request({
                url: "/flow/batchupdatebusiness",
                method: "post",
                data: this.batchform,
              })
                .then((response) => {
                  this.$modal.msgSuccess("操作成功");
                  this.batchOpen = false;
                  this.getList();
                  this.btnloading = false;
                })
                .catch((err) => {
                  console.log(err);
                  this.btnloading = false;
                });
            }
          });
        }
      });
    },
    getList() {
      (this.queryParams.approveStatus = "审批中"),
        request({
          url: "/display/business/manage/list",
          method: "get",
          params: this.queryParams,
        })
          .then((response) => {
            this.approvalList = response.data;
            // this.total = response.total;
            this.loading = false;
          })
          .catch((err) => {
            console.log(err);
          });
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleUpdate(row) {
      this.stepsForm = row;
      this.UserOpen = true;
      this.getFlowList(row.busPk);
      this.appform.activityNo = row.activityId;
    },
    SubForm() {
      this.$refs["appform"].validate((valid) => {
        if (valid) {
          request({
            url: "/flow/updatebusiness",
            method: "post",
            data: this.appform,
          })
            .then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.UserOpen = false;
              this.getList();
            })
            .catch((err) => {
              console.log(err);
            });
        }
      });
    },
    cancel() {
      this.UserOpen = false;
    },
    getFlowList(data) {
      this.apploading = true;
      request({
        url: "/flow/list?bus_pk=" + data,
        method: "get",
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            this.waitList = response.rows;
            this.appform.flowno = response.rows[0].flowno;
            this.appform.bus_pk = data;
          }
          this.apploading = false;
        })
        .catch((err) => {
          this.apploading = false;
          console.log(err);
        });
    },
    handleAdd() {
      this.batchOpen = true;
    },
  },
};
</script>
<style scoped rel="stylesheet/scss" lang="scss">
::v-deep .form-create button {
  display: none;
}

::v-deep #self .el-form-item {
  margin-bottom: 5px;
}

::v-deep .form-create .el-form-item__label {
  width: 100px !important;
}

// ::v-deep .form-create .el-form-item__content {
//   margin-left: 100px !important;
// }
// ::v-deep .form-create .el-input {
//   width: 78% !important;
// }
::v-deep .form-create .el-select {
  width: 100% !important;
}

.el-tooltip__popper.is-dark {
  min-width: 10px !important;
  max-width: 300px !important;
}

::v-deep .form-create .el-select {
  width: 100%;
}

::v-deep .form-create .el-date-editor {
  width: 100%;
}
</style>