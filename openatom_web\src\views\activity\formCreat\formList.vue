<template>
  <div class="app-container">
    <!-- <el-row :gutter="10" v-if="!open">
      <el-col :span="24">
        <el-card style="height: calc(100vh - 125px)">
          <div slot="header">
            <span><i class="el-icon-key"></i> 表单管理</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              icon="el-icon-refresh-right"
              @click="refreshCacheKeys()"
            ></el-button>
          </div>
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            label-width="68px"
          >
            <el-form-item label="活动选择" prop="activityId">
              <el-select
                v-model="queryParams.activityId"
                placeholder="请选择活动"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in activeOption"
                  :key="dict.activityId"
                  :label="dict.activityName"
                  :value="dict.activityId"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['activity:form:save']"
                >新增</el-button
              >
            </el-form-item>
          </el-form>
          <el-table
            v-loading="tableLoading"
            :data="formList"
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column
              label="表单名称"
              align="center"
              key="formName"
              prop="formName"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              label="所属活动"
              align="center"
              key="activityname"
              prop="activityname"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              label="活动编号"
              align="center"
              key="activityId"
              prop="activityId"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              label="创建人"
              align="center"
              :show-overflow-tooltip="true"
              key="createBy"
              prop="createBy"
            >
            </el-table-column>
            <el-table-column
              label="创建时间"
              align="center"
              :show-overflow-tooltip="true"
              key="createTime"
              prop="createTime"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              width="160"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope" v-if="scope.row.userId !== 1">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['activity:form:update']"
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['activity:form:delete']"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getFromList"
          />
        </el-card>
      </el-col>
    </el-row> -->
    <!-- 添加或修改用户配置对话框 -->
    <!-- <el-dialog
      :title="'报名表单配置'"
      :visible.sync="open"
      width="1300px"
      append-to-body
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="cancel"
    > -->
    <div>
      <div style="display: flex; justify-content: space-between">
        <el-form
          :model="formParams"
          ref="formParams"
          size="small"
          :inline="true"
          label-width="100px"
          :rules="rules"
        >
        
          <el-form-item label="活动名称" prop="activityId">
              <div
          style="
            max-width: 300px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          "
        >
          {{queryParams.activityname }}
        </div>
            
            <!-- <el-select
              v-model="formParams.activityId"
              placeholder="请输入用户名称"
              clearable
              style="width: 240px"
              :disabled="this.formParams.id > 0"
            >
              <el-option
                v-for="dict in activeOption"
                :key="dict.activityId"
                :label="dict.activityName"
                :value="dict.activityId"
              />
            </el-select> -->
          </el-form-item>
            <el-form-item label="表单名称" prop="formName">
            <el-input
              v-model="formParams.formName"
              placeholder="请输入用户名称"
              clearable
            />
          </el-form-item>
        </el-form>
        <div>
          <!-- <template slot="handle">
            <el-button size="mini" type="primary" @click="handleUploadRule()"
              >导入JSON</el-button
            >
            <el-button size="mini" @click="handleDownloadRule()"
              >导出JSON</el-button
            >
          </template> -->
          <!-- <el-button size="mini" type="primary" @click="handleUploadRule()"
            >表单预览</el-button> -->
          <!-- <el-button size="mini" @click="handleDownloadRule()"
            >导出JSON</el-button
          > -->
        </div>
      </div>

      <el-divider content-position="left">静态内容</el-divider>
      <!-- <el-form class="formInfo" size="small" label-width="10px">
        <el-form-item label="" prop="">
          <span style="font-weight: 700">
            系统自动为表单填充如下字段：
            用户类型、姓名、性别、手机号、国籍\省市、证件类型、证件号码、邮箱地址、工作单位、职务；</span
          >
        </el-form-item>
        <el-form-item label="" prop="">
          <span style="font-weight: 700">
            报名表单页面自动拼装静态表单内容和动态表单内容；</span
          >
        </el-form-item>
      </el-form> -->
      <div style="min-height: 200px; display: flex; max-height: 400px">
        <div
          class="cutsome"
          style="width: 266px; border-right: 1px solid #ececec"
        >
          <div>
            <el-button plain size="mini" @click="formShowFun('userType')"
              >用户类型</el-button
            >
            <el-button plain size="mini" :disabled="true">姓名</el-button>
            <el-button plain size="mini" @click="formShowFun('sex')"
              >性别</el-button
            >
            <el-button plain size="mini" :disabled="true">手机号</el-button>
            <el-button plain size="mini" :disabled="true">邮箱</el-button>
          </div>
          <div>
            <el-button plain size="mini" @click="formShowFun('nationality')"
              >国籍</el-button
            >
            <el-button plain size="mini" @click="formShowFun('provinceCity')"
              >省市</el-button
            >
            <el-button plain size="mini" @click="formShowFun('idCardType')"
              >证件类型/号码</el-button
            >
            <el-button plain size="mini" @click="formShowFun('company')"
              >工作单位</el-button
            >
            <el-button plain size="mini" @click="formShowFun('job')"
              >职务</el-button
            >
          </div>
        </div>
        <el-form
          class="formInfo"
          size="small"
          :label-position="'top'"
          :inline="false"
          style="
            height: 100%;
            max-height: 400px;
            overflow: auto;
            width: calc(100% - 266px);
            margin: 0 20px;
          "
        >
          <el-form-item
            :label="formShow[0].title"
            prop=""
            v-if="formShow[0].open"
          >
            <el-select
              v-model="formShow[0].value"
              placeholder="请选择人员类型"
              clearable
              style="width: 70%"
            >
              <el-option
                v-for="dict in userOption"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
            <el-checkbox
              style="padding-left: 5px"
              v-model="formShow[0].required"
              @change="(e) => changeFormShow('userType', e)"
              >是否必填</el-checkbox
            >
            <i
              class="el-icon-delete"
              title="删除"
              style="padding-left: 5px; color: red; cursor: pointer"
              @click="deleteFormFun('userType')"
            ></i>
          </el-form-item>
          <el-form-item label="姓名" prop="">
            <el-input style="width: 70%"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="" v-if="formShow[4].open">
            <el-radio-group v-model="formShow[4].value" size="small">
              <el-radio label="1" border>男</el-radio>
              <el-radio label="2" border>女</el-radio>
            </el-radio-group>
            <el-checkbox
              style="padding-left: 5px"
              v-model="formShow[4].required"
              @change="(e) => changeFormShow('sex', e)"
              >是否必填</el-checkbox
            >
            <i
              class="el-icon-delete"
              title="删除"
              style="padding-left: 5px; color: red; cursor: pointer"
              @click="deleteFormFun('sex')"
            ></i>
          </el-form-item>
          <el-form-item label="手机号" prop="">
            <el-input style="width: 70%"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="">
            <el-input style="width: 70%"></el-input>
          </el-form-item>

          <el-form-item label="国籍" prop="" v-if="formShow[5].open">
            <el-radio-group v-model="formShow[5].value" size="small">
              <el-radio label="1" border>中国</el-radio>
              <el-radio label="2" border>外国籍</el-radio>
            </el-radio-group>
            <el-checkbox
              style="padding-left: 5px"
              v-model="formShow[5].required"
              @change="(e) => changeFormShow('nationality', e)"
              >是否必填</el-checkbox
            >
            <i
              class="el-icon-delete"
              title="删除"
              style="padding-left: 5px; color: red; cursor: pointer"
              @click="deleteFormFun('nationality')"
            ></i>
          </el-form-item>
          <el-form-item label="省市" prop="" v-if="formShow[6].open">
            <el-cascader
              style="width: 70%"
              v-model="formShow[6].value"
              :options="provinceCityOption"
              clearable
            ></el-cascader>
            <el-checkbox
              style="padding-left: 5px"
              v-model="formShow[6].required"
              @change="(e) => changeFormShow('provinceCity', e)"
              >是否必填</el-checkbox
            >
            <i
              class="el-icon-delete"
              title="删除"
              style="padding-left: 5px; color: red; cursor: pointer"
              @click="deleteFormFun('provinceCity')"
            ></i>
          </el-form-item>

          <el-form-item label="证件类型" prop="" v-if="formShow[7].open">
            <el-select
              v-model="formShow[7].value"
              placeholder="请选择证件类型"
              clearable
              style="width: 70%"
            >
              <el-option label="身份证" value="身份证"></el-option>
              <el-option label="护照" value="护照"></el-option>
              <el-option label="港澳通行证" value="港澳通行证"></el-option>
            </el-select>
            <el-checkbox
              style="padding-left: 5px"
              v-model="formShow[7].required"
              @change="(e) => changeFormShow('idCardType', e)"
              >是否必填</el-checkbox
            >
            <i
              class="el-icon-delete"
              title="删除"
              style="padding-left: 5px; color: red; cursor: pointer"
              @click="deleteFormFun('idCardType')"
            ></i>
          </el-form-item>
          <el-form-item label="证件号码" prop="" v-if="formShow[7].open">
            <el-input style="width: 70%"></el-input>
            <el-checkbox
              style="padding-left: 5px"
              v-model="formShow[7].required"
              @change="(e) => changeFormShow('idCardType', e)"
              >是否必填</el-checkbox
            >
            <i
              class="el-icon-delete"
              title="删除"
              style="padding-left: 5px; color: red; cursor: pointer"
              @click="deleteFormFun('idCardType')"
            ></i>
          </el-form-item>
          <el-form-item label="工作单位" prop="" v-if="formShow[8].open">
            <el-input style="width: 70%"></el-input>
            <el-checkbox
              style="padding-left: 5px"
              v-model="formShow[8].required"
              @change="(e) => changeFormShow('company', e)"
              >是否必填</el-checkbox
            >
            <i
              class="el-icon-delete"
              title="删除"
              style="padding-left: 5px; color: red; cursor: pointer"
              @click="deleteFormFun('company')"
            ></i>
          </el-form-item>
          <el-form-item label="职务" prop="" v-if="formShow[9].open">
            <el-input style="width: 70%"></el-input>
            <el-checkbox
              style="padding-left: 5px"
              v-model="formShow[9].required"
              @change="(e) => changeFormShow('job', e)"
              >是否必填</el-checkbox
            >
            <i
              class="el-icon-delete"
              title="删除"
              style="padding-left: 5px; color: red; cursor: pointer"
              @click="deleteFormFun('job')"
            ></i>
          </el-form-item>
        </el-form>
      </div>
      <!-- :menu="menuList" -->
      <!-- <fc-designer
        ref="designer"
        height="500px"
        :menu="menuList"
        v-if="menuList.length > 0"
      /> -->
      <el-divider content-position="left">动态配置</el-divider>
      <div v-loading="designerLoading">
        <fc-designer ref="designer">
          <template slot="handle">
            <el-button size="mini" type="primary" @click="handleUploadRule()"
              >导入JSON</el-button
            >
            <el-button size="mini" @click="handleDownloadRule()"
              >导出JSON</el-button
            >
          </template>
        </fc-designer>
      </div>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <!-- <el-button  @click="handleUploadRule()"
              >导入动态表单JSON</el-button
            >
            <el-button  @click="handleDownloadRule()"
              >导出动态表单JSON</el-button
            > -->
        <el-button @click="handlePrew()">表单预览</el-button>
        <el-button type="primary" @click="submitForm" :loading="btnloading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>
    <!-- </el-dialog> -->
    <el-dialog
      :title="'报名表单预览'"
      :visible.sync="prewOPen"
      :close-on-click-modal="false"
      append-to-body
      :destroy-on-close="true"
      width="600px"
    >
      <div style="max-height: 600px; overflow: auto">
        <el-form
          class="formInfo"
          size="small"
          :label-position="'top'"
          :inline="false"
          :rules="prewRules"
          style="margin: 0 20px"
        >
          <el-form-item
            label="用户类型"
            :prop="formShow[0].defaultName"
            v-if="formShow[0].open"
          >
            <el-select
              v-model="formShow[0].value"
              placeholder="请选择人员类型"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in userOption"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input></el-input>
          </el-form-item>
          <el-form-item
            label="性别"
            :prop="formShow[4].defaultName"
            v-if="formShow[4].open"
          >
            <el-radio-group v-model="formShow[4].value" size="small">
              <el-radio label="1" border>男</el-radio>
              <el-radio label="2" border>女</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input></el-input>
          </el-form-item>

          <el-form-item
            label="国籍"
            :prop="formShow[5].defaultName"
            v-if="formShow[5].open"
          >
            <el-radio-group v-model="formShow[5].value" size="small">
              <el-radio label="1" border>中国</el-radio>
              <el-radio label="2" border>外国籍</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="省市"
            :prop="formShow[6].defaultName"
            v-if="formShow[6].open"
          >
            <el-cascader
              style="width: 100%"
              v-model="formShow[6].value"
              :options="provinceCityOption"
              clearable
            ></el-cascader>
          </el-form-item>

          <el-form-item
            label="证件类型"
            :prop="formShow[7].defaultName"
            v-if="formShow[7].open"
          >
            <el-select
              v-model="formShow[7].value"
              placeholder="请选择证件类型"
              clearable
              style="width: 100%"
            >
              <el-option label="身份证" value="身份证"></el-option>
              <el-option label="护照" value="护照"></el-option>
              <el-option label="港澳通行证" value="港澳通行证"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="证件号码"
            :prop="formShow[7].defaultName"
            v-if="formShow[7].open"
          >
            <el-input style="width: 100%"></el-input>
          </el-form-item>
          <el-form-item
            label="工作单位"
            :prop="formShow[8].defaultName"
            v-if="formShow[8].open"
          >
            <el-input style="width: 100%"></el-input>
          </el-form-item>
          <el-form-item
            label="职务"
            :prop="formShow[9].defaultName"
            v-if="formShow[9].open"
          >
            <el-input style="width: 100%"></el-input>
          </el-form-item>
        </el-form>
        <form-create
          style="margin: 0 20px"
          :rule="formCreateRule"
          :value.sync="formValue"
          :option="formOption"
        ></form-create>
      </div>

      <el-row style="text-align: center; margin-top: 10px">
        <el-button @click="prewOPen = false">取 消</el-button>
        <!-- <el-button type="primary" @click="handleImport()">导 入</el-button> -->
      </el-row>
    </el-dialog>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogState"
      :close-on-click-modal="false"
      append-to-body
      :destroy-on-close="true"
    >
      <codemirror
        v-model="codemirrorContent"
        :options="codemirrorOptions"
        style="height: 90%; text-align: left; border: 1px solid #ccc"
      >
      </codemirror>
      <el-row v-if="dialogMenu">
        <el-button @click="dialogState = false">取 消</el-button>
        <el-button type="primary" @click="handleImport()">导 入</el-button>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
import jsonlint from "jsonlint-mod";
import { codemirror } from "vue-codemirror";
import "codemirror/addon/lint/lint";
import "codemirror/addon/lint/json-lint";
import "codemirror/lib/codemirror.css";
import "codemirror/addon/lint/lint.css";
import "codemirror/addon/edit/matchbrackets.js";
import "codemirror/mode/javascript/javascript.js";
import provinceCity from "./provinceCity.js";
export default {
  name: "formList",
  data() {
    return {
      activeOption: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: undefined,
        activityname: undefined,
      },
      formParams: {
        formName: undefined,
        activityId: undefined,
        formInfo: [],
      },
      subLoading: false,
      open: false,
      formList: [],
      tableHeight: window.innerHeight - 200,
      dialogTitle: "", // 对话框标题
      dialogState: false, // 对话框状态
      dialogMenu: false, // 对话框菜单状态
      codemirrorOptions: {
        mode: "application/json",
        theme: "default",
        gutters: ["CodeMirror-lint-markers"],
        tabSize: 2,
        lint: true,
        line: true,
        lineNumbers: true,
        matchBrackets: true,
        lineWrapping: true,
        styleActiveLine: true,
        readOnly: false,
      },
      // codemirror内容
      codemirrorContent: null,
      // 表单校验
      rules: {
        formName: [
          { required: true, message: "表单名称不能为空", trigger: "blur" },
        ],
        // activityId: [
        //   { required: true, message: "请选择活动", trigger: "blur" },
        // ],
        // phonenumber: [
        //   {
        //     pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        //     message: "请输入正确的手机号码",
        //     trigger: "blur",
        //   },
        // ],
      },
      prewRules: {
        name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
        phone: [{ required: true, message: "手机号不能为空", trigger: "blur" }],
        email: [{ required: true, message: "邮箱不能为空", trigger: "blur" }],
        userType: [
          { required: true, message: "用户类型不能为空", trigger: "blur" },
        ],
        sex: [{ required: true, message: "性别不能为空", trigger: "blur" }],
        nationality: [
          { required: true, message: "国籍不能为空", trigger: "blur" },
        ],
        provinceCity: [
          { required: true, message: "省市不能为空", trigger: "blur" },
        ],
        idCardType: [
          { required: true, message: "证件号码不能为空", trigger: "blur" },
        ],
        cardType: [
          { required: true, message: "证件类型不能为空", trigger: "blur" },
        ],
        company: [
          { required: true, message: "工作单位不能为空", trigger: "blur" },
        ],
        job: [{ required: true, message: "职务不能为空", trigger: "blur" }],
      },
      // 总条数
      total: 0,
      formList: [],
      tableLoading: false,
      menuList: [],
      designerLoading: true,
      formShow: [
        {
          open: false,
          required: false,
          value: undefined,
          sort: 1,
          defaultName: "userType",
          title: "用户类型",
        },
        {
          open: true,
          required: true,
          value: undefined,
          sort: 2,
          defaultName: "name",
          title: "姓名",
        },
        {
          open: true,
          required: true,
          value: undefined,
          sort: 3,
          defaultName: "phone",
          title: "手机号码",
        },
        {
          open: true,
          required: true,
          value: undefined,
          sort: 4,
          defaultName: "email",
          title: "邮箱",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 5,
          defaultName: "sex",
          title: "性别",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 6,
          defaultName: "nationality",
          title: "国籍",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 7,
          defaultName: "provinceCity",
          title: "省市",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 8,
          defaultName: "idCardType",
          title: "证件类型/号码",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 9,
          defaultName: "company",
          title: "工作单位",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 10,
          defaultName: "job",
          title: "职务",
        },
      ],
      userOption: [],
      provinceCityOption: [],
      prewOPen: false,
      formOption: {
        form: { labelPosition: "top" },
        submitBtn: { show: false },
      },
      formValue: {},
      formCreateRule: [],
      btnloading:false
    };
  },
  components: {
    codemirror,
  },
  beforeCreate() {
    // 开启JSON校验
    window.jsonlint = jsonlint;
  },
  created() {
    //  this.getactiveOption();

    this.getuserType();
    this.provinceCityOption = provinceCity;
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.queryParams.activityId = activeInfo.activityId;
    this.queryParams.activityname = activeInfo.activityName;
    // this.formParams.formName = activeInfo.activityName;
    this.formParams.activityId = activeInfo.activityId;
    // this.formParams={...activeInfo}
    this.open = true;
    // console.log(this.formShow);
    setTimeout(() => {
      let menuList = this.$refs.designer.menuList;
      if (menuList && menuList.length > 0) {
        menuList[0].list.map((item) => {
          if (
            item.name != "input" &&
            item.name != "select" &&
            item.name != "datePicker" &&
            item.name != "timePicker"
          ) {
            this.$refs.designer.removeMenuItem(item.name);
          }
        });
      }
      this.$refs.designer.removeMenuItem("radio");
      this.$refs.designer.removeMenuItem("cascader");
      this.$refs.designer.removeMenuItem("rate");
      this.$refs.designer.removeMenuItem("fc-editor");
      this.$refs.designer.removeMenuItem("el-transfer");
      this.$refs.designer.removeMenu("aide");
      this.$refs.designer.removeMenu("layout");
      this.$refs.designer.setOption({ form: { labelPosition: "top" } });
      this.designerLoading = false;
    }, 500);
  },
  mounted() {
    this.getFromList();
  },

  methods: {
    formShowFun(type) {
      // this.formShow[type].open=!this.formShow[type].open]
      this.formShow.map((item) => {
        if (item.defaultName == type) {
          item.open = true;
        }
      });
      // this.formShow[type].open = true;
    },
    deleteFormFun(type) {
      this.formShow.map((item) => {
        if (item.defaultName == type) {
          item.open = false;
        }
      });
    },
    changeFormShow(type, e) {
      // this.formShow[type].required = e;
      this.formShow.map((item) => {
        if (item.defaultName == type) {
          item.required = e;
        }
      });
    },
    getuserType() {
      request({
        url: "/system/dict/data/list?pageNum=1&pageSize=100&dictType=activity_user_type&status=0",
        method: "get",
      })
        .then((response) => {
          this.userOption = response.rows;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getactiveOption() {
      request({
        url: "/activityManage/list",
        method: "post",
        data: { pageNum: 1, pageSize: 10000 },
      })
        .then((response) => {
          this.activeOption = response.rows;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getFromList() {
      this.tableLoading = true;
      request({
        url: "/form/list",
        method: "get",
        params: this.queryParams,
      })
        .then((response) => {
          if (response.total == 1) {
            this.formParams.formInfo[1] = response.rows[0].formInfo[1];
            this.formParams.formInfo[0] = response.rows[0].formInfo[0];
            this.formShow = response.rows[0].formInfo[0];
             this.formParams.formName = response.rows[0].formName;
            this.formParams.id = response.rows[0].id;
            this.$refs.designer.setRule(response.rows[0].formInfo[1]);
          }
          // this.formList = response.rows;
          // this.total = response.total;
          // this.tableLoading = false;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getFromList();
    },
    handleAdd() {
      this.open = true;
      setTimeout(() => {
        let menuList = this.$refs.designer.menuList;
        if (menuList && menuList.length > 0) {
          menuList[0].list.map((item) => {
            if (
              item.name != "input" &&
              item.name != "select" &&
              item.name != "datePicker" &&
              item.name != "timePicker"
            ) {
              this.$refs.designer.removeMenuItem(item.name);
            }
          });
        }
        this.$refs.designer.removeMenuItem("radio");
        this.$refs.designer.removeMenuItem("cascader");
        this.$refs.designer.removeMenuItem("rate");
        this.$refs.designer.removeMenuItem("fc-editor");
        this.$refs.designer.removeMenuItem("el-transfer");
        this.$refs.designer.removeMenu("aide");
        this.$refs.designer.removeMenu("layout");
        this.$refs.designer.setOption({ form: { labelPosition: "top" } });
        this.designerLoading = false;
      }, 500);
    },
    handleUpdate(row) {
      this.open = true;
      this.designerLoading = true;
      this.formParams.formName = row.formName;
      this.formParams.activityId = row.activityId;
      this.formParams.formInfo[1] = row.formInfo[1];
      this.formParams.formInfo[0] = row.formInfo[0];
      this.formShow = row.formInfo[0];
      this.formParams.id = row.id;
      setTimeout(() => {

        //插入拖拽按钮到`main`分类下
        //  this.$refs.designer.removeMenuItem("main", "input");
        // this.$refs.designer.appendMenuItem("main", {
        //   icon: "icon-checkbox",
        //   name: "checkbox",
        //   label: "xzguo",
        // });
        //  this.$refs.designer.removeMenuItem("input")
        let menuList = this.$refs.designer.menuList;
        if (menuList && menuList.length > 0) {
          menuList[0].list.map((item) => {
            if (
              item.name != "input" &&
              item.name != "select" &&
              item.name != "datePicker" &&
              item.name != "timePicker"
            ) {
              this.$refs.designer.removeMenuItem(item.name);
              // this.$forceUpdate();
            }
          });
        }
        this.$refs.designer.removeMenuItem("radio");
        this.$refs.designer.removeMenuItem("cascader");
        this.$refs.designer.removeMenuItem("rate");
        this.$refs.designer.removeMenuItem("fc-editor");
        this.$refs.designer.removeMenuItem("el-transfer");
        this.$refs.designer.removeMenu("aide");
        this.$refs.designer.removeMenu("layout");
        this.$refs.designer.setRule(row.formInfo[1]);
        this.$refs.designer.setOption({ form: { labelPosition: "top" } });
        this.designerLoading = false;
      }, 500);
    },
    cancel() {
      this.open = false;
      this.designerLoading = true;
      this.formParams = {
        formName: undefined,
        activityId: undefined,
        formInfo: [],
      };
      this.$router.push("/activity/post");
    },
    // 导出表单JSON
    handleDownloadRule() {
      this.dialogTitle = "表单规则";
      this.dialogState = true;
      this.dialogMenu = false;

      this.codemirrorOptions.readOnly = true;
      this.codemirrorContent = JSON.stringify(
        this.$refs.designer.getRule(),
        null,
        2
      );
    },
    // 导入表单JSON
    handleUploadRule() {
      this.dialogTitle = "导入表单规则";
      this.dialogState = true;
      this.dialogMenu = true;

      this.codemirrorOptions.readOnly = false;
      this.codemirrorContent = JSON.stringify([], null, 2);
    },
    handlePrew() {
      this.prewOPen = true;
      // this.formCreateRule = this.formParams.formInfo[1];
      (this.formCreateRule = this.$refs.designer.getRule()),
        this.formShow.map((item) => {
          if (item.required == false) {
            this.prewRules[item.defaultName][0].required = false;
          } else {
            this.prewRules[item.defaultName][0].required = true;
          }
        });
    },
    handleImport() {
      try {
        let content = JSON.parse(this.codemirrorContent);
        if (this.dialogTitle == "导入表单规则") {
          this.$refs.designer.setRule(content);
        }
        if (this.dialogTitle == "导入表单配置") {
          this.$refs.designer.setOptions(content);
        }
        this.dialogState = false;
      } catch (e) {
        this.$message.error("输入内容格式有误");
      }
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["formParams"].validate((valid) => {
        if (valid) {
          this.btnloading=true
          let formRules = this.$refs.designer.getRule();
          if (formRules.length > 0) {
            let custom = [
              "userType",
              "name",
              "sex",
              "nationality",
              "province",
              "city",
              "phone",
              "idCard",
              "email",
              "company",
              "job",
              "cardType",
            ];
            let flag = false;
            formRules.map((item, index) => {
              if (custom.indexOf(item.field) > -1) {
                flag = true;
                this.$message.error(
                  "表单组件:'" +
                    item.title +
                    "'的字段 ID与静态内容有重复,请修改后提交"
                );
              } else {
                if (index === formRules.length - 1 && flag === false) {
                  for (var i = 0; i < formRules.length - 1; i++) {
                    for (var j = i + 1; j < formRules.length; j++) {
                      if (formRules[i].field === formRules[j].field) {
                        this.$message.error(
                          "表单组件:'" +
                            formRules[i].title +
                            "'的 字段ID与'" +
                            formRules[j].title +
                            "'的字段ID重复,请重新输入"
                        );

                        return false;
                      }
                    }
                  }
                }
              }
            });
          }
          if(formRules.length>0){
            formRules.map((item)=>{
              if(item.type=="select"){
                if(item.options&&item.options.length>0){
                  item.options.map((val)=>{
                    val.value=val.label
                  })
                }
              }
            })
          }
          this.formParams.formInfo[0] = this.formShow;
          this.formParams.formInfo[1] = formRules;
          let url = "";
          if (this.formParams.id && this.formParams.id != "") {
            url = "/form/update";
          } else {
            url = "/form/save";
          }
          request({
            url: url,
            method: "post",
            data: this.formParams,
          })
            .then((response) => {
              if (response.code == 200) {
                this.$message.success("操作成功");
                this.$router.push("/activity/post");
                this.btnloading=false
              }
            })
            .catch((err) => {
              console.log(err);
               this.btnloading=false
            });
          // }
        }
      });
    },
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除"' + row.activityname + '"的表单配置？')
        .then(() => {
          request({
            url: "/form/delete/" + row.id,
            method: "get",
          })
            .then((response) => {
              if (response.code == 200) {
                this.$message.success("操作成功");
                this.handleQuery();
              }
            })
            .catch((err) => {
              console.log(err);
            });
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep ._fc-designer .el-aside {
  padding: 0 !important;
  background: #fff !important;
}
aside {
  padding: 0;
  background: #fff;
}
.el-dialog__body {
  padding: 10px;
}
.el-tooltip__popper.is-dark {
  min-width: 10px !important;
  max-width: 300px !important;
}
.el-divider--horizontal {
  margin: 10px 0;
}
.formInfo .el-form-item--small.el-form-item {
  margin-bottom: 0;
}
::v-deep ._fc-m-tools .is-round {
  display: none !important;
}
.cutsome {
  display: flex;
  justify-content: center;
}
.cutsome .el-button--mini {
  width: 100px;
  display: block;
  margin: 10px 5px;
}
::v-deep .form-create .el-form-item {
  margin-bottom: 10px;
}
::v-deep .form-create .el-select {
  width: 100%;
}
::v-deep .form-create .el-date-editor {
  width: 100%;
}
</style>
