Contributing
==============

If you find an issue, submitting a pull request is always better than a bug report! Please fork and submit your code fixes.

If you want to build some new features, we have a [roadmap.md](docs/roadmap.md) of features we want. You can add features you want there, or just code the feature and send a pull request.

### Cloning

```sh
$ git clone https://github.com/zeroclipboard/zeroclipboard.git
$ cd zeroclipboard/
$ npm install -g grunt-cli
$ npm install
$ grunt
```

### Developing

```sh
$ npm install
$ grunt
```

### Testing

```sh
$ grunt test
```
