<template>
  <div id="steps_1">
    <el-form
      :model="stepsForm"
      ref="stepsForm"
      label-width="80px"
      :rules="rules"
      :inline="true"
    >
      <p>公司信息</p>
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="stepsForm.companyName"
          placeholder="请输入公司名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="公司性质" prop="companyType">
        <!-- <el-input v-model="stepsForm.companyType"></el-input> -->
        <el-select v-model="stepsForm.companyType" placeholder="请选择公司性质">
          <el-option
            v-for="(item, idx) in selectList"
            :key="idx"
            :value="item.dictValue"
            :lalel="item.dictLabel"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="公司地址" prop="companyAddress">
        <el-input
          v-model="stepsForm.companyAddress"
          placeholder="请输入公司地址"
        ></el-input>
      </el-form-item>
      <el-form-item label="公司网站" prop="companyPortal">
        <el-input
          v-model="stepsForm.companyPortal"
          placeholder="请输入公司网站"
        ></el-input>
      </el-form-item>
      <el-form-item label="意向面积" prop="areaSize">
        <el-input
          v-model.number="stepsForm.areaSize"
          placeholder="请输入意向面积"
        >
          <template slot="append">㎡</template>
        </el-input>
      </el-form-item>
      <el-form-item label="主营业务" prop="mainBusiness">
        <el-input
          v-model="stepsForm.mainBusiness"
          placeholder="请输入主营业务"
        ></el-input>
      </el-form-item>
      <p>联系人信息</p>
      <el-form-item label="姓名" prop="contactName">
        <el-input
          v-model="stepsForm.contactName"
          placeholder="请输入姓名"
        ></el-input>
      </el-form-item>
      <el-form-item label="部门" prop="contactDept">
        <el-input
          v-model="stepsForm.contactDept"
          placeholder="请输入部门"
        ></el-input>
      </el-form-item>
      <el-form-item label="职务" prop="contactDuties">
        <el-input
          v-model="stepsForm.contactDuties"
          placeholder="请输入职务"
        ></el-input>
      </el-form-item>
      <el-form-item label="手机" prop="contactPhone">
        <el-input
          v-model="stepsForm.contactPhone"
          placeholder="请输入手机号"
        ></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="contactEmail">
        <el-input
          v-model="stepsForm.contactEmail"
          placeholder="请输入邮箱"
        ></el-input>
      </el-form-item>
      <el-form-item label="传真" prop="contactFax">
        <el-input
          v-model="stepsForm.contactFax"
          placeholder="请输入传真"
        ></el-input>
      </el-form-item>
    </el-form>
    <div class="btn">
      <el-button
        type="primary"
        @click="submitForm('stepsForm')"
        :disabled="
          !(steps1.approveStatus == '审批驳回' || steps1 == '') ||
          activeInfo.activityStatus == 3
        "
        >提交</el-button
      >
      <span
        v-if="activeInfo.activityStatus == 3"
        style="font-size: 12px; margin-left: 12px; color: rgba(255, 0, 26)"
        >*活动已结束无法提交</span
      >
      <span
        v-else-if="!(steps1.approveStatus == '审批驳回' || steps1 == '')"
        style="font-size: 12px; margin-left: 12px; color: rgba(255, 0, 26)"
        >*内容正在审核中或审核已通过无法再次提交</span
      >
    </div>
  </div>
</template>
  
<script>
import request from "@/utils/request";
import { checkPrice } from "@/utils/validate";
export default {
  name: "steps_1",
  props: {
    steps1: {
      type: String,
    },
    activeInfo: {
      type: Object,
    },
  },
  data() {
    return {
      rules: {
        companyName: [{ required: true, message: "公司名称不能为空" }],
        companyType: [{ required: true, message: "公司组织性质不能为空" }],
        contactName: [{ required: true, message: "联系人姓名不能为空" }],
        contactDept: [{ required: true, message: "联系人部门不能为空" }],
        contactDuties: [{ required: true, message: "联系人职务不能为空" }],
        contactPhone: [
          { required: true, message: "联系人手机不能为空" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请填写正确的手机号",
          },
        ],
        areaSize: [
          { required: true, message: "请填写意向面积" },
          { validator: checkPrice, trigger: "blur" },
        ],
        contactEmail: [
          { required: true, message: "联系人邮箱不能为空" },
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],
      },
      stepsForm: {
        activityId: localStorage.getItem("activityNo"),
        applyUser: JSON.parse(localStorage.getItem("userInfo")).login,
        // 公司信息
        companyName: "",
        companyType: "",
        companyAddress: "",
        companyPortal: "",
        areaSize: "",
        mainBusiness: "",
        // 联系人信息
        contactName: "",
        contactDept: "",
        contactDuties: "",
        contactPhone: "",
        contactEmail: "",
        contactFax: "",
      },
      selectList: [],
      interval: null,
    };
  },
  created() {
    if (
      localStorage.getItem("activityNo") &&
      localStorage.getItem("activityNo") != ""
    ) {
      if (this.interval) {
        clearInterval(this.interval);
        this.interval = null;
      }
      if (this.steps1 != "") {
        this.stepsForm = this.steps1;
      }
      setTimeout(() => {
        this.getSelect();
      }, 500);
    } else {
      this.interval = setInterval(() => {
        this.activityNo = localStorage.getItem("activityNo");
      }, 100);
    }
  },
  destroyed() {
    clearInterval(this.interval);
    this.interval = null;
  },
  methods: {
    getSelect() {
      request({
        url: "/display/business/companyType",
        method: "get",
      }).then((res) => {
        this.selectList = res.data;
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.postAxios();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    postAxios() {
      let url = "";
      if (this.stepsForm.id && this.stepsForm.id != "") {
        delete this.stepsForm.isValid;
      } else {
      }

      url = "/display/business/add";
      request({
        url: url,
        data: this.stepsForm,
        method: "post",
      }).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: "提交成功",
            type: "success",
          });
          //   this.$parent.getData();
          this.$emit("getData");
        } else {
          this.$message({
            message: "提交失败",
            type: "warning",
          });
        }
      });
    },
  },
};
</script>
  
<style lang="scss" scoped>
#steps_1 {
  width: 100%;

  p {
    font-size: 16px;
    font-weight: 600;
  }

  ::v-deep .el-form {
    display: flex;
    // justify-content: space-around;
    justify-content: space-between;
    align-items: center;
    flex-flow: wrap;

    p {
      width: 100%;
      color: #606266;
      font-size: 14px;
    }
    .el-input--medium {
      width: 330px;
    }
    .el-form-item {
      //   width: 50%;
    }

    .el-form-item__label {
      //   width: 110px !important;
    }

    .el-input {
      //   width: 97%;
    }
  }
  .btn {
    width: 100%;
    //   margin-left: 0;
    // margin-top: 80px;
    text-align: center;
  }
}
</style>
  