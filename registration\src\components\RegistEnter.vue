<template>
  <div class="registEnter">
      <div class="registBaner">{{ activeMsg ? activeMsg.activityName:"" }}</div>
      <div style="position: relative;top: -70px; width: 80%;margin: auto;text-align: center;">

        <div class="intrMsg" style="margin-bottom: 20px; font-size: 14px;">{{ activeMsg ? activeMsg.activityDescription:"" }}</div>

        <div class="intrMsg" style="text-align: center;line-height: 25px;">
          <div style="font-weight: bold;font-size: 16px;">活动时间</div>
          <div style="font-size: 14px;">{{ activeMsg ? activeMsg.startDate+" - "+activeMsg.endDate : "" }}</div>
        </div>

        <div class="registerNow">
          <el-button type="info" size="large" @click="registerNow(1)" style="width: 100%;padding: 15px 20px;">立即报名</el-button><br>
          <el-link type="primary" @click="registerNow(2)" style="margin-top: 10px;">报名查询</el-link>
        </div>

      </div>
  </div>
</template>

<script>
import { access_token,getActityMsg} from '@/utils/apiManage'
import{getAccesToken,setAccesToken} from '@/utils/auth'
import{REDIRECT_URL} from '@/utils/request.js'
import moment from 'moment'
moment.locale("zh-cn"); 
export default {
  name: 'RegistEnter',
  props: {
  },
  data(){
    return {
      activeMsg:null
    }
  },
  mounted(){
    this.getActityData()
    let code=window.location.href.split("=").length>=2?window.location.href.split("=")[1]:null
    if((!getAccesToken()) && code){
      this.accessToken({code:code})
    }
  },
  methods:{
    
    registerNow(type){
      if(getAccesToken()){
        if(type === 1){
          localStorage.setItem("redirectType",1)
          this.$router.replace({ path: '/UserRegist'})
          
        }else{
          localStorage.setItem("redirectType",3)
          this.$router.replace({ path: '/RegistView'})

        }
      }else{
        this.$alert('您未登录，现在去登录？', '系统提示', {
          confirmButtonText: '确定',
          callback: action => {
            window.location.href= REDIRECT_URL
          }
        });
      }
    },
    
    accessToken(data){//获取accesToken
      return access_token(data).then(res => {
        if(res.accessToken){
          setAccesToken(res.accessToken)
          this.$message({
              type: 'success',
              message: `登录成功！`,
              offset:60,
            });
          this.getActityData()
        }
      })
    },
    getActityData(){//获取活动信息
      return getActityMsg({"activityNo":"HD202309260101"}).then(res => {
        if(res.code === 200 && res.data){
          res.data.startDate = moment(res.data.startDate).format('ll')
          res.data.endDate = moment(res.data.endDate).format('ll')
          this.activeMsg = res.data
        }else{
          this.activeMsg = null
        }
      })
    },


  },


}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
h3 {
  margin: 40px 0 0;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
.registEnter{
  margin: 0px;
  padding: 0px;
}
.registBaner{
  height: 140PX;
  width: 100%;
  color: #fff;
  padding-top:110px;
  background-color: #424242;
}
.intrMsg{
  color: #535353;
  min-height: 50px;
  margin: auto;
  padding: 15px 20px;
  word-break:break-all;
  border:1px solid #cccccc;
  border-radius: 15px;
  background-color: #fff;
  text-align: left;
  
}
.registerNow{
  padding-top: 20px;
}
</style>
