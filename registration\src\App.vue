<template>
  <div id="app" v-if="isShow" style="min-width: 120px;">
    <router-view/>
    <div style="position:fixed;right:10px;bottom:100px" v-if="contactData">
      <el-popover
        placement="left"
        width="280"
        trigger="hover">

        <div>
          <div style="line-height: 32px;">
            <el-image
              :src="customerService"
              fit="cover"
              style="float: left;width: 40px;"
              />
            <div style="line-height: 30px; height: 30px; float: left;padding-left: 10px;">联系客服</div>
            <div style="clear: both;"></div>
          </div>
          <div v-for="item in contactData" v-if="item.dictValue">{{ item.dictLabel+":"+item.dictValue }}</div>
        </div>

        <div slot="reference" style="text-align: center;margin: auto;width: 100%;">
          <el-avatar icon="el-icon-headset"  style="background:#1677ff;"></el-avatar>
          <div>联系客服</div>
        </div>

      </el-popover>
    </div>
  </div>
</template>

<script>
import { getActityMsg,getatom,access_token,contactInfo } from '@/utils/apiManage'
import {getAccesToken,setAccesToken} from '@/utils/auth'
import NavMenu from '@/components/NavMenu/index.vue'
import customerService from '@/assets/customerService.svg'
import moment from 'moment'
moment.locale("zh-cn");
export default {
  name: 'App',
  components:{
    NavMenu
  },
  data(){
    return {
      isShow:false,
      contactData:null,
      customerService
    }
  },
  mounted(){
    localStorage.removeItem("code")
    if( this.contactData === null  || !this.contactData || (this.contactData && this.contactData.length<=0)){
      this.getContactInfo()
    }

    if (window.location.href.indexOf('/mobile/auth/login?code=') > -1) {
      let code = window.location.href.split("=").length >= 2 ? window.location.href.split("=")[1] : null
      if ((!getAccesToken()) && code) {
        this.accessToken({code: code})
      } else {
        this.getActityData()
      }
    } else {
      const queryString = window.location.search;

      const urlParams = new URLSearchParams(queryString);

      if(urlParams.get("activityNo")){
        localStorage.setItem("activityNo",urlParams.get("activityNo"))
      }
      if(urlParams.get("code")){
        localStorage.setItem("channelCode",urlParams.get("code"))
      }
      this.getActityData()
    }

    if(!localStorage.getItem("userInfo") && getAccesToken()){
      this.getUser()
    }
  },
  methods:{
    accessToken(data){//获取accesToken
      return access_token(data).then(res => {
        if(res.accessToken){
          setAccesToken(res.accessToken)
          this.$message({
            message: '登录成功',
            type: 'success',
            offset:60
          });
          this.getActityData()
          this.getUser()
        }else{
          this.$message({
            message: JSON.stringify(res),
            offset:60,
            type: 'warning'
          });
        }
      })
    },
    getContactInfo(){//获取accesToken
      return contactInfo().then(res => {
        if(res.code === 200 && res.data){
          this.contactData = res.data
        }
      })
    },
    getUser(){
      return getatom().then(res => {
        if(res){
          localStorage.setItem("userInfo", JSON.stringify(res))
        }else{
          this.$message({
            message: JSON.stringify(res),
            type: 'warning',
            offset:60,
          });
        }

      })
    },
    getActityData(){
      if(localStorage.getItem("activityNo")){
        this.isShow=false
        return getActityMsg({"activityId":localStorage.getItem("activityNo")}).then(res => {
          this.isShow=true
          if(res.code === 200 && res.data){
            res.data.startDate = moment(res.data.startDate).format('ll')
            res.data.endDate = moment(res.data.endDate).format('ll')
            this.activeMsg = res.data
            localStorage.setItem("activity",JSON.stringify(res.data))
          }else{
            this.activeMsg = null
            this.$message({
              message: JSON.stringify(res),
              type: 'warning',
              offset:60,
            });
          }
        })
      }else{
        this.$message({
          message: '活动编号不存在,请从报名入口重新进入!',
          type: 'warning',
          offset:60,
        });
      }
    },

  }
}
</script>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}
body{
  background-color: #f3f8ff !important;
}
.calback{
  margin: 0px 0px 15px 0px;
  text-align: left;
}
a{
  color: #1281FF;
}
</style>
