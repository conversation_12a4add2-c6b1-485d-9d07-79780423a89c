<template>
  <div class="chart-content">
    <div class="top-chart"></div>
    <el-table size="mini" :data="tableData">
      <el-table-column label="已签到人数" prop="sign"></el-table-column>
      <el-table-column label="未签到人数" prop="noSign"></el-table-column>
      <el-table-column label="总人数" prop="total"></el-table-column>
    </el-table>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { querySignChart } from "@/api/statistical/index";
export default {
  props: {},
  components: {},
  data() {
    return {
      chart: null,
      tableData: [],
      activityId: "",
      chartData: null,
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      option: {
        title: {
          text: "签到统计(报名通过)",
          left: "top",
        },
        // tooltip: {
        //   trigger: 'item'

        // },
        tooltip: {
          trigger: "item",
          formatter: "{b}: {c}人 ({d}%)",
        },
        legend: {
          top: "5%",
          left: "center",
        },
        series: [
          {
            // name: '签到统计',
            type: "pie",
            radius: ["40%", "50%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2,
            },
            label: {
              show: true,
              formatter: function (params) {
                var name = params.name;
                var percent = params.value || 0;
                return name + "\n" + percent + "人";
              },
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 15,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: [],
          },
        ],
      },
    };
  },
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activityId = activeInfo.activityId;
  },
  mounted() {
    this.initChart();
    
  },
  methods: {
    async initChart() {
      let res = await querySignChart(this.activityId);

      this.chartData = res.data;
      this.tableData = [res.data];
      this.drawChart();
    },
    drawChart() {
      this.chart = echarts.init(
        document.getElementsByClassName("top-chart")[2]
      );
      this.option.series[0].data = [
        { value: this.chartData.sign, name: "已签到" },
        { value: this.chartData.noSign, name: "未签到" },
        // { value: this.chartData.total, name: "所有" },
      ];
      this.chart.setOption(this.option);
    //    window.onresize = () => {
    //   this.chart.resize();
    // };
    },
  },
};
</script>
<style lang='scss'></style>
