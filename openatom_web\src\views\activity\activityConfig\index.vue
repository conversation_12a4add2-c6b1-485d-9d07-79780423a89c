<template>
  <div class="app-container" style="width: 80%; position: relative;left: 6%;">
    <div style="position: absolute; right: 100px;top:-10px">
      <el-button type="primary" @click="callbackHome">返回</el-button>
    </div>

    <div>

      <div class="activityMainMsg">
        <div style="float: left; color: #606266;font-weight: 700;">
          <span>
            活动名称：{{ this.activityMsg.activityName }}<br/>
          </span>
          <span>
            活动周期：{{ this.activityMsg.startDate +" ~ "+this.activityMsg.endDate }}
          </span>
          <span style="padding-left: 100px;">
            活动地点：{{ this.activityMsg.activityPlace }}
          </span>
        </div>
        <div style="float: right; margin-top: 15px;">
          <el-button type="warning" icon="el-icon-plus">发布</el-button>
          <el-button type="warning" icon="el-icon-video-pause">结束</el-button>
        </div>
        <div style="clear: both;"></div>
      </div>

      <div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="grid-content bg-green">
              <div class="titleStat">报名统计</div>
              <div class="remarkStat"><i class="el-icon-user" /> 总报名人数：1000</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content bg-yellow">
              <div class="titleStat">人员来源统计</div>
              <div class="remarkStat">移动端报名人数：88 <br/>
                PC端报名人数：88<br/> 批量导入人数：888</div>
              
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content bg-red">
              <div class="titleStat">签到统计</div>
              <div class="remarkStat"><i class="el-icon-s-custom" /> 总签到人数：1000</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content bg-gray">
              <div class="titleStat">票款统计</div>
              <div class="remarkStat"><i class="el-icon-money" /> 1000</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <div class="grid-content" style="color: #000; padding: 10px 20px;">
              <div class="titleIcon"><i class="el-icon-date"  /></div>
              <div class="remarkIcon"> 活动前准备</div>
              <div class="activityMenu">
                <el-collapse accordion>
                  <el-collapse-item>
                    <div slot="title" @click="menuShow">
                     活动基本信息<i class="header-icon el-icon-info"></i>
                    </div>
                    <div>说明：活动基本信息查看&修改</div>
                  </el-collapse-item>
                  <el-collapse-item title="分配管理员">
                    <div>分配管理员</div>
                  </el-collapse-item>
                  <el-collapse-item title="报名表单设置">
                    <div>报名表单设置</div>
                  </el-collapse-item>
                  <el-collapse-item title="隐私设置">
                    <div>隐私设置</div>
                  </el-collapse-item>
                  <el-collapse-item title="票务设置">
                    <div>票务设置</div>
                  </el-collapse-item>
                  <el-collapse-item title="优惠设置">
                    <div>优惠设置</div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content" style="color: #000;">
              <div class="titleIcon"><i class="el-icon-s-opportunity"  /></div>
              <div class="remarkIcon"> 活动推广</div>
              <div class="activityMenu">
                <el-collapse accordion>
                  <el-collapse-item>
                    <template slot="title">
                     活动宣传<i class="header-icon el-icon-info"></i>
                    </template>
                    <div>活动宣传</div>
                  </el-collapse-item>
                  <el-collapse-item title="活动邀请">
                    <div>活动邀请</div>
                  </el-collapse-item>
                  <el-collapse-item title="提醒管理">
                    <div>提醒管理</div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content" style="color: #000;">
              <div class="titleIcon"><i class="el-icon-s-claim"  /></div>
              <div class="remarkIcon"> 活动过程</div>
              <div class="activityMenu">
                <el-collapse accordion>
                  <el-collapse-item>
                    <template slot="title">
                     报名人员审批<i class="header-icon el-icon-info"></i>
                    </template>
                    <div>报名人员审批</div>
                  </el-collapse-item>
                  <el-collapse-item title="报名人员管理">
                    <div>报名人员管理</div>
                  </el-collapse-item>
                  <el-collapse-item title="订单管理">
                    <div>订单管理</div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content" style="color: #000;">
              <div class="titleIcon"><i class="el-icon-s-data"  /></div>
              <div class="remarkIcon"> 统计分析</div>
              <div class="activityMenu">
                <el-collapse accordion>
                  <el-collapse-item>
                    <template slot="title">
                     报名统计<i class="header-icon el-icon-info"></i>
                    </template>
                    <div>报名统计</div>
                  </el-collapse-item>
                  <el-collapse-item title="人员来源统计">
                    <div>人员来源统计</div>
                  </el-collapse-item>
                  <el-collapse-item title="签到统计">
                    <div>签到统计</div>
                  </el-collapse-item>
                  <el-collapse-item title="票款统计">
                    <div>票款统计</div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-col>
          
        </el-row>
      </div>
    </div>


  </div>
</template>

<script>
import request from "@/utils/request";
import { constantRoutes } from "@/router";
import {
  listPost,
  getPost,
  delPost,
  addPost,
  updatePost,
} from "@/api/system/post";
export default {
  name: "Index",
  data() {
    return {
      activityMsg:{}
    };
  },
  components: {},
  created() {
    this.activityMsg= JSON.parse(localStorage.getItem("activity"))
    this.getPostType();
  },
  computed: {
    
  },
  methods: {
    callbackHome(){
      this.$emit("callbackHome")
    },
    menuShow(){
      alert(1)
    },
    // 菜单选择事件
    handleSelect(activity) {

      localStorage.setItem("activity",activity)
      this.showType = 2
      
    },
      // 当前激活的路由
    activeRoutes(key) {
      var routes = [];
      if (this.childrenMenus && this.childrenMenus.length > 0) {
        this.childrenMenus.map((item) => {
          if (key == item.parentPath || (key == "index" && "" == item.path)) {
            routes.push(item);
          }
        });
      }
      if(routes.length > 0) {
        // window.location.href="http://atomgitactivity.com/atomgit/activity/post"
         this.$router.push({ path: routes[0].path});
          // window.location.reload()
        this.$store.commit("SET_SIDEBAR_ROUTERS", routes);
      } else {
        this.$store.dispatch('app/toggleSideBarHide', true);
      }
    },
     ishttp(url) {
      return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1
    },
    getPostType() {
      request({
        url: "/system/dict/data/list?pageNum=1&pageSize=100&dictType=activity_status&status=0",
        method: "get",
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            this.postTypeOption = response.rows;
            this.queryParams.activityStatus = response.rows[0].dictValue;
            this.getList();
          } else {
            this.getList();
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getList() {
      this.loading = true;
      let param = JSON.parse(JSON.stringify(this.queryParams));
      param.activityStatus = Number(this.queryParams.activityStatus);
      request({
        url: "/activityManage/list",
        method: "post",
        data: param,
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            this.postList = response.rows;
            this.total = response.total;
            this.loading = false;
          } else {
            this.postList = [];
            this.total = 0;
            this.loading = false;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleClick() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.queryParams.searchParam = undefined;
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      this.form = {};
      this.open = true;
      this.title = "添加活动";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.startDate = this.form.stratEndTime[0];
          this.form.endDate = this.form.stratEndTime[1];
          if (this.form.activityId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    submitFlowForm() {
      this.$refs["flowform"].validate((valid) => {
        if (valid) {
          request({
            url: "/activityManage/saveActivityFlow",
            method: "post",
            data: this.flowform,
          })
            .then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.flowOpen = false;
              this.getList();
            })
            .catch((err) => {
              console.log(err);
            });
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.form = {};
      // this.reset();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      debugger;
      const postIds = row.postId;
      let obj = {};
      obj.id = row.id;
      obj.activityId = row.activityId;
      obj.isValidate = 0;
      this.$modal
        .confirm('是否确认删除活动编号为"' + row.activityId + '"的数据？')
        .then(function () {
          return delPost(obj);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss">
.app-container {
  font-size: 14px;
}
.activityMainMsg{
  line-height: 30px;
  font-size: 16px;
  margin: 10px 0px;
}
.el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-col {
    border-radius: 4px;
  }
  .bg-purple-dark {
    background: #99a9bf;
  }
  .bg-green {
    background: #67C23A;
  }
  .bg-yellow {
    background: #E6A23C;
  }
  .bg-red {
    background: #F56C6C;
  }
  .bg-gray {
    background: #909399;
  }
  .bg-purple-light {
    background: #e5e9f2;
  }
  .grid-content {
    border-radius: 4px;
    min-height: 36px;
    color: #ffffff;
    padding: 10px;
    height: 113px;
  }
  .row-bg {
    padding: 10px 0;
    background-color: #f9fafc;
  }
  .titleStat{
    font-size: 16px;
    font-weight: 500;
  }
  .remarkStat{
    line-height: 24px;
  }
  .titleIcon{
    font-size: 80px;
    margin: auto;
    text-align: center;
  }
  .remarkIcon{
    font-weight: 700;
    font-size: 16px;
    margin: auto;
    text-align: center;
  }
  .activityMenu{
    width: 80%;
    margin-left:10% ;
    padding-top: 10px;
  }
</style>


<style scoped lang="scss">
</style>

