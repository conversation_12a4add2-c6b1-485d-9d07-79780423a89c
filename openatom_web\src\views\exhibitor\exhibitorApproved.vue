<template>
  <div class="app-containers">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输人员姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="姓名" prop="contactName">
        <el-input
          v-model="queryParams.contactName"
          placeholder="请输人员姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="手机号" prop="contactPhone">
        <el-input
          v-model="queryParams.contactPhone"
          placeholder="请输人员手机号"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="
        approvalList.slice(
          (queryParams.pageNum - 1) * queryParams.pageSize,
          queryParams.pageNum * queryParams.pageSize
        )
      "
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="50" align="center" /> -->
      <el-table-column label="序号" align="center" prop="" type="index" />
      <el-table-column
        prop="applyUser"
        label="申请人"
        width="100px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="展商名称"
        align="center"
        prop="businessCompanyName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="展商介绍"
        align="center"
        prop="businessDesc"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="联系人"
        align="center"
        prop="contactName"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        label="联系电话"
        align="center"
        prop="contactPhone"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="approveStatus"
        label="审核状态"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="申请时间"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="100"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      style="margin-top: 10px"
      align="right"
      v-show="approvalList.length > 0"
      :total="approvalList.length"
      :current-page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="10"
      layout="total, sizes, prev, pager,next"
      @size-change="handleSizeChangeSys"
      @current-change="handleCurrentChangeSys"
    />
    <el-dialog
      custom-class="selfDialog"
      :title="'查看'"
      :visible.sync="UserOpen"
      width="800px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-collapse v-model="activepanel">
        <el-collapse-item title="审批信息" name="useInfo">
          <div id="self" style="height: 300px; overflow: auto">
            <el-form label-width="100px">
              <el-form-item label="展商名称" prop="businessCompanyName">
                <span>{{ stepsForm.businessCompanyName }}</span>
              </el-form-item>
              <el-form-item label="展商介绍:" prop="businessDesc">
                <span>{{ stepsForm.businessDesc }}</span>
              </el-form-item>
              <el-form-item label="联系人:" prop="contactName">
                <span>{{ stepsForm.contactName }}</span>
              </el-form-item>
              <el-form-item label="联系电话:" prop="contactPhone">
                <span>{{ stepsForm.contactPhone }}</span>
              </el-form-item>
              <el-form-item label="展品:" prop="productDetails" class="content">
                <div
                  v-for="(item, idx) in stepsForm.productDetails"
                  :key="idx"
                  style="
                    display: flex;
                    width: 150px;
                    flex-wrap: wrap;
                    margin: 5px;
                  "
                >
                  <img
                    :src="item.image_url"
                    style="width: 150px; height: 150px"
                  />
                  <span
                    :tltle="item.name"
                    style="
                      text-overflow: ellipsis;
                      overflow: hidden;
                      display: block;
                      white-space: nowrap;
                    "
                    >{{ item.name }}</span
                  >
                </div>
              </el-form-item>
              <el-form-item label="工作人员:">
                <el-table
                  :data="stepsForm.workPerson"
                  style="width: 98%"
                  size="small"
                >
                  <el-table-column label="姓名">
                    <template slot-scope="scope">
                      <el-form-item
                        :prop="'workPerson.' + scope.$index + '.name'"
                      >
                        <span>{{ scope.row.name }}</span>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column label="岗位">
                    <template slot-scope="scope">
                      <el-form-item
                        :prop="'workPerson.' + scope.$index + '.position'"
                      >
                        <span>{{ scope.row.position }}</span>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column label="电话">
                    <template slot-scope="scope">
                      <el-form-item
                        :prop="'workPerson.' + scope.$index + '.phone'"
                      >
                        <span>{{ scope.row.phone }}</span>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column label="邮箱">
                    <template slot-scope="scope">
                      <el-form-item
                        :prop="'workPerson.' + scope.$index + '.email'"
                      >
                        <span>{{ scope.row.email }}</span>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-form>
          </div>
        </el-collapse-item>
        <el-collapse-item title="审批流程" name="approval">
          <el-table v-loading="apploading" :data="waitList">
            <el-table-column
              label="序号"
              align="center"
              prop="key"
              type="index"
              :show-overflow-tooltip="true"
              width="100px"
            />
            <el-table-column
              label="审批人"
              align="center"
              prop="oper_user"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="操作"
              align="center"
              prop="oper_info"
              :show-overflow-tooltip="true"
            >
            </el-table-column>

            <el-table-column
              label="说明"
              align="center"
              prop="remarks"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              label="时间"
              align="center"
              prop="oper_date"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
<script>
import request from "@/utils/request";
import formCreate from "@form-create/iview";
export default {
  name: "haveApproval",
  data() {
    return {
      batchrule: {
        action: [
          { required: true, message: "请选择审批结果", trigger: "blur" },
        ],
        remarks: [
          { required: false, message: "请填写审批意见", trigger: "blur" },
        ],
      },
      btnloading: false,
      batchform: {},
      activeName: "first",
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: undefined,
      },
      approvalList: [],
      loading: false,
      total: 0,
      UserOpen: false,
      activepanel: "useInfo",
      formParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: undefined,
      },
      flowform: {},
      option: {
        form: { labelPosition: "top" },
        submitBtn: { show: false },
      },
      appform: {},
      apprule: {
        action: [
          { required: true, message: "请选择审批结果", trigger: "blur" },
        ],
        remarks: [
          { required: false, message: "请填写审批意见", trigger: "blur" },
        ],
      },
      waitList: [],
      apploading: false,
      stepsForm: {},
      selected: [],
      batchOpen: false,
      selectList: [],
    };
  },
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.queryParams.activityId = activeInfo.activityId;
    this.queryParams.active_Name = activeInfo.activityName;
    this.getSelect();
  },
  watch: {
    "queryParams.activityId": {
      handler(val, oldval) {
        if (val != oldval) {
          this.getList();
          this.queryParams.activityId = val;
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleCurrentChangeSys(val) {
      this.queryParams.pageNum = val;
    },
    // 当当前页改变
    handleSizeChangeSys(val) {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = val;
    },
    getSelect() {
      const data = {
        activityId: this.queryParams.activityId,
        status: "0",
      };
      (this.queryParams.approveStatus = "已审批"),
        request({
          url: "/display/position/manage/list",
          method: "get",
          params: data,
        })
          .then((response) => {
            this.selectList = response.data;
            this.loading = false;
          })
          .catch((err) => {
            console.log(err);
          });
    },
    handleSelectionChange(selection) {
      this.selected = [...selection];
    },
    getList() {
      (this.queryParams.approveStatus = "已审批"),
        request({
          url: "/display/product/manage/list",
          method: "get",
          params: this.queryParams,
        })
          .then((response) => {
            this.approvalList = response.data;
            // this.total = response.total;
            this.loading = false;
          })
          .catch((err) => {
            console.log(err);
          });
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleUpdate(row) {
      this.stepsForm = row;
      if (typeof row.workPerson == "string") {
        this.stepsForm.workPerson = JSON.parse(row.workPerson);
      }
      if (typeof row.workPerson == "object") {
        this.stepsForm.workPerson = row.workPerson;
      }
      if (typeof row.productDetails == "string") {
        this.stepsForm.productDetails = JSON.parse(row.productDetails);
      }
      if (typeof row.productDetails == "object") {
        this.stepsForm.productDetails = row.productDetails;
      }
      this.UserOpen = true;
      this.getFlowList(row.busPk);
      this.appform.activityNo = row.activityId;
    },
    cancel() {
      this.UserOpen = false;
    },
    getFlowList(data) {
      this.apploading = true;
      request({
        url: "/flow/list?bus_pk=" + data,
        method: "get",
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            this.waitList = response.rows;
            this.appform.flowno = response.rows[0].flowno;
            this.appform.bus_pk = data;
          }
          this.apploading = false;
        })
        .catch((err) => {
          this.apploading = false;
          console.log(err);
        });
    },
  },
};
</script>
<style scoped rel="stylesheet/scss" lang="scss">
::v-deep .form-create button {
  display: none;
}

::v-deep #self .el-form-item {
  margin-bottom: 5px;
}

::v-deep .form-create .el-form-item__label {
  width: 100px !important;
}

// ::v-deep .form-create .el-form-item__content {
//   margin-left: 100px !important;
// }
// ::v-deep .form-create .el-input {
//   width: 78% !important;
// }
::v-deep .form-create .el-select {
  width: 100% !important;
}

.el-tooltip__popper.is-dark {
  min-width: 10px !important;
  max-width: 300px !important;
}

::v-deep .form-create .el-select {
  width: 100%;
}

::v-deep .form-create .el-date-editor {
  width: 100%;
}
::v-deep .content .el-form-item__content {
  display: flex;
  flex-wrap: wrap;
}
</style>