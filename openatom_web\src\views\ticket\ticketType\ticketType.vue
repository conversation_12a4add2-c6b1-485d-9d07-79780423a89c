<template>
  <div>
    <div class="tableBtnGroup">
      <el-button
        v-if="activetyInfo.isBuyTicket == 1"
        size="mini"
        type="warning"
        icon="el-icon-plus"
        @click="addTicketType"
        >新增</el-button
      >
    </div>
    <el-table ref="ticketTable" :data="tableData" :height="tableHight">
      <el-table-column
        :resizable="false"
        align="center"
        label="序号"
        width="50"
        type="index"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="name"
        label="门票名称"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="price"
        label="价格（元）"
      >
        <template v-slot="scope">
          <span>
            {{ scope.row.price === null ? "免费" : scope.row.price }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="activityPrice"
        label="活动价格（元）"
      >
        <template v-slot="scope">
          <span> {{ scope.row.activityPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="isActivityPrice"
        label="是否启用活动价"
      >
        <template v-slot="scope">
          <span> {{ scope.row.isActivityPrice === 0 ? "否" : "是" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="isSales"
        label="是否可售"
      >
        <template v-slot="scope">
          <span> {{ scope.row.isSales === 0 ? "不可售" : "可售" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="isShow"
        label="上架/下架"
      >
        <template v-slot="scope">
          <span> {{ scope.row.isShow === 0 ? "下架" : "上架" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="stock"
        label="数量"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="info"
        label="票种权益"
      />

      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="createTime"
        label="创建时间"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="updateTime"
        label="更新时间"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="sort"
        label="显示顺序"
      />
      <el-table-column
        :resizable="false"
        width="220"
        label="操作"
        v-if="activetyInfo.isBuyTicket == 1"
      >
        <template v-slot="scope">
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDeleteTicket(scope.row.id)"
            >删除</el-button
          > -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdateTicket(scope.row)"
            >修改</el-button
          >
          <el-button
            v-if="scope.row.isShow === 1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleOffTicket(scope.row.id)"
            >下架</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleOnTicket(scope.row.id)"
            >上架</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <AddTicketType
      ref="dialogForm"
      :showDialog="showDialog"
      :dialogTitle="dialogTitle"
      :activityId="activityId"
      :rowData="rowData"
      @cancelDialogFun="cancelDialogFun"
    />
  </div>
</template>
<script>
import ticketType, {
  addTicketType,
  removeTicketType,
} from "@/api/ticket/ticketType/index";
import AddTicketType from "@/views/ticket/ticketType/addTicketType";
export default {
  name: "TicketType",
  components: {
    AddTicketType,
  },
  data() {
    return {
      activetyInfo: JSON.parse(sessionStorage.getItem("activity")),
      activityId: JSON.parse(sessionStorage.getItem("activity")).activityId,
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      rowData: {},
      tableData: [],
      dialogTitle: "新增",
      tableHight: "534px",
      showDialog: false,
      multipleData: [],
    };
  },
  props: {},
  mounted() {
    this.init();
  },
  methods: {
    cancelDialogFun(data) {
      this.showDialog = data;
    },
    addTicketType() {
      this.showDialog = true;
      this.rowData = {
        id: "",
        name: "",
        price: null,
        stock: null,
        info: "",
        sort: 1,
        isShow: undefined,
        activityid: this.activityId,
        isSales: undefined,
        isActivityPrice: undefined,
      };
      this.dialogTitle = "新增";
    },
    init() {
      let obj = {
        activityid: this.activityId,
      };
      ticketType.getTicketTypeList(obj).then((res) => {
        if (res.code === 200) {
          this.tableData = res.rows;
        }
      });
    },
    handleDeleteTicket(id) {
      this.$confirm(`确定删除活动编码为${this.activityId}的数据？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(() => {
          ticketType.removeTicketType(id).then((res) => {
            if (res.code === 200) {
              setTimeout(() => {
                this.$message({
                  type: "success",
                  message: "删除成功",
                });
                this.init();
              }, 500);
            } else {
              this.$message({
                type: "error",
                message: res.msg,
              });
            }
          });
        })
        .catch(() => {
          return false;
        });
    },
    handleUpdateTicket(data) {
      this.showDialog = true;
      this.dialogTitle = "修改";
      this.rowData = {
        id: data.id,
        name: data.name,
        price: data.price,
        stock: data.stock,
        info: data.info,
        sort: data.sort,
        isShow: data.isShow,
        activityid: this.activityId,
        isSales: data.isSales,
        activityPrice: data.activityPrice,
        isActivityPrice: data.isActivityPrice,
      };
    },
    handleOffTicket(data) {
      this.$confirm(`确定下架活动编码为${this.activityId}的数据？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(() => {
          let obj = {
            id: data,
            isShow: 0,
          };
          ticketType.updateTicketType(obj).then((res) => {
            if (res.code === 200) {
              setTimeout(() => {
                this.$message({
                  type: "success",
                  message: "下架成功",
                });
                this.init();
              }, 500);
            } else {
              this.$message({
                type: "error",
                message: res.msg,
              });
            }
          });
        })
        .catch(() => {
          return false;
        });
    },
    handleOnTicket(data) {
      this.$confirm(`确定上架活动编码为${this.activityId}的数据？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(() => {
          let obj = {
            id: data,
            isShow: 1,
          };
          ticketType.updateTicketType(obj).then((res) => {
            if (res.code === 200) {
              setTimeout(() => {
                this.$message({
                  type: "success",
                  message: "上架成功",
                });
                this.init();
              }, 500);
            } else {
              this.$message({
                type: "error",
                message: res.msg,
              });
            }
          });
        })
        .catch(() => {
          return false;
        });
    },
  },
};
</script>

<style scoped lang="scss">
.tableBtnGroup {
  margin-bottom: 20px;
}
</style>

