{"name": "zeroclipboard", "description": "The ZeroClipboard library provides an easy way to copy text to the clipboard using an invisible Adobe Flash movie and a JavaScript interface.", "version": "2.2.0-beta.1", "main": ["./dist/ZeroClipboard.js", "./dist/ZeroClipboard.swf"], "keywords": ["flash", "clipboard", "copy", "cut", "paste", "zclip", "clip", "clippy"], "license": "https://github.com/zeroclipboard/zeroclipboard/blob/master/LICENSE", "authors": [{"name": "<PERSON>", "url": "http://jonrohan.me/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jamesgreene.net/"}], "homepage": "http://zeroclipboard.org/", "repository": {"type": "git", "url": "https://github.com/zeroclipboard/zeroclipboard.git"}, "location": "git://github.com/zeroclipboard/zeroclipboard.git", "ignore": ["*", "!/bower.json", "!/dist/**"]}