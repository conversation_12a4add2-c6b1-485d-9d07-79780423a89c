<template>
  <div class="registEnter" v-loading='isLoading'>
    <!-- 订单信息 -->
    <div v-if="showType === 1" class="productMsg">
      <div >
        <div style="margin:auto;text-align:left;padding:5px 10px">商品信息 ></div>
        <div style="border:1px solid #DCDFE6;border-radius:10px;height:110px;padding:10px;margin:5px 10px">

          <div class="productIcon" style="padding: 10px 0px;">
            <img src="@/assets/logo.svg" class="image"  >
            <div style="font-size:14px; font-weight:bold;">{{ orderMsg.ordersDetails[0].product.name }}</div>
          </div>

          <!-- <div class="productAttribute" v-if="orderMsg" style="border-left: 1px dashed #E3ECF8;padding-left:5px;height:80px"  @click="showOrder(order)">
            <div class="moneyShow" :style="orderMsg.ordersDetails[0].product.isActivityPrice === 1 ? 'line-height:50px' : 'line-height:80px'">
              <span style="font-size: 16px;" >{{orderMsg.ordersDetails[0].product.isActivityPrice === 1 ? "活动价¥" : "原价¥"}}</span>
              <span style="font-size: 22px; font-weight: bold;" v-if="orderMsg.ordersDetails[0].product.isActivityPrice === 1">{{ orderMsg.ordersDetails[0].product.activityPrice+"" }}</span>
              <span style="font-size: 22px; font-weight: bold;" v-else>{{ orderMsg.ordersDetails[0].product.price+""}}</span>
            </div>
            <div class="moneyShow" style="color:rgb(124, 123, 123);text-decoration:line-through;line-height: 30px" v-if="orderMsg.ordersDetails[0].product.isActivityPrice === 1">
              <span style="font-size: 12px;" >原价¥</span>
              <span style="font-size: 12px;">{{ orderMsg.ordersDetails[0].product.price }}</span>
            </div>
          </div> -->

          <!-- <div class="productAttribute" v-if="orderMsg" style="border-left: 1px dashed #E3ECF8;padding-left:5px;line-height: 80px;text-align: center;"  @click="showOrder(order)">
            <span v-if="orderMsg.orderStatus === '10' && !isExpire " style="color:red" >等待付款
              <PayTime v-if="orderMsg" :data="orderMsg" @payCallback="refrashOrderData" style="display: inline;"/>
            </span>
            <span v-if="isExpire">订单已过期</span>
            <span v-if="orderMsg.orderStatus === '20'" >已付款</span>
            <span v-if="orderMsg.orderStatus === '30'">已取消</span>
            <span v-if="orderMsg.orderStatus === '40'" >交易关闭</span>
          </div> -->
          <div class="productAttribute" v-if="orderMsg" style="border-left: 1px dashed #E3ECF8;padding-left:5px;line-height: 80px;text-align: center;"  @click="showOrder(order)">
            <div>数量*{{ orderMsg.ordersDetails[0].buyCounts }}</div>
          </div>
        </div>
        <div style="clear:both"></div>
      </div>

      <div class="orderDetail">
        <el-form style="padding:10px">
          <!-- <el-form-item
              label="商品总价"
            >
            <div style="margin:auto;text-align:right" v-if="orderMsg.ordersDetails[0].product.isActivityPrice === 1">
              {{ "¥"+orderMsg.ordersDetails[0].product.activityPrice }}
            </div>
            <div style="margin:auto;text-align:right" v-else>
              {{ "¥"+orderMsg.ordersDetails[0].product.price }}
            </div>
          </el-form-item> -->

          <el-form-item
              label="校验码"
              v-if="isCalc && orderMsg.ordersDetails[0].product.price>0"
            >
            <div style="margin:auto;text-align:right;clear: both;" >
              <div style="width:300px;float: right;">
                <el-checkbox v-model="isUsePreferential" @change="couponChange" disabled>是否使用校验码</el-checkbox>
                <el-input v-if="isUsePreferential" placeholder="请输入校验码" v-model="preferential" size="small"/>
                <el-button v-if="preferential && isUsePreferential " type="primary" size="small" @click="couponBlur">查询使用</el-button>
              </div>
            </div>
          </el-form-item>

          <!-- <el-form-item
            label="支付方式"
            :rules="{required:true, message: '请选择支付方式'} "
          >
            <div style="margin:auto;text-align:right">
              <el-radio-group v-model="payType" size="small">
                <el-radio label="1" style="width: 110px;margin-right: 10px;line-height: 32px; height: 32px;">
                  <el-image
                  style="width: 30px; height: 30px"
                  :src="wechat"
                  fit="fit"></el-image>微信支付
                </el-radio>
                <el-radio label="2" style="width: 110px;margin-right: 10px;line-height: 32px; height: 32px;">
                  <el-image
                    style="width: 30px; height: 30px"
                    :src="alipayIcon"
                    fit="fit"></el-image>支付宝支付
                </el-radio>
              </el-radio-group>

            </div>
          </el-form-item> -->
          <el-form-item
            label="订单编号"
          >
            <div style="margin:auto;text-align:right">
              {{ orderMsg.orderNum }}
            </div>
          </el-form-item>
          <el-form-item
            label="下单时间"
          >
            <div style="margin:auto;text-align:right">
              {{ orderMsg.createTime }}
            </div>
          </el-form-item>
        </el-form>

        <!-- <div class="payMoneyMsg">
          <span class="moneyShow" style=" font-size:16px;" v-if=" isUsePreferential && coupon">
            共减<span style="font-weight:bold;color: red;"> ¥{{ couponPrice ?couponPrice : 0 }}</span>
          </span>
          需支付<span style="font-weight:bold;color: red;font-size: 24px;"> ¥{{isCalc ? needPay : price}}</span>
        </div> -->

        <div style="text-align: center; padding: 10px 0px;">
          <el-button :disabled = "isLoading && !isExpire" type="primary" @click="submitPayMoney(null)">确认</el-button>
          <el-button @click="canback">查看订单</el-button>
        </div>

      </div>

    </div>

    <!-- 支付二维码 -->
    <div v-if="showType === 2">
      <div class="productMsg" style="padding:40px" v-loading="isLoading">
        <!-- 微信支付 -->
        <div v-if='payType === "1"'>
          <div style="line-height: 50px;"  v-if="this.timeData>0">
            距离二维码过期还剩<span style="font-size:16px;color:red;padding:0px 5px">{{ timeData }}</span>秒，过期后请点击刷新按钮重新获取二维码
          </div>
          <div style="line-height: 50px;"  v-else>
            二维码已经过期,请点击刷新按钮重新获取二维码
          </div>
          <div v-if="this.timeData>0" style="margin: auto; text-align: center;width: 100%;padding: 30px 0px;">
            <div class="qrcode" ref="qrCodeDiv" style="margin: auto; text-align: center;width: 200px; border-radius: 5px;"></div>
          </div>
          <div v-else style="margin: auto; text-align: center;width: 210px; height:210px;border-radius: 5px;" >
            <i class="el-icon-refresh-right" style="height: 200px;width: 200px; font-size: 130px;padding: 5px; color: #ccc; cursor: pointer;" @click="submitPayMoney({
              'orderNum': orderMsg.orderNum,
              'couponCode': null,
              'paidAmount': null,
            })"></i>
          </div>
        </div>
        <!-- 支付宝支付 -->
        <div  v-else-if='payType === "2"'>
          <div v-if="orderMsg.orderStatus === '10' && !isExpire " style="text-align:left;line-height:50px;margin: auto;text-align: center;" >距离订单关闭还剩
            <PayTime v-if="orderMsg" :data="orderMsg" @payCallback="refrashOrderData" style="display: inline;color: red;"/>，请及时支付
          </div>

          <div v-if="!isExpire" style="margin: auto; text-align: center;width: 100%;padding: 30px 0px;">
            <iframe :srcdoc="alipayFormData" height="210px" style=" width:200px;border: 210px;"></iframe>
          </div>
          <div v-else style="margin: auto; text-align: center;width: 210px; height:210px;border-radius: 5px;" >
            <i class="el-icon-refresh-right" style="height: 200px;width: 200px; font-size: 130px;padding: 5px; color: #ccc; cursor: pointer;"></i>
          </div>
        </div>

        <div style="text-align: center; padding: 10px 0px;">
          <el-button @click="canback">查看订单</el-button>
        </div>

      </div>
    </div>
    <!-- 支付二维码 -->

  </div>
</template>

<script>
import{orderGoPay,wapGoPay,couponGetOne,wxqueryOrderByOutTradeNo,aliqueryOrderByOutTradeNo,aliGopay,orderIsCalc}from '@/utils/apiManage'
import citys from '@/utils/city.js'
import moment from 'moment'
import wechat from '@/assets/wechat.png'
import alipayIcon from '@/assets/alipayIcon.png'
import PayTime from '@/components/PayTimer'
import QRCode from 'qrcodejs2'
import * as math from 'mathjs'
let Timer = null
export default {
  name: 'OrderPay',
  props: {
    orderNum:{required:true,type:undefined},

  },
  components:{
    PayTime,
  },
  data(){
    return {
      isLoading:false,
      showType:0,
      timeData:null,
      wechat,
      alipayIcon,
      isExpire:false,

      step:1,
      activeMsg:null,
      fApi:{},
      isUsePreferential: true,
      ticketList:[],
      ticketType:null,
      payType: '2',  // 默认使用支付宝
      orderMsg:null,
      preferential:"",
      isChecked:false,
      protocolMsg:null,
      htmlMsg:null,
      formInfo:[],
      value: {},
      cityOption:citys,
      cardOption:[],
      options: [],
      showLoading:false,
      regFormData:{},
      coupon:null,
      needPay:0,
      QRImgUrl:null,
      startTime:null,
      couponPrice:null,
      isMobile:true,
      alipayFormData:null,
      isCalc:null,
      price:null,

    }
  },
  destroyed(){
    if(Timer){
      clearInterval(Timer)
      Timer = null
    }
  },
  mounted(){
    this.orderIsCalc()
    this.goPayNow()
    var system = {};
    var p = navigator.platform;
    var u = navigator.userAgent;
    system.win = p.indexOf("Win") == 0;
    system.mac = p.indexOf("Mac") == 0;
    system.x11 = (p == "X11") || (p.indexOf("Linux") == 0);
    if (system.win || system.mac || system.xll) {//如果是PC转
      if (u.indexOf('Windows Phone') == -1) {  //win手机端
        this.isMobile = false
      }
    }
  },

  methods:{
    couponChange(){
      this.coupon = null
      this.preferential = ""
      this.couponPrice = 0
      this.needPay = this.orderMsg.ordersDetails[0].product.isActivityPrice === 1 ? this.orderMsg.ordersDetails[0].product.activityPrice : this.orderMsg.ordersDetails[0].product.price
    },
    viewOrderPay(){
      if(Timer){
        this.timeData=0
        clearInterval(Timer)
        Timer = null
      }
      this.showType = 1
    },
    canback(){
      if(Timer){
        this.timeData=0
        clearInterval(Timer)
        Timer = null
      }
      this.$emit("cancelPay")
    },
    refrashOrderData(){
      this.isExpire = true
      clearInterval(Timer)
      Timer = null
      this.$alert('订单已过期，点击确定跳转订单列表页面！','系统提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: action => {
          this.$emit("callbackOrderExpire")
        }
      });

    },
    goPayNow(){
      this.isLoading = true
      return orderGoPay({
        "orderNum": this.orderNum
      }).then(res => {
        if(res.code === 200 && res.data){
          res.data.createTime = res.data.createTime ? moment(res.data.createTime).format('YYYY-MM-DD HH:mm:ss') : null
          this.orderMsg = res.data
          if(this.orderMsg.ordersDetails[0].product.isActivityPrice === 1){
            this.needPay = Number(this.orderMsg.ordersDetails[0].product.isActivityPrice) > 0 ?Number(this.orderMsg.ordersDetails[0].product.activityPrice) : 0
          }else{
            this.needPay = Number(this.orderMsg.ordersDetails[0].product.price) > 0 ?Number(this.orderMsg.ordersDetails[0].product.price) : 0
          }
          this.isLoading = false
          this.step=1
          this.showType=1
        }
      }).catch(()=>{
        this.isLoading = false
      })
    },
    orderIsCalc(){
      return orderIsCalc({
        "orderNum": this.orderNum
      }).then(res => {
        if(res.code === 200 && res.data){
          this.isCalc = res.data.isCalc
          this.price = res.data.price ? res.data.price : null
        }
      }).catch((error)=>{

      })
    },
    queryOrderByOutTradeNo(type){
      if(type === 1){
        return wxqueryOrderByOutTradeNo(this.orderNum).then(res => {
          if(res.code === 200 && res.msg ==='支付成功!'){
            this.orderSuccessPay(res.msg)
          }else if(res.code === 5004){
            this.orderCannotPay(res.msg)
          }else if(res.code === 5006){
            this.orderCannotPay(res.msg)
          }
        })
      }else{

        return aliqueryOrderByOutTradeNo(this.orderNum).then(res => {
          if(res.code === 200 && res.msg ==='支付成功!'){
            this.orderSuccessPay(res.msg)
          }else if(res.code === 5004){
            this.orderCannotPay(res.msg)
          }else if(res.code === 5006){
            this.orderCannotPay(res.msg)
          }
        })
      }
    },
    submitPayMoney(val){
      this.isLoading = true
      let param = null
      if(!val){
        param ={
          "orderNum": this.orderMsg.orderNum,
          "couponCode": null,
          "paidAmount": this.isCalc ? this.needPay : this.price,
          "sourceId": this.orderMsg.sourceId,
        }
        // 2024.09.02 蒲天阳修改：支付金额必须为0
        if(param.paidAmount != 0){
          this.$alert( "请输入正确的校验码","提示", {
              confirmButtonText: '确定'
            });
            this.isLoading = false
            return
        }
        if(this.payType === undefined){
          this.$alert( "请选择支付方式！","提示", {
              confirmButtonText: '确定'
            });
        }
        if(this.isUsePreferential && this.preferential.length>0 && this.coupon){
          param.couponCode = this.preferential
        }else if(this.isUsePreferential){
          this.isLoading = false
          // if(this.preferential.length<=0){
          //   this.$alert( "请输入校验码","提示", {
          //     confirmButtonText: '确定'
          //   });
          // }else{
          //   this.$alert( "请点击使用校验码！","提示", {
          //     confirmButtonText: '确定'
          //   });
          // }
          // return null
        }
      }else{
        param = val
      }
      if(this.payType && this.payType === "1"){//微信
        return wapGoPay(param).then(res => {
          this.isLoading = false

          //计算订单过期时间 是否超过一分钟
          this.payResoult(res)

        }).catch((e) => {
          this.isLoading = false
          this.clearCoupon()
        });
      }else if(this.payType && this.payType === "2"){//支付宝
        return aliGopay(param).then(res => {
          this.isLoading = false
          if(res.code ===200 && res.msg==="支付成功!"){
            this.orderSuccessPay(res.msg)
          }else if(res.code === 200 && res.data){
            this.alipayFormData = res.data
            this.showType = 2
            //支付宝每3秒查询支付状态
            Timer= setInterval(()=>{
              this.queryOrderByOutTradeNo(2)
            }, 3000);
          }

        }).catch((e) => {
          this.isLoading = false
          this.clearCoupon()
        });
      }else{//没有选择支付方式
        this.isLoading = false
        this.step=1
        this.showType=1
      }


    },
    payResoult(res){//微信支付
      if(res.code === 200 && res.data ){//需要扫二维码支付
        this.payExpire()
        setTimeout(() => {
          var qrcode = new QRCode(this.$refs.qrCodeDiv, {
              text: res.data,//this.link, // 需要转换为二维码的内容
              width: 200,
              height: 200,
              colorDark: '#000000',
              colorLight: '#ffffff',
              correctLevel: QRCode.CorrectLevel.H
          })
        }, 100);
      }else if(res.code ===200 && res.msg==="支付成功!"){
        this.orderSuccessPay(res.msg)
      }else if(res.code === 5004){
        this.clearCoupon()
        if(res.data && res.data.indexOf("校验码数量失败")>-1){
          this.$message({
            message: "校验码使用失败！",
            type: 'warning'
          });
          this.timeData=0
          clearInterval(Timer)
          Timer = null
          this.showType = 1
        }else{
          this.orderCannotPay(res.msg)
        }

      }else if(res.code === 5006){
        this.clearCoupon()
        this.orderCannotPay(res.msg)
      }else if(res.code === 5007){
        this.clearCoupon()
        //手机端报201商户订单号重复
        if( res.msg && res.msg.indexOf('201')>-1){
          this.orderCannotPay("请前往手机端打开订单进行支付")
        }else{
          this.orderCannotPay(res.msg)
        }

      }else{//报错
        this.clearCoupon()
        this.$message({
          message: res.msg,
          type: 'warning'
        });
      }
    },
    payExpire(){//微信支付60秒过期
      this.showType = 2//显示支付页面
      let count = null
      this.startTime = moment(new Date())
      let end = moment(moment(this.startTime).add(60, 'seconds'))
      this.timeData=59
      Timer= setInterval(()=>{
        count = end.diff(moment(),'milliseconds')
        if(count>=0){
          this.timeData = moment(count).format("ss")
          if((this.timeData%2) === 0 && this.timeData!=60){//3秒调用一次支付回调
            this.queryOrderByOutTradeNo(1)
          }
        }else{
          this.timeData=0
          clearInterval(Timer)
          Timer = null
        }
      }, 1000);
    },

    orderCannotPay(msg){
      this.timeData=0
      clearInterval(Timer)
      Timer = null
      this.$alert(msg, '系统提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: action => {
          this.$emit("callbackOrderExpire")
        }
      });
    },
    orderSuccessPay(msg){
      this.timeData=0
      clearInterval(Timer)
      Timer = null
      this.$alert(msg,'系统提示',{
        confirmButtonText: '确定',
        type: 'success',
        callback: action => {
          this.$emit("paySuccess")
        }
      });
    },
    protocolClick(record){
      this.htmlMsg = record.protocolContent
      this.showProtocol = true
    },
    couponBlur(){
      this.preferential= (this.preferential ? this.preferential.trim():"")
      if(this.preferential){
        return couponGetOne({
          "couponCode": this.preferential,
          "ticketId": this.orderMsg.ordersDetails[0].productId
        }).then(res => {
          if(res.code === 200 && res.data ){
            this.coupon = res.data
            if(this.orderMsg.ordersDetails[0].product.isActivityPrice === 1){
              let activityPrice = math.bignumber(this.orderMsg.ordersDetails[0].product.activityPrice)
              let couponPrice = math.bignumber(res.data.couponPrice)
              this.needPay = (math.subtract(activityPrice,couponPrice)<=0 )? 0 :math.format(math.subtract(activityPrice,couponPrice))
              this.couponPrice = math.format(math.subtract(activityPrice,this.needPay))
            }else{
              let price=math.bignumber(this.orderMsg.ordersDetails[0].product.price)
              let couponPrice =  math.bignumber(res.data.couponPrice)
              this.needPay =(math.subtract(price,couponPrice)<=0 )? 0 :math.format(math.subtract(price,couponPrice))
              this.couponPrice = math.format(math.subtract(price,this.needPay))
            }
          }else{
            this.preferential = ""
            this.couponPrice = 0
            this.coupon = null
            this.needPay = this.orderMsg.ordersDetails[0].product.isActivityPrice === 1 ? this.orderMsg.ordersDetails[0].product.activityPrice : this.orderMsg.ordersDetails[0].product.price
            this.$alert(res.msg,"系统提示", {
              type: 'warning',
              confirmButtonText: '确定'
            });
          }
        })
      }else{
        this.$alert("请输入校验码","系统提示", {
          type: 'warning',
          confirmButtonText: '确定'
        });
      }
    },
    clearCoupon(){
      this.preferential = ""
      this.couponPrice = null
      this.coupon = null
      this.isLoading = false
      if(this.orderMsg.ordersDetails[0].product.isActivityPrice === 1){
        this.needPay = Number(this.orderMsg.ordersDetails[0].product.activityPrice)
      }else{
        this.needPay = Number(this.orderMsg.ordersDetails[0].product.price)
      }
    }
  },


}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>

body{
  padding-right: 0px !important;
  overflow: hidden;
}
.el-form-item__label:before{
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}
.el-form-item__label{
  margin-bottom: 5px !important;
}

h3 {
  margin: 40px 0 0;
}
a {
  color: #42b983;
}
.registEnter{
  padding-bottom: 20px;
  /* margin: 0px 4%;
  width: 90%; */
  text-align: center;
  min-height: 500px;
}
.el-form-item__content,.el-radio-group{
  text-align: left !important;
}
.el-radio{
  margin-right: 10px !important;
}
.el-form-item{
  margin-bottom: 15px !important;
}
.formEdite{
  text-align: left;
  margin: 10px 5% 0px 5%;
  padding: 20px 5%;
  width: 90%;
  background-color: #ffffff;
  border-radius: 10px;
}
.moneyShow{
  padding: 0px 0px;
}
.productMsg,.orderDetail{
  background:#ffffff;
  margin:5px;
  border-radius:5px;
  padding: 20px;
}
.productIcon{
  float:left;
  padding-left:10px;

}
.productAttribute{
  float:right;
  margin:auto;
  text-align:right;
  padding:5px;
  min-width: 100px;
}
.payMoneyMsg{
  padding:10px;
  margin:auto;
  text-align:right;
  font-size:16px;
}



</style>
