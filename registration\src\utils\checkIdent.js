const validateIdent = {
    IdentityCode_City: {
        11: '北京', 12: '天津', 13: '河北', 14: '山西', 15: '内蒙古', 21: '辽宁', 22: '吉林',
        23: '黑龙江', 31: '上海', 32: '江苏', 33: '浙江', 34: '安徽', 35: '福建', 36: '江西',
        37: '山东', 41: '河南', 42: '湖北', 43: '湖南', 44: '广东', 45: '广西', 46: '海南',
        50: '重庆', 51: '四川', 52: '贵州', 53: '云南', 54: '西藏', 61: '陕西', 62: '甘肃',
        63: '青海', 64: '宁夏', 65: '新疆', 71: '台湾', 81: '香港', 82: '澳门', 91: '国外'
    },
    //检查号码是否符合规范，包括长度，类型
    IdentityCode_isCardNo(card) {
        //身份证号码为15未或18位，15位时全为数字，18位前17位为数字，最后一位时校验位
        let reg = /(^\d{15}$)|(^\d{17}(\d|X)$)/;
        if (reg.test(card) === false) {
            return false;
        }
        return true;
    },
    IdentityCode_checkProvince(card) {
        let province = card.substr(0, 2);
        if (validateIdent.IdentityCode_City[province] == undefined) {
            return false;
        }
        return true;
    },
    IdentityCode_checkBirthday(card) {
        let len = card.length;
        //身份证15位时，次序位省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
        if (len == 15) {
            let re_fifteen = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
            let arr_data = card.match(re_fifteen); //正则取号码内所含出生年月日数据
            let year = arr_data[2];
            let month = arr_data[3];
            let day = arr_data[4];
            let birthday = new Date('19' + year + '/' + month + '/' + day);
            return validateIdent.IdentityCode_verfyBirthday('19' + year, month, day, birthday);
        }
        //身份证18位时，次序位省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能位X
        if (len == 18) {
            let re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/;
            let arr_data = card.match(re_eighteen); //正则取号码内所含出生年月日数据
            let year = arr_data[2];
            let month = arr_data[3];
            let day = arr_data[4];
            let birthday = new Date(year + '/' + month + '/' + day);
            return validateIdent.IdentityCode_verfyBirthday(year, month, day, birthday);
        }
        return false;
    },
    IdentityCode_verfyBirthday(year, month, day, birthday) {
        let now = new Date();
        let now_year = now.getFullYear();
        //年月日是否合理
        if (birthday.getFullYear() == year && (birthday.getMonth() + 1) == month && birthday.getDate() == day) {
            //判断年份的范围（3-150岁之间）
            let time = Number(now_year) - Number(year);
            if (time >= 3 && time <= 150) {
                return true;
            }
            return false;
        }
        return false;
    },
    //检验位的规则
    IdentityCode_checkParity(card) {
        card = validateIdent.IdentityCode_changeFifteenToEighteen(card);
        let len = card.length;
        if (len == 18) {
            let arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
            let arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
            let cardTemp = 0;
            for (let i = 0; i < 17; i++) {
                cardTemp += card.substr(i, 1) * arrInt[i];
            }
            let valnum = arrCh[cardTemp % 11];
            if (valnum == card.substr(17, 1)) {
                return true;
            }
            return false;
        }
        return false;
    },
    //15位转位18位身份证号
    IdentityCode_changeFifteenToEighteen(card) {
        if (card.length == 15) {
            let arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
            let arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
            let cardTemp = 0;
            card = card.substr(0, 6) + '19' + card.substr(6, card.length - 6);
            for (let i = 0; i < 17; i++) {
                cardTemp += card.substr(i, 1) * arrInt[i];
            }
            card = arrCh[cardTemp % 11];
            return card;
        }
        return card;
    },
    //身份证校验主入口
    IdentityCodeValid(card) {
        let pass = true;
        //判空
        if (pass && card == '') {
            pass = false;
        }
        //校验长度，类型
        if (pass && validateIdent.IdentityCode_isCardNo(card) == false) {
            pass = false;
        }
        //检查省份
        if (pass && validateIdent.IdentityCode_checkProvince(card) == false) {
            pass = false;
        }
        //校验生日
        if (pass && validateIdent.IdentityCode_checkBirthday(card) == false) {
            pass = false;
        }
        //校验位的检测
        if (pass && validateIdent.IdentityCode_checkParity(card) == false) {
            pass = false;
        }
        if (pass) {
            return true;
        } else {
            return false;
        }
    }
}

export default validateIdent.IdentityCodeValid;