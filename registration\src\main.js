import Vue from 'vue'
import 'element-ui/lib/theme-chalk/index.css';
import formCreate from '@form-create/element-ui'
import App from './App'
import router from './router'

Vue.use(formCreate)
Vue.config.productionTip = false

import {
  Input,
  Radio,
  RadioGroup,
  RadioButton,
  Checkbox,
  Select,
  Option,
  OptionGroup,
  Button,
  DatePicker,
  TimePicker,
  Form,
  FormItem,
  Row,
  Col,
  Cascader,
  Link,
  Card,
  MessageBox,
  Message,
  Menu,
  MenuItem,
  Avatar,
  Steps,
  Step,
  Dialog,
  Popover,
  Image,
  Tooltip,
  Descriptions,
  DescriptionsItem,
  tabs,
  TabPane,
  Loading,
  Popconfirm,
  Autocomplete,
}from "element-ui"

Vue.use(tabs);
Vue.use(TabPane);
Vue.use(Menu);
Vue.use(Descriptions);
Vue.use(DescriptionsItem);
Vue.use(MenuItem);
Vue.use(Avatar);
Vue.use(Steps);
Vue.use(Step);
Vue.use(Dialog);
Vue.use(Popover);
Vue.use(Image);
Vue.use(Tooltip);
Vue.use(Input);
Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(RadioButton);
Vue.use(Checkbox);
Vue.use(Select);
Vue.use(Option);
Vue.use(OptionGroup);
Vue.use(Button);
Vue.use(TimePicker);
Vue.use(DatePicker);
Vue.use(Form);
Vue.use(FormItem);
Vue.use(Row);
Vue.use(Col);
Vue.use(Cascader);
Vue.use(Card);
Vue.use(Link);
Vue.use(Loading);
Vue.use(Popconfirm);
Vue.use(Autocomplete);

Vue.prototype.$message = Message;
Vue.prototype.$alert = MessageBox.alert;
Vue.prototype.$ELEMENT = { size: 'medium', zIndex: 3000 };
new Vue({
  el: '#app',
  router,
  render: h => h(App)
})
