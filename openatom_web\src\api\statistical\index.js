import request from "@/utils/request";
// 票数统计
export function queryTicket(id) {
  return request({
    url: "/stats/product/" + id,
    method: "get",
  });
}
// 签到统计
export function querySignChart(id) {
  return request({
    url: "/stats/signStatics/" + id,
    method: "get",
  });
}

// 报名来源
export function querySource(id) {
  return request({
    url: "/stats/regFromStatics/" + id,
    method: "get",
  });
}

// 门票金额
export function queryAmount(id) {
  return request({
    url: "/stats/amountStatics/" + id,
    method: "get",
  });
}
// 
export function channelAmount(id) {
  return request({
    url: "/stats/channelStatics/" + id,
    method: "get",
  });
}
