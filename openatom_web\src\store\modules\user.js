import { login, logout, getInfo,access_token } from '@/api/login'
import {getatom} from '@/api/system/user.js'
import { getToken, setToken, removeToken,getAccesToken,setAccesToken,removeAccesToken } from '@/utils/auth'

const user = {
  state: {
    token: getToken(),
    Access_token:getAccesToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    userId:"",
    activeInfo:null
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_USERID:(state,userId)=>{
      state.userId = userId
    },
    SET_ACTIVEINFO:(state,info)=>{
      state.activeInfo=info
    }
  },

  actions: {
    setActive({commit},info){
      commit('SET_ACTIVEINFO', info)
    },
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid).then(res => {
          setToken(res.token)
          commit('SET_TOKEN', res.token)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    //获取accesToken
    Access_token({ commit },data){
      return new Promise((resolve, reject) => {
        access_token(data).then(res => {
          setToken(res.token)
          setAccesToken(res.accessToken)
          commit('SET_TOKEN', res.token)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.user
          const avatar = (user.avatar == "" || user.avatar == null) ? require("@/assets/images/profile.jpg") : process.env.VUE_APP_BASE_API + user.avatar;
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', user.userName)
          commit("SET_USERID",user.userId)
          getatom().then(result=>{
            if(result.avatar_url&&result.avatar_url!=""){
              commit('SET_AVATAR', result.avatar_url)
            }else{
              commit('SET_AVATAR', avatar)
            }
           
          })
         
          resolve(res)

        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          removeAccesToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        removeAccesToken()
        resolve()
      })
    }
  }
}

export default user
