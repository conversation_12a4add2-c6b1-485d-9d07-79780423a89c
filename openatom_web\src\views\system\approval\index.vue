<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="待审批" name="0" :disabled="flag1">
        <wait v-if="activeName == 0"></wait>
      </el-tab-pane>
      <el-tab-pane label="已审批" name="1" :disabled="flag2">
        <haveApproval v-if="activeName == 1"></haveApproval>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import haveApproval from "./haveApproval.vue";
import wait from "./wait.vue";
export default {
  name: "approval",
  components: {
    haveApproval,
    wait,
  },
  data() {
    return {
      activeName: "0",
      flag1: false,
      flag2: true,
    };
  },

  beforeCreate() {},
  created() {
    setTimeout(() => {
      this.flag2 = false;
    }, 1000);
  },
  mounted() {},
  update() {},
  methods: {
    handleClick(tab) {
      this.activeName = tab.index;
      if (tab.index == 0) {
        this.flag2 = true;
        setTimeout(() => {
          this.flag2 = false;
        }, 1000);
      }
      if (tab.index == 1) {
        this.flag1 = true;
        setTimeout(() => {
          this.flag1 = false;
        }, 1000);
      }
    },
  },
};
</script>