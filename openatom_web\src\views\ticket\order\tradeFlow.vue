<template>
  <div class="app-container">
    <el-form ref="form" :inline="true" :model="searchForm">
      <el-form-item label="订单编号:" prop="orderNum">
        <el-input
          placeholder="请输入订单编号"
          v-model="searchForm.orderNum"
        ></el-input>
      </el-form-item>
      <el-form-item label="支付人:" prop="payer">
        <el-input
          placeholder="请输入支付人"
          v-model="searchForm.payer"
        ></el-input>
      </el-form-item>
      <el-form-item label="支付方式">
        <el-select
          size="small"
          v-model.trim="searchForm.paidMethod"
          clearable
          placeholder="请选择支付方式"
        >
          <el-option
            v-for="item in paymentMode"
            :key="item.value"
            selected="selected"
            :label="item.cnName"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类别">
        <el-select
          size="small"
          v-model.trim="searchForm.flowType"
          clearable
          placeholder="请选择类别"
        >
          <el-option :label="'支付'" :value="1" />
          <el-option :label="'退款'" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="交易来源">
        <el-select
          size="small"
          v-model.trim="searchForm.tradeType"
          clearable
          placeholder="请选择交易来源"
        >
          <el-option :label="'PC'" :value="'PC'" />
          <el-option :label="'手机'" :value="'WAP'" />
        </el-select>
      </el-form-item>
      <el-form-item label="开始日期">
        <el-date-picker
          size="small"
          v-model="searchForm.startTime"
          type="datetime"
          placeholder="选择日期时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结束日期">
        <el-date-picker
          size="small"
          v-model="searchForm.endTime"
          type="datetime"
          placeholder="选择结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item style="margin-left: 10px">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click="search(1)"
          >查询</el-button
        >
      </el-form-item>
    </el-form>
    <el-table ref="orderTable" :data="tableData" style="width: 100%">
      <el-table-column
        :resizable="false"
        align="center"
        label="序号"
        type="index"
        width="50"
      />
      <el-table-column
        :resizable="false"
        align="center"
        label="活动名称"
        :show-overflow-tooltip="true"
      >
        {{ this.activityName }}
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="orderNum"
        label="订单号"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="tradeNo"
        label="交易编号"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="payer"
        label="支付人"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="paidAmount"
        label="支付金额"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="paidMethod"
        label="支付方式"
      >
        <template v-slot="scope">
          <span v-for="item in paymentMode" :key="item.value">{{
            scope.row.paidMethod === item.value ? item.cnName : ""
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="tradeType"
        label="交易类型"
      >
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="flowType"
        label="类别"
      >
        <template v-slot="scope">
          <span> {{ scope.row.flowType == 1 ? "支付" : scope.row.flowType == 2? "退款":"" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="tradeState"
        label="交易状态"
      >
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="remark"
        label="备注"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="paidTime"
        label="支付时间"
      >
        <template slot-scope="{ row }">
          {{ formatDate(row.paidTime) }}
        </template>
        
      </el-table-column>
   <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="createTime"
        label="创建时间"
      >
        <template slot-scope="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="searchForm.pageNum"
      :limit.sync="searchForm.pageSize"
      @pagination="getData"
    />
  </div>
</template>
<script>
import order from "@/api/ticket/order/index";
import { formatDate } from "@/utils/index";
const defaultSearchForm = {
  startTime: "",
  endTime: "",
  paidMethod: "",
  tradeType: "",
  orderNum: "",
  pageNum: 1,
  pageSize: 10,
};
export default {
  name: "tradeFlow",
  components: {},
  watch: {
    orderTimes(cur, _old) {
      if (cur) {
        this.searchForm.startTime = cur[0];
        this.searchForm.endTime = cur[1];
      } else {
        this.searchForm.startTime = "";
        this.searchForm.endTime = "";
      }
      return [this.searchForm.startTime, this.searchForm.endTime];
    },
  },
  data() {
    return {
      searchForm: { ...defaultSearchForm },
      showOrderInfo: false,
      formatDate,
      total: 0,
      ticketList: [],
      paymentMode: [
        {
          value: 1,
          cnName: "微信",
        },
        {
          value: 2,
          cnName: "支付宝",
        },
      ],
      orderTimes: [], // 订单日期
      tableData: [],
      tableHight: "600px",
      pageData: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
    };
  },
  props: {
    currOrderNum: {
      type: String,
      default: "",
    },
  },
  created() {
    this.searchForm.orderNum = this.currOrderNum;
  },
  mounted() {
    this.search(1);
  },
  methods: {
    init(val) {
      this.getData();
    },

    async getData() {
      this.searchForm.activityId = JSON.parse(
        sessionStorage.getItem("activity")
      ).activityId;
           let res = await order.getTradeFlow(this.searchForm.pageNum,this.searchForm.pageSize,this.searchForm);
      if (res.code === 200) {
        this.tableData = res.rows;
        this.total = res.total;
      }
    },
    search(val) {
      this.pageData.pageNum = val;
      this.init(val);
    },
    handleDeleteOrder(data) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(async () => {
          let res = await order.delOrder(data.orderNum);
          if (res.code === 200) {
            this.$message.success("删除成功");
            this.getData();
          }
        })
        .catch(() => {
          return false;
        });
    },
    handleBackOrder(data) {
      this.$confirm("确定退款吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(() => {})
        .catch(() => {
          return false;
        });
    },
    handleCurrentChange(val) {
      this.search(val.page);
    },
  },
};
</script>

<style scoped lang="scss">
.tableBtnGroup {
  margin-bottom: 20px;
}
</style>

