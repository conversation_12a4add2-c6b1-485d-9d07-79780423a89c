<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
<title>答卷</title>
  <link rel="stylesheet" type="text/css" href="js/plugs/jquery.mobile-1.4.5/jquery.mobile-1.4.5.min.css">
  <link rel="stylesheet" type="text/css" href="js/plugs/jquery-ui-1.10.3.custom/css/mycss/jquery-ui-1.10.3.custom.css" />
  <link rel="stylesheet" type="text/css" href="js/plugs/font-awesome-4.2.0/css/font-awesome.css" >
  <link rel="stylesheet" type="text/css" href="js/plugs/validate/jquery.validate.css" />
  <link rel="stylesheet" type="text/css" href="css/survey-common.css" />
  <link rel="stylesheet" type="text/css" href="css/answer-m.css" />
  <script src="js/dw/header.js" type="text/javascript"></script>
  <script type="text/javascript" src="js/plugs/jquery-ui-1.10.3.custom/js/jquery-1.10.1.js"></script>
  <script type="text/javascript" src="js/plugs/jquery-ui-1.10.3.custom/js/jquery-ui-1.10.3.custom.js"></script>
  <link rel="stylesheet" href="js/plugs/jQuery-File-Upload-9.19.2/css/jquery.fileupload.css">
  <link rel="stylesheet" href="js/plugs/jQuery-File-Upload-9.19.2/css/jquery.fileupload-ui.css">
  <script src="js/plugs/jQuery-File-Upload-9.19.2/js/vendor/jquery.ui.widget.js"></script>
  <script src="js/plugs/jQuery-File-Upload-9.19.2/js/jquery.fileupload.js"></script>
  <script src="js/plugs/jQuery-File-Upload-9.19.2/js/jquery.iframe-transport.js"></script>
  <script type="text/javascript" src="js/plugs/laydate/laydate.js"></script>
  <script type="text/javascript" src="js/common/ans-common.js"></script>
  <script type="text/javascript" src="js/common/common.js"></script>
  <script type="text/javascript" src="js/dw/an/an-pcm-common.js"></script>
  <script type="text/javascript" src="js/dw/an/ans-m-data.js"></script>
  <script type="text/javascript" src="js/dw/an/ans-m.js"></script>
  <script src="https://openatom.cn/trivial-services/stats/index/entry?id=8&auto=no"></script>
<script type="text/javascript">
  $(document).click(function(){
    $(".tabbarDialog",parent.document).hide();
    $(".js-tabselected",parent.document).removeClass("js-tabselected");
  });
  $(document).ready(function(){
    let AccesToken=  getCookie('Acces-Token');
    if(!AccesToken||AccesToken==""){
      $('#submitSurvey').hide()
        $('#dialog').dialog({ autoOpen: true, modal: true,position:"center" });
        if(window.location.href.indexOf("?surveyId=")>-1){
          let sid=window.location.href.split("?surveyId=")[1]
          window.localStorage.setItem("answerId-m",sid)
        }
    }else{
    //   let AccesToken=getCookie('Acces-Token');
    //   $.ajax({
    //   url: "https://api.atomgit.com/user/info",
    //   contentType: 'application/json',
    //   type: "get",
    //   headers:{Authorization:"Bearer "+AccesToken},
    //   success: function (httpResult){
    //     window.localStorage.setItem("userInfo",JSON.stringify(httpResult))
    //     $("#username").val(httpResult.login)
    //   },
    //   error: function(xmlHttpRequest, textStatus, errorThrown){
    //     // alert(xmlHttpRequest);
       
    //   }
    // })
    }
let userInfo=localStorage.getItem("userInfo")?JSON.parse(localStorage.getItem("userInfo")):null
// if(userInfo){
if (window._oafs) {
window._oafs.setDefault({
  pageId: "answer-Wap",
  userId: userInfo&&userInfo.id ? userInfo.id : undefined,
  extra: {
    path: "answer-wap",
  },
});
window._oafs.pv();
}
$( "#dialog" ).dialog({
      modal: true,
    autoOpen: false,
    closeOnEscape:false,
    show: {
      effect: "blind",
      duration: 1000
    },
    hide: {
      effect: "explode",
      duration: 1000
    },
    buttons: { "登录": function() { 
      if (develop == "test") {
        window.location.href =
          "https://atomgit.com/login/oauth/authorize?client_id=97c24289a6964fd5&redirect_uri=https://www.ican365.net/dwsurvey/static/diaowen/answerMobileFree.html";
      } else if (develop == "prod") {
        window.location.href =
          "https://atomgit.com/login/oauth/authorize?client_id=97c24289a6964fd5&redirect_uri=https://openatomcon.openatom.cn/dwsurvey/static/diaowen/answerMobileFree.html";
      } else {
        window.location.href =
          "https://atomgit.com/login/oauth/authorize?client_id=97c24289a6964fd5&redirect_uri=http://atomgitactivity.com:8091/dwsurvey/static/diaowen/answerMobileFree.html";
      }
     }} 
  });
});
function getCookie(name) {
  let cookies = document.cookie; // 获取所有cookie，格式为 "key1=value1; key2=value2; ..."
  let cookieArray = cookies.split('; '); // 以分号和空格分割cookie字符串

  for (let i = 0; i < cookieArray.length; i++) {
    let cookiePair = cookieArray[i].split('='); // 分割键和值
    if (cookiePair[0] === name) {
      return decodeURIComponent(cookiePair[1]); // 解码并返回值
    }
  }

  return null; // 如果没有找到指定的cookie，返回null
}
</script>
</head>
<body>
  <div id="dialog" title="系统提示" style="text-align: center;padding-top: 25px;">
    <p>未登录状态,请重新登录!</p>
  </div>
<div>
  <div id="resultProgressRoot">
    <div class="progress-label">完成度：0%</div>
    <div id="resultProgress" class="progressbarDiv"></div>
  </div>
</div>
<div id="diaowen-m-body">
  <div id="preview_head_top" style='text-align: center;background: #aaa;padding: 10px;color: #f3f3f3;'>当前为预览模式</div>
  <input type="hidden" id="id" name="id" value="">
  <input type="hidden" id="sid" name="sid" value="">
  <input type="hidden" id="ctxApp" name="ctx" value="/api/dwsurvey/app">
  <input type="hidden" id="ctx" name="ctx" value="/api/dwsurvey/anon">
  <input type="hidden" id="prevHost" value="http://ent.surveyform.cn/">
  <input type="hidden" id="tag" name="tag" value="${tag }">
  <input type="hidden" id="anClient" name="anClient" value="m">
  <input type="hidden" id="laydatetag" name="laydatetag" value="laydatetag">
  <input type="hidden" id="answerLoadTag" name="answerLoadTag" value="0">
  <input type="hidden" id="breakpoint1" name="breakpoint1" value="0">
  <form id="surveyForm" action="/dwsurvey/bk/api/dwsurvey/anon/response/saveMobile.do" method="post" data-ajax="false">
    <input type="hidden" id="surveyId" name="surveyId" value="${survey.id }">
    <input type="hidden" id="ruleCode" name="ruleCode" value="">
    <input type="hidden" id="surveyLogId" name="surveyLogId" value="${survey.surveyLogId }">
    <input type="hidden" name="form-from" value="mobile" >
    <input type="hidden" id="wxCode" name="wxCode" value="">
    <input type="hidden" id="username" name="username" value="">
    <div id="dw_body_content">
    <div id="diaowen-m-content" class="diaowen-m-content" >
      <div id="dwSurveyHeader" >

        <div id="answer-m-head" style="text-align: center;margin: 0px 16px;">
          <div id="dwSurveyTitle" class="noLogoImg" style="padding-top: 5px;">
            <div id="dwSurveyName" class="dwSvyName" >加载中</div>
          </div>
          <div id="dwSurveyNote" style="text-align: left;">
            <div id="dwSurveyNoteEdit"  style="font-weight: normal;line-height: 20px;">加载中</div>
          </div>
        </div>
      </div>
      <div id="m-errorMsg"></div>
      <div  id="dwSurveyQuContent" >
        <div>
          <div id="dwSurveyQuContentBg">
            <ul id="dwSurveyQuContentAppUl">

            </ul>
          </div>
        </div>
      </div>
    </div>
    </div>
  </form>
</div>
<div id="fixedMsg" style="position: fixed;top: 0px;width: 100%;padding: 10px;text-align: center;font-size: 18px;letter-spacing: 4px;line-height: 56px;background-color: #111;background-color: rgba(17,17,17,0.5);color: #fff;color: rgba(255,255,255,0.5);z-index: 200;display: none;"></div>
<div style="display: none;">
  <div>
    <ul id="radioQuModel" >
      <li class="li_surveyQuItemBody"  >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="RADIO" >
            <input type="hidden" class="quId" value="${en.id }"  >
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="answerTag" value="0" >
            <input type="hidden" class="hv" value="">
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <div class="quLogicInputCase"></div>
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent">
              <div>
                <legend class="quCoTitle" >
                  <span class="quTitleNum">${i.count }、</span>
                  <span class="quTitleText">${en.quTitle}</span>
                </legend>
                <div class="quCoItem" ></div>
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <ul id="checkboxQuModel" >
      <li class="li_surveyQuItemBody"  >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="CHECKBOX">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="answerTag" value="0" >
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <input type="hidden" class="paramInt01" value="${en.paramInt01 }">
            <input type="hidden" class="paramInt02" value="${en.paramInt02 }">
            <div class="quLogicInputCase"></div>
            <input type="hidden" class="hidden_input_tag" name="qu_${en.quType }_${en.id }" value="tag_qu_${en.quType }_${en.id }_" />
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent">
              <div>
                <legend class="quCoTitle" >
                  <span class="quTitleNum">${i.count }、</span>
                  <span class="quTitleText">${en.quTitle}</span>
                </legend>
                <div class="quCoItem" ></div>
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <ul id="fillblankQuModel">
      <li class="li_surveyQuItemBody"  >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="FILLBLANK">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="checkType" value="${en.checkType }">
            <input type="hidden" class="answerTag" value="0" >
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <input type="hidden" class="paramInt01" value="${en.paramInt01}" >
            <div class="quLogicInputCase"></div>
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent" >
              <legend class="quCoTitle" >
                <span class="quTitleNum">${i.count }、</span>
                <span class="quTitleText">${en.quTitle}</span>
              </legend>
              <div class="quCoItem" style="margin-top: 5px;"></div>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <ul id="scoreQuModel">
      <li class="li_surveyQuItemBody"  >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="SCORE">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <div class="quLogicInputCase"></div>
            <input type="hidden" class="hidden_input_tag" name="qu_${en.quType }_${en.id }" value="item_qu_${en.quType }_${en.id }_" />
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent">
              <legend class="quCoTitle" >
                <span class="quTitleNum">${i.count }、</span>
                <span class="quTitleText">${en.quTitle}</span>
              </legend>
              <div class="quCoItem"></div>
            </div>
          </div>
        </div>
      </li>
    </ul>>
    <ul id="orderQuModel">
      <li class="li_surveyQuItemBody"  >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="ORDERQU">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="answerTag" value="0" >
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <div class="quLogicInputCase">
            </div>
            <input type="hidden" class="hidden_input_tag" name="qu_${en.quType }_${en.id }" value="item_qu_${en.quType }_${en.id }_" />
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent">
              <div>
                <legend class="quCoTitle" >
                  <span class="quTitleNum">${i.count }、</span>
                  <span class="quTitleText">${en.quTitle}</span>
                </legend>
                <div class="quCoItem">
                  <div class="ui-controlgroup-controls "></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>>
    <ul id="pageQuModel">
      <li class="li_surveyQuItemBody"  >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="PAGETAG">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <div class="quLogicInputCase"></div>
          </div>
          <div class="surveyQuItem">
            <div class="pageBorderTop nohover" style="display: none;" ></div>
            <div class="surveyQuItemContent" style="padding: 0px;min-height: 30px;text-align: center;">
              <!-- <div class="pageQuContent">下一页（1/2）</div> -->
              <a href="#" class="sbtn24 sbtn24_0 nextPage_a" >下一页</a>&nbsp;&nbsp;
              <input type="hidden" name="prevPageNo" value="${pageNo-1 }">
              <input type="hidden" name="nextPageNo" value="${pageNo }">
            </div>
          </div>
        </div>
      </li>
    </ul>
    <ul id="paragraphQuModel">
      <li class="li_surveyQuItemBody"  >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="PARAGRAPH">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <div class="quLogicInputCase"></div>
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent"  style="min-height: 20px;">
              <div class="quCoTitle">
                <div class="quCoTitleEdit quTitleText" >${en.quTitle}</div>
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <ul id="mfillblankQuModel">
      <li class="li_surveyQuItemBody"  >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="MULTIFILLBLANK">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="pageNo" value="${pageNo}" >
<!--            <input type="hidden" class="answerTag" value="0" >-->
            <div class="quLogicInputCase"></div>
            <input type="hidden" class="hidden_input_tag" name="qu_${en.quType }_${en.id }" value="text_qu_${en.quType }_${en.id }_" />
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent">
              <legend class="quCoTitle" >
                <span class="quTitleNum">${i.count }、</span>
                <span class="quTitleText">${en.quTitle}</span>
              </legend>
              <div class="quCoItem"></div>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <ul id="uploadFileQuModel">
      <li class="li_surveyQuItemBody" >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="UPLOADFILE">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="checkType" value="${en.checkType }">
            <input type="hidden" class="answerTag" value="0" >
            <input type="hidden" class="paramInt01" value="${en.paramInt01 }">
            <input type="hidden" class="paramInt02" value="${en.paramInt02 }">
            <input type="hidden" class="randOrder" value="${en.randOrder }">
            <div class="quLogicInputCase"></div>
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent">
              <div class="quCoTitle">
                <span class="quTitleNum">${i.count }、</span>
                <span class="quTitleText">${en.quTitle}</span>
              </div>
              <div class="quCoItem"><ul>
                <li class="quCoItemUlLi">
                  <div class="quFillblankItem">
                    <div class="uploadFileTypeNoteSpan" style="color: grey;line-height: 23px;padding-bottom: 5px;font-size: 12px;">
                      <span class="uploadFileTypeNote" ></span>
                      <span>单个文件最大支持 <label class="uploadFileMaxSize">${en.paramInt02}</label> M</span>
                    </div>
                    <div class="upFileDiv">

                    </div>

                    <div>
                      <!-- The fileinput-button span is used to style the file input field as button -->
                      <span class="sbtn sbtn_0 fileinput-button">
																	<span>上传文件</span>
																	<input class="fileupload" type="file" name="file" multiple>
																	<input class="fileuploadPath" type="hidden" uphidinputname="qu_${en.quType }_${en.id }" >
																</span>
                      <!-- The global progress bar -->
                      <div class="progress" style="display: none;margin-top: 10px;">
                        <div class="progress-bar progress-bar-success"></div>
                      </div>
                      <!-- The container for the uploaded files -->
                      <div class="files"></div>
                    </div>
                    <input type="hidden" class="answerTag" value="0" >
                    <div class="dwComEditMenuBtn" ></div>
                  </div>
                </li>
              </ul>
              </div>
            </div>

          </div>
        </div>
      </li>
    </ul>
    <ul id="dwSubmitModel">
      <li class="li_surveyQuItemBody surveySubmitBody" style="padding-top:20px;"  >
        <div class="surveyQuItemBody">
          <div class="surveyQuItem">
            <div id="jcaptchaImgBody" class="r-qu-body" style="display: none;">
              <div class="frmItem" >
                <label class="frm_label">验证码</label>
                <div class="frm_controls" style="padding-bottom: 10px;">
											<span class="auth-code" id="verifycodeImgArea">
											<input name="jcaptchaInput" type="text" class="jcaptchaInput" autocomplete="off" >
											<img id="register-jcaptchaImg" onclick="refreshAutoCode('register-jcaptchaImg')" src="/dwsurvey/bk/api/dwsurvey/anon/jcap/jcaptcha.do" alt="验证码" ></span>
                  <a href="javascript:refreshAutoCode('register-jcaptchaImg');" style="margin-left: 5px;" hidefocus="true">换一张</a>
                  <span class="frm_desc">验证码，区分大小写</span>
                  <p class="valid-msg fail" style="display: none;"><i>●</i><span class="msg_content">验证码错误，请重新输入</span></p>
                </div>
              </div>
              <div class="errorItem" style="display: none;"><label class="error">验证码错误，请重新输入！</label></div>
            </div>
            <input type="hidden" class="quType" value="submitSurveyBtn">
            <div class="surveyQuItemContent" style="margin-bottom: 0px;min-height:20px;">
              <input type="button" class="submitSurvey" id="submitSurvey" value="提交" data-theme="b" />
              <input type="hidden" name="prevPageNo" value="${pageNo-1 }">
              <input type="hidden" name="nextPageNo" value="${pageNo }">
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
  <div id="quLogicItemModel">
    <div class="quLogicItem">
      <input type="hidden" class="cgQuItemId" value="${quLogicEn.cgQuItemId }"/>
      <input type="hidden" class="skQuId" value="${quLogicEn.skQuId }"/>
      <input type="hidden" class="logicId" value="${quLogicEn.id }"/>
      <input type="hidden" class="geLe" value="${quLogicEn.geLe }"/>
      <input type="hidden" class="scoreNum" value="${quLogicEn.scoreNum }"/>
      <input type="hidden" class="logicType" value="${quLogicEn.logicType }"/>
    </div>
  </div>
  <ul id="quRadioItem_default">
    <li class="quCoItemUlLi">
    <div class="dwQuOptionItemContent">
      <label class="dwRedioStyle dwQuInputLabel" ></label>
      <input id="qu_${en.quType }_${en.id }_${item.id}" type="radio" name="qu_${en.quType }_${en.id }" value="${item.id }">
      <label class="editAble" >${item.optionName }</label>
      <input type='text' class='inputSytle_1 dwQuOptionItemNote'  style="display: none;"   name="text_qu_${en.quType }_${en.id }_${item.id }"  />
      <div class="quItemInputCase">
        <input type="hidden" class="isNote" value="${item.isNote }">
        <input type="hidden" class="checkType" value="${item.checkType }">
        <input type="hidden" class="isRequiredFill" value="${item.isRequiredFill }">
      </div>
    </div>
    </li>
  </ul>
  <div id="radioSelect_content" >
    <div class="quCoItem radioSelect_coitem">
      <ul>
        <li class="quCoItemUlLi">
          <div class="radioSelectSet">
            <select class="radioSelect"  name="qu_${en.quType }_${en.id }" >
              <option value="0">--请选择--</option>
            </select>
          </div>
        </li>
      </ul>
    </div>
  </div>
  <div id="radioSelectOption">
    <div class="quItemInputCase" itemid="${item.id }">
      <input type="hidden" class="isNote" value="${item.isNote }">
      <input type="hidden" class="checkType" value="${item.checkType }">
      <input type="hidden" class="isRequiredFill" value="${item.isRequiredFill }">
    </div>
    <div><input type='text' class='inputSytle_1 dwQuOptionItemNote'  style="display: none;"   name="text_qu_${en.quType }_${en.id }_${item.id }"  /></div>
  </div>
  <ul id="quCheckboxItem_default">
    <li class="quCoItemUlLi">
      <div class="dwQuOptionItemContent">
        <label class="dwCheckboxStyle dwQuInputLabel" ></label>
        <input id="tag_qu_${en.quType }_${en.id }_${item.id }" type="checkbox" name="tag_qu_${en.quType }_${en.id }_${item.id }"  value="${item.id }" >
        <label class="editAble" >${item.optionName }</label>
        <input type='text' class='inputSytle_1 dwQuOptionItemNote' name="text_tag_qu_${en.quType }_${en.id }_${item.id }" style="display: none;" />
        <div class="quItemInputCase">
          <input type="hidden" class="isNote" value="${item.isNote }">
          <input type="hidden" class="checkType" value="${item.checkType }">
          <input type="hidden" class="isRequiredFill" value="${item.isRequiredFill }">
        </div>
      </div>
    </li>
  </ul>
  <div id="quScoreItemModel">
      <div class="scoreRow quScoreOptionTr quOptionItemRow">
        <input class="dwScoreOptionId" value="${item.id }" disabled="disabled" type="hidden"/>
        <input type="hidden" class="answerTag" value="0" >
        <div class="dwsurvey-controlgroup starRating" >
          <div class="starOptionTitle" ><label class="editAble">${item.optionName }</label></div>
          <div class="starOptionContent" >  </div>
        </div>
        <input name="item_qu_${en.quType }_${en.id }_${item.id }" value=""  type="hidden" class="scoreNumInput" >
      </div>
  </div>
  <div id="quOrderItemLeftModel">
    <div class="ui-checkbox m_clickQuOrderItem">
      <label class="ui-btn ui-corner-all ui-btn-inherit itemOptionname editAble"  style="text-align: left;" >${item.optionName }</label>
      <div class="m_orderby_num">0</div>
      <div style="display: none;">
        <input  name="item_qu_${en.quType }_${en.id }_${item.id }"  value="0" type="hidden" class="quOrderItemHidInput" >
      </div>
    </div>
  </div>
  <div id="mFillblankTableModel" >
      <div class="mFillblankTableTr">
        <label  class="editAble" >${item.optionName }</label>
        <input id="text_qu_${en.quType }_${en.id }_${item.id }" name="text_qu_${en.quType }_${en.id }_${item.id }"  type="text" class="dwMFillblankInput inputSytle_1 " >
        <input class="dwMFillblankOptionId" value="${item.id }" disabled="disabled" type="hidden"/>
        <input type="hidden" class="answerTag" value="0" >
      </div>
  </div>
</div>
<script type="text/javascript">
	var bfbFloat=0;
	$("#resultProgress").progressbar({value: bfbFloat,orientation: "vertical"});
var errorcode="${param['errorcode']}";
if(errorcode=="3"){
	var errorHtml="<div class=\"errorItem\" style=\"padding-left:30px;padding-top:10px;\" ><label for=\"\" class=\"error\">验证码不正确，请重新回答！</label></div>";
	$("#m-errorMsg").append(errorHtml);
}
</script>
</body>
</html>
