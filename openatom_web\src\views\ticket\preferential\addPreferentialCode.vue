<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    :visible.sync="showDialog"
    :title="dialogTitle"
    width="600px"
    @close="cancelDialog"
    :destroy-on-close="true"
  >
    <div class="tableBtnGroup" v-if="!this.dialogData.id"></div>
    <el-form
      ref="dialogData"
      :model="dialogData"
      :rules="rules"
      label-width="100px"
      class="dialogFormDataAlert"
      @submit.native.prevent
    >
      <el-form-item label="优惠码" prop="couponCode">
        <el-input
          v-model.trim="dialogData.couponCode"
          disabled
          type="text"
          maxlength="50"
          show-word-limit
          placeholder="点击上方按钮生成优惠码"
        >
          <el-button
            slot="append"
            @click="getCouponCode"
            :disabled="this.dialogData.id ? true : false"
            :style="
              this.dialogData.id ? '' : 'background-color: #1890ff; color: #fff'
            "
            >更新优惠码</el-button
          >
        </el-input>
      </el-form-item>
      <el-form-item label="票种" prop="ticketId">
        <el-select
          style="width: 100%"
          size="small"
          v-model.trim="dialogData.name"
          clearable
          placeholder="请选择票种"
          @focus="getTicketType"
          @change="checkTicketType"
          :disabled="this.dialogData.id ? true : false"
        >
          <el-option
            v-for="item in ticket"
            :key="item.id"
            :label="item.name + '(' + item.price + ')'"
            :value="{ id: item.id, price: item.price, name: item.name }"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="金额" prop="couponPrice">
        <el-input
          v-model.trim="dialogData.couponPrice"
          :disabled="this.dialogData.id ? true : false"
          type="text"
          show-word-limit
          placeholder="请输入金额"
        />
      </el-form-item>
      <el-form-item label="总使用次数" prop="totalCount">
        <el-input
          v-model.trim="dialogData.totalCount"
          type="text"
          show-word-limit
          placeholder="请输入总使用次数"
        />
      </el-form-item>
      <el-form-item label="所属单位" prop="company">
        <el-input
          v-model.trim="dialogData.company"
          type="text"
          maxlength="50"
          show-word-limit
          placeholder="请输入所属单位"
        />
      </el-form-item>
      <div class="formBtn" style="text-align: center">
        <el-button
          type="primary"
          @click.stop="submitDialog"
          :loading="btnloading"
          >提交</el-button
        >
        <el-button @click="cancelDialog">取消</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>
<script>
import { checkPrice, checkStock } from "@/utils/validate";
import ticketType, { getTicketTypeList } from "@/api/ticket/ticketType";
import coupon from "@/api/ticket/preferential/index";
export default {
  name: "AddPreferentialCode",
  components: {},
  props: {
    showDialog: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    dialogTitle: {
      type: String,
      default: () => {
        return "";
      },
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  watch: {
    rowData(newVal, _oldVal) {
      if (newVal) {
        this.dialogData = newVal;
      }
    },
    showDialog(newVal, _oldVal) {
      if (newVal) {
        this.isVisible = newVal;
      }
    },
  },
  created() {
  },
  mounted() {
  },
  data() {
    return {
      activityId: JSON.parse(sessionStorage.getItem("activity")).activityId,
      ticket: [], // 票种数据
      isVisible: false,
      dialogData: {
        id: "",
        price: "",
        couponCode: "",
        ticketId: "",
        couponPrice: null,
        totalCount: null,
        company: "",
        name: "",
      },
      rules: {
        couponCode: [
          { required: true, message: "请先生成优惠码", trigger: "blur" },
        ],
        ticketId: [
          { required: true, message: "请输入票种名称", trigger: "blur" },
        ],
        couponPrice: [
          { required: true, message: "请输入票种价格", trigger: "blur" },
          { required: true, validator: checkPrice, trigger: "blur" },
        ],
        totalCount: [
          { required: true, message: "请输入总使用次数", trigger: "blur" },
          { required: true, validator: checkStock, trigger: "blur" },
        ],
      },
      btnloading: false,
    };
  },
  methods: {
    getTicketType() {
      let obj = {
        activityid: this.activityId,
      };
      ticketType.getTicketTypeList(obj).then((res) => {
        if (res.code === 200) {
          this.ticket = Array.from(res.rows);
        }
      });
    },
    checkTicketType(val) {
      this.dialogData.price = val.price;
      this.dialogData.ticketId = val.id;
      this.dialogData.name = val.name + "(" + val.price + ")";
    },
    // 获取优惠码
    getCouponCode() {
      coupon.getGenerateCode().then((res) => {
        if (res.code === 200) {
          this.dialogData.couponCode = res.msg;
        }
      });
    },
    submitDialog() {
      this.$refs.dialogData.validate(async (valid) => {
        let data = {
          id: this.dialogData.id,
          couponCode: this.dialogData.couponCode,
          ticketId: this.dialogData.ticketId,
          couponPrice: this.dialogData.couponPrice,
          totalCount: this.dialogData.totalCount,
          company: this.dialogData.company,
        };
        if (valid) {
          this.btnloading = true;
          if (this.dialogData.id) {
            if (this.dialogData.usedCount > data.totalCount) {
              this.$message({
                type: "error",
                message: "使用次数不能小于已用张数",
              });
              this.btnloading = false;
            } else {
              coupon
                .updateGenerateCode(data)
                .then((res) => {
                  if (res.code === 200) {
                    this.$parent.resetSearch();
                    this.$message({
                      type: "success",
                      message: "修改成功",
                    });
                    this.cancelDialog();
                    this.btnloading = false;
                  } else {
                    this.btnloading = false;
                  }
                })
                .catch(() => {
                  this.btnloading = false;
                });
            }
            // console.log(this.dialogData)
          } else {
            coupon
              .saveGenerateCode(data)
              .then((res) => {
                if (res.code === 200) {
                  this.$parent.resetSearch();
                  this.$message({
                    type: "success",
                    message: "新增成功",
                  });
                  this.cancelDialog();
                  this.btnloading = false;
                } else {
                  this.btnloading = false;
                }
              })
              .catch(() => {
                this.btnloading = false;
              });
          }
        }
      });
    },
    cancelDialog() {
      this.dialogData = {
        couponCode: "",
        ticketId: "",
        couponPrice: null,
        totalCount: null,
        company: "",
      };
      this.$refs.dialogData.resetFields();
      this.isVisible = false;
      this.$emit("cancelDialogFun", false);
    },
  },
};
</script>

<style scoped lang="scss">
.tableBtnGroup {
  margin: 0 0 20px 0;
}
</style>

