<template>
  <div class="chart-content">
    <div class="top-chart"></div>
    <el-table size="mini" :data="tableData">
      <el-table-column label="" prop="name"></el-table-column>
      <el-table-column
        v-for="(item, index) in xData"
        :key="index"
        :label="item"
        :prop="item"
      ></el-table-column>
    </el-table>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { queryAmount } from "@/api/statistical/index";
export default {
  props: {},
  components: {},
  data() {
    return {
      chart: null,
      activityId: "",
      chartData: null,
      yDataBar: [],
      yDataLineAct: [],
      yDataLineShould: [],
      xData: [],
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      tableData: [],
      option: {
        legend: {
          data: ["票款应付总金额", "票款实付总金额"],
        },
        tooltip: {
          show: true,
          trigger: "axis",
          axisPointer: {
            type: "line",
          },
          // formatter: " {b}<br/>  {c} {c1} {c2}",
        },
        grid: { left: "16%", right: "10%", bottom: "10%", top: "18%" },
        title: {
          text: "票款统计",
          left: "top",
        },
        xAxis: {
          type: "category",
          data: [],
        },
        yAxis: [
          // {
          //   name: "单位：张",
          //   type: "value",
          //   axisLine: {
          //     show: true,
          //   },
          // },
          {
            name: "单位：元",
            type: "value",
            axisLine: {
              show: true,
            },
          },
        ],
        series: [
          {
            name: "票款实付总金额",
            stack: "total",
            yAxisIndex: 0,
            smooth: true,
            label: {
              show: true,
              position: "right",
              valueAnimation: true,
            },
            lineStyle: {
              normal: {
                color: "rgba(11, 255, 165, 1)",
                shadowBlur: 3,
                shadowColor: "rgba(11, 255, 165, 0.4)",
                shadowOffsetY: 8,
              },
            },
            data: [],
            type: "bar",
            barWidth: 20,
          },
          {
            name: "票款应付总金额",
            stack: "total",
            yAxisIndex: 0,
            smooth: true,
            label: {
              show: true,
              position: "left",
              valueAnimation: true,
            },
            lineStyle: {
              normal: {
                color: "rgba(11, 105, 255, 1)",
                shadowBlur: 3,
                shadowColor: "rgba(11, 105, 255, 0.4)",
                shadowOffsetY: 8,
              },
            },
            data: [],
            type: "bar",
            barWidth: 20,
          },
        ],
      },
    };
  },
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activityId = activeInfo.activityId;
  },
  destroyed() {},
  mounted() {
    this.initChart();
  },
  methods: {
    async initChart() {
      let res = await queryAmount(this.activityId);

      this.chartData = res.data;

      let ticketCount = {};
      let ticketActAmount = {};
      let ticketShouldAmount = {};
      let data = [ticketCount, ticketShouldAmount, ticketActAmount];
      for (const key in res.data) {
        if (Object.hasOwnProperty.call(res.data, key)) {
          console.log(res.data);
          const element = res.data[key];
          this.xData.push(key);
          this.yDataBar.push(element.totalCounts);
          this.yDataLineAct.push(element.actAmount);
          this.yDataLineShould.push(element.payableAmount);
          ticketCount[key] = element.totalCounts;
          ticketActAmount[key] = element.actAmount;
          ticketShouldAmount[key] = element.payableAmount;
        }
      }
      ticketCount.name = "票数";
      ticketShouldAmount.name = "票款应付总金额";
      ticketActAmount.name = "票款实付总金额";
      if (JSON.stringify(res.data) !== "{}") {
        this.tableData = data;
      }
      this.drawChart();
    },
    drawChart() {
      this.chart = echarts.init(
        document.getElementsByClassName("top-chart")[3]
      );
      this.option.series[0].data = this.yDataLineAct;
      this.option.series[1].data = this.yDataLineShould;
      this.option.xAxis.data = this.xData;
      this.chart.setOption(this.option);
    },
  },
};
</script>
<style lang='scss'></style>
