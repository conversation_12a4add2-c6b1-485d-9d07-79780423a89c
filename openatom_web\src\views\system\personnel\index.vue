<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="100px"
      v-if="activeName == 0"
    >
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输人员姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输人员手机号"
          clearable
        />
      </el-form-item>
      <el-form-item label="票种" prop="ticketType">
        <el-select
          v-model="queryParams.ticketType"
          placeholder="请选择票种"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="dict in TicketTypeOPtion"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审批状态" prop="approveStatus">
        <el-select
          v-model="queryParams.approveStatus"
          placeholder="请选择审批状态"
          clearable
          style="width: 200px"
        >
          <el-option :label="'审批中'" :value="'审批中'" />
          <el-option :label="'审批驳回'" :value="'审批驳回'" />
          <el-option :label="'审批通过'" :value="'审批通过'" />
        </el-select>
      </el-form-item>
      <el-form-item label="渠道来源" prop="channelCode">
        <el-select
          v-model="queryParams.channelCode"
          placeholder="请选择渠道来源"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in channelCodeList"
            :key="dict.channelCode"
            :label="dict.channelName"
            :value="dict.channelCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报名开始日期" prop="startTime">
        <el-date-picker
          size="small"
          v-model="queryParams.startTimeStr"
          type="datetime"
          placeholder="选择开始日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="报名结束日期">
        <el-date-picker
          size="small"
          v-model="queryParams.endTimeStr"
          type="datetime"
          placeholder="选择结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="来源" prop="regFrom">
        <el-select
          v-model="queryParams.regFrom"
          placeholder="请选择来源"
          clearable
          style="width: 200px"
        >
          <el-option :label="'PC端报名'" :value="'1'" />
          <el-option :label="'手机端报名'" :value="'2'" />
          <el-option :label="'管理员导入'" :value="'3'" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleDownTemp"
          v-hasPermi="['activity:reg:download']"
          >导出</el-button
        >
        <!-- <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleDown"
          v-hasPermi="['activity:reg:download']"
          >模板下载</el-button
        > -->
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handlePerson"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['activity:reg:upload']"
          >导入</el-button
        >
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="this.useIds.length>0?false:true"
          @click="handleDeleteUser"
          v-hasPermi="['activity:reg:delete']"
          >删除</el-button
        >
        <!-- <el-upload
          class="upload-demo"
          :action="local + '/reg/upload'"
          :data="queryParams"
          :headers="headers"
          :on-success="upSuccess"
          :limit="1"
          :file-list="fileList"
          v-hasPermi="['activity:reg:upload']"
        >
          <el-button
            type="primary"
            plain
            icon="el-icon-upload2"
            size="mini"
            @click="handleAdd"
            >导入</el-button
          >
          <div slot="tip" class="el-upload__tip">
            只能上传xlsx文件，且不超过10M
          </div>
        </el-upload> -->
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :element-loading-text="refoundText"
      :data="approvalList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="50"
        align="center"
        :selectable="selected"
      />
      <el-table-column
        label="活动名称"
        align="center"
        prop=""
        width="150px"
        :show-overflow-tooltip="true"
      >
        {{ this.queryParams.active_Name }}
      </el-table-column>
      <el-table-column
        label="渠道推广"
        align="center"
        prop="channelCodeName"
        width="100px"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="{ row }">
          {{
            row.channelCodeName && row.channelCodeName != ""
              ? row.channelCodeName
              : "默认"
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="来源"
        align="center"
        prop="regFromName"
        width="120px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="activetyInfo.isBuyTicket == 1"
        label="订单号"
        align="center"
        prop="orderNum"
        width="120px"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="票种"
        align="center"
        prop="ticketName"
        width="150px"
        :show-overflow-tooltip="true"
      />
      <template v-for="item in formRegular">
        <el-table-column
          v-if="item.open"
          :resizable="true"
          :key="item.defaultName"
          :label="item.title"
          :prop="item.defaultName"
          :show-overflow-tooltip="true"
        ></el-table-column>
      </template>
      <template v-for="item in formJSon">
        <!-- v-bind="c" -->
        <el-table-column
          :resizable="true"
          :key="item.field"
          :label="item.title"
          :prop="item.field"
          width="150px"
          :show-overflow-tooltip="true"
        ></el-table-column>
      </template>
      <el-table-column
        label="报名时间"
        align="center"
        prop="createTime"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        label="审批状态"
        align="center"
        prop="approveStatus"
        :show-overflow-tooltip="true"
        fixed="right"
      >
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200px"
        fixed="right"
      >
        <!-- v-hasPermi="['system:post:edit']" -->
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['activity:reg:update']"
            >修改</el-button
          >
          <!-- v-hasPermi="['system:post:remove']" -->
          <el-button
            v-if="activetyInfo && activetyInfo.isBuyTicket == 1"
            type="text"
            icon="el-icon-edit"
            size="mini"
            @click="handleRefuned(scope.row)"
            v-hasPermi="['activity:reg:update']"
            :disabled="
              scope.row.regFrom != 3 && scope.row.approveStatus == '审批通过'
                ? false
                : true
            "
            >退票</el-button
          >
          <el-button
            type="text"
            icon="el-icon-delete"
            size="mini"
            @click="handleDelete(scope.row)"
            v-hasPermi="['activity:reg:delete']"
            :disabled="scope.row.regFrom == 3 ? false : true"
            >删除</el-button
          >
          <el-tooltip
            class="item"
            effect="dark"
            placement="top-start"
            v-if="scope.row.regFrom != 3"
          >
            <template slot="content">
              <div>仅管理员导入的可删除</div>
            </template>
            <i class="el-icon-warning-outline" style="padding-right: 5px"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      custom-class="selfDialog"
      :title="'导入'"
      :visible.sync="upLoadOpen"
      width="500px"
      append-to-body
      :destroy-on-close="true"
      @close="uploadCancel"
    >
      <el-form
        ref="upLoadform"
        :model="uploadform"
        label-width="150px"
        :inline="true"
        :rules="uploadRules"
      >
        <el-form-item
          label="是否发送短信邮件:"
          prop="isSend"
          style="width: 100%"
        >
          <el-radio-group
            v-model="uploadform.isSend"
            size="small"
            style="width: 100%"
          >
            <el-radio :label="true" :value="true">是</el-radio>
            <el-radio :label="false" :value="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="附件:" prop="file" style="width: 100%">
          <el-upload
            class="upload-demo"
            :auto-upload="false"
            :action="local + '/reg/upload'"
            :file-list="fileList"
            :limit="1"
            :on-change="handleUploadChange"
            :on-remove="handleRemove"
          >
            <el-button type="primary" plain icon="el-icon-upload2" size="mini"
              >导入</el-button
            >
            <a style="color: #1890ff" @click.stop="handleDown">模板下载</a>
            <div slot="tip" class="el-upload__tip">
              只能上传xlsx文件，且不超过10M
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="SubFormUpload">提交</el-button>
        <el-button @click="uploadCancel">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="selfDialog"
      :title="flowform.id && flowform.id != '' ? '编辑' : '新增'"
      :visible.sync="UserOpen"
      width="800px"
      append-to-body
      :destroy-on-close="true"
      @close="cancel"
    >
      <el-collapse v-model="activepanel" @change="handleChange">
        <el-collapse-item title="报名人信息" name="useInfo">
          <div id="self" style="height: 500px; overflow: auto">
            <el-form
              ref="flowform"
              :model="flowform"
              label-width="100px"
              :inline="false"
              class="selfForm"
              :label-position="'top'"
              :rules="prewRules"
            >
              <el-form-item label="票种" prop="ticketType">
                <el-select
                  v-model="flowform.ticketType"
                  placeholder="请选择票种"
                  style="width: 100%"
                  :disabled="flowform.id && flowform.id != '' ? true : false"
                >
                  <el-option
                    v-for="dict in TicketOPtion"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="人员类型"
                prop="userType"
                v-if="formRegular[0].open"
              >
                <el-select
                  v-model="flowform.userType"
                  placeholder="请选择人员类型"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in userOption"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="姓名" prop="name" v-if="formRegular[1].open">
                <el-input v-model="flowform.name" />
              </el-form-item>
              <el-form-item label="性别" prop="sex" v-if="formRegular[4].open">
                <el-radio-group v-model="flowform.sex" size="small">
                  <el-radio label="男" border>男</el-radio>
                  <el-radio label="女" border>女</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                label="国籍"
                prop="nationality"
                v-if="formRegular[5].open"
              >
                <el-radio-group v-model="flowform.nationality" size="small">
                  <el-radio label="中国" border>中国</el-radio>
                  <el-radio label="外国籍" border>外国籍</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                label="省市"
                prop="nationalityCity"
                v-if="formRegular[6].open"
              >
                <el-cascader
                  style="width: 100%"
                  v-model="flowform.nationalityCity"
                  :options="provinceCityOption"
                  clearable
                ></el-cascader>
              </el-form-item>
              <el-form-item
                label="手机号"
                prop="phone"
                v-if="formRegular[2].open"
              >
                <el-input v-model="flowform.phone" />
              </el-form-item>
              <el-form-item
                label="邮箱"
                prop="email"
                v-if="formRegular[3].open"
              >
                <el-input v-model="flowform.email" />
              </el-form-item>
              <el-form-item
                label="证件类型"
                prop="cardTypeName"
                v-if="formRegular[8].open"
              >
                <el-select
                  v-model="flowform.cardType"
                  placeholder="请选择证件类型"
                  clearable
                  style="width: 100%"
                >
                  <el-option value="1" label="身份证"></el-option>
                  <el-option value="2" label="护照"></el-option>
                  <el-option value="3" label="港澳通行证"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="证件号"
                prop="idCard"
                v-if="formRegular[8].open"
              >
                <el-input v-model="flowform.idCard" />
              </el-form-item>
              <el-form-item
                label="工作单位"
                prop="company"
                v-if="formRegular[10].open"
              >
                <el-input v-model="flowform.company" />
              </el-form-item>
              <el-form-item label="职务" prop="job" v-if="formRegular[11].open">
                <el-input v-model="flowform.job" />
              </el-form-item>
            </el-form>
            <div
              slot="footer"
              class="dialog-footer"
              v-if="formCreateRule.length == 0"
              style="text-align: center"
            >
              <el-button type="primary" @click="SubForm">提交</el-button>
            </div>
            <form-create
              :rule="formCreateRule"
              :value="formValue"
              :option="option"
            ></form-create>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
import formCreate from "@form-create/iview";
import { getToken, getAccesToken } from "@/utils/auth";
import { MessageBox } from "element-ui";
import provinceCity from "../../activity/formCreat/provinceCity.js";
import { develop } from "@/utils/baseKey.js";
export default {
  name: "personnel",
  data() {
    return {
      // /dev-api
      //window.location.origin
      upLoadOpen: false,
      activetyInfo: JSON.parse(sessionStorage.getItem("activity")),
      // local: process.env.VUE_APP_BASE_API,
      local:
        develop() == "test"
          ? "/atomgit/bk"
          : develop() == "prod"
          ? "/atomgit/bk"
          : develop() == "prew"
          ? "/atomgit/bk"
          : "/dev-api",
      refoundText: null,
      headers: {
        Authorization: "Bearer " + getToken(),
        access_token: getAccesToken(),
      },
      activeName: "0",
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: undefined,
        valid: 1,
        dealIdCard: false,
        // needApp: "true",
      },
      activeOption: [],
      userOption: [],
      approvalList: [],
      loading: false,
      total: 0,
      UserOpen: false,
      activepanel: "useInfo",
      formParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: undefined,
      },
      formCreateRule: [],
      formValue: {},
      flowform: {},
      uploadform: {
        isSend: undefined,
        file: undefined,
      },
      option: {
        form: { labelPosition: "top" },
        submitBtn: { show: true },
        onSubmit: (formData) => {
          this.SubForm(formData);
        },
      },
      appform: {},
      apprule: {
        action: [
          { required: true, message: "请选择审批结果", trigger: "blur" },
        ],
        remarks: [
          { required: true, message: "请填写审批意见", trigger: "blur" },
        ],
      },
      waitList: [],
      apploading: false,
      formJSon: [],
      fileList: [],
      formRegular: [
        {
          open: false,
          required: false,
          value: undefined,
          sort: 1,
          defaultName: "userTypeName",
          title: "用户类型",
        },
        {
          open: true,
          required: true,
          value: undefined,
          sort: 2,
          defaultName: "name",
          title: "姓名",
        },
        {
          open: true,
          required: true,
          value: undefined,
          sort: 3,
          defaultName: "phone",
          title: "手机号码",
        },
        {
          open: true,
          required: true,
          value: undefined,
          sort: 4,
          defaultName: "email",
          title: "邮箱",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 5,
          defaultName: "sex",
          title: "性别",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 6,
          defaultName: "nationality",
          title: "国籍",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 7,
          defaultName: "province",
          title: "省份",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 8,
          defaultName: "city",
          title: "城市",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 9,
          defaultName: "cardTypeName",
          title: "证件类型",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 10,
          defaultName: "idCard",
          title: "证件号码",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 11,
          defaultName: "company",
          title: "工作单位",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 12,
          defaultName: "job",
          title: "职务",
        },
      ],
      provinceCityOption: [],
      TicketTypeOPtion: [],
      channelCodeList: [],
      uploadRules: {
        isSend: [{ required: true, message: "请选择", trigger: "change" }],
        file: [{ required: true, message: "请上传附件", trigger: "change" }],
      },
      prewRules: {
        ticketType: [
          { required: true, message: "票种不能为空", trigger: "blur" },
        ],
        name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
        phone: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
          {
            pattern: /^1[3456789]\d{9}$/,
            message: "请输入正确的手机号",
            trigger: "blur",
          },
        ],
        email: [
          { required: true, message: "邮箱不能为空", trigger: "blur" },
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: "blur",
          },
        ],
        userType: [
          { required: true, message: "用户类型不能为空", trigger: "blur" },
        ],
      },
      timer: null,
      TicketOPtion: [],
      files: null,
      useIds:[]
    };
  },
  destroyed() {
    clearInterval(this.timer);
    this.timer = null;
  },
  watch: {
    "queryParams.activityId": {
      handler(val, oldval) {
        if (val != oldval) {
          this.getList();
          this.queryParams.activityId = val;
          this.getFromList();
        }
      },
      // immediate: true,
    },
  },
  beforeCreate() {},
  created() {
    this.provinceCityOption = provinceCity;
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.queryParams.activityId = activeInfo.activityId;
    this.queryParams.active_Name = activeInfo.activityName;
  },
  mounted() {
    this.getuserType();
    this.getActiveList();
    this.getTicketType();
    this.getChannelLisy();
  },
  update() {
    //this.getActiveList();
  },
  methods: {
    handleDeleteUser() {
      this.$modal
        .confirm('是否确认删除所选中的用户？')
        .then(() => {
          request({
            url: "reg/deleteBatch",
            method: "post",
            data:this.useIds
          })
            .then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("操作成功");
                this.getList();
              }
            })
            .catch((err) => {
              console.log(err);
            });
          // return delPost(obj);
        });
    },
    handleSelectionChange(selection) {
       this.useIds = selection.map((item) => item.id);
    },
    selected(row) {
      if (row.regFrom == 3) {
        return true;
      } else {
        return false;
      }
    },
    uploadCancel() {
      this.upLoadOpen = false;
      this.uploadform = {
        isSend: undefined,
        file: undefined,
      };
      this.files = null;
    },
    SubFormUpload() {
      this.$refs["upLoadform"].validate((valid) => {
        if (valid) {
          this.uploadform.activityId = this.queryParams.activityId;
          const formData = new FormData();
          formData.append("file", this.files ? this.files.raw : undefined);
          formData.append("activityId", this.queryParams.activityId);
          formData.append("isSend", this.uploadform.isSend);
          request({
            url: "/reg/upload",
            method: "post",
            data: formData,
          })
            .then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("操作成功");
                this.uploadCancel();
                this.getList();
              }
            })
            .catch((err) => {
              console.log(err);
            });
        }
      });
    },
    handleUploadChange(file, fileList) {
      let flag = true;
      const imgType = file.name.endsWith(".xlsx");
      const isLt500k = file.size / 1024 / 1024 < 10;
      if (!imgType) {
        flag = false;
        this.fileList = [];
        this.$message.error("附件只能是 xlsx 格式!");
        return false;
      }
      if (!isLt500k) {
        flag = true;
        this.$message.error("附件大小不能超过 10M!");
        this.fileList = [];
        return false;
      }
      if (flag) {
        this.uploadform.file = file.name;
        this.files = file;
      }
    },
    handleRemove() {
      this.uploadform.file = undefined;
      this.files = undefined;
    },
    handleRefuned(row) {
      this.$confirm("确定要对用户为" + row.name + "的发起退票？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      }).then(() => {
        request({
          url: "/order/manage/list",
          method: "get",
          params: {
            pageNum: 1,
            pageSize: 10,
            activityId: this.queryParams.activityId,
            orderNum: row.orderNum,
          },
        })
          .then((response) => {
            if (response.code == 200) {
              if (response.rows && response.rows.length > 0) {
                let url = "";
                if (response.rows[0].paidMethod == 1) {
                  //微信支付退款
                  url = "/wxrefund/refund";
                } else if (response.rows[0].paidMethod == 2) {
                  //支付宝支付退款
                  url = "alirefund/refund";
                }
                if (url != "") {
                  this.refundConfig(url, response.rows[0]);
                }
              }
            } else {
            }
          })
          .catch((err) => {
            console.log(err);
          });
      });
    },
    refundConfig(url, row) {
      this.loading = true;
      this.refoundText = "退票中...";
      request({
        url: url,
        method: "post",
        data: { order: { orderNum: row.orderNum }, reason: "用户主动退款" },
      })
        .then((data) => {
          if (data.code === 200) {
            this.getOrderData(row);
          } else {
            this.loading = false;
            this.refoundText = null;
            if (data.code === 5005) {
              this.$message.success(data.msg);
            } else {
              this.$message.error("退票异常");
            }
          }
        })
        .catch((err) => {
          console.log(err);
          this.loading = false;
          this.refoundText = null;
          this.$message.error("退票异常");
        });
    },
    getChannelLisy() {
      request({
        url: "/channel/list",
        method: "post",
        data: {
          activityId: this.queryParams.activityId,
        },
      })
        .then((response) => {
          let obj = {
            channelCode: "null",
            channelName: "默认",
          };
          response.data.unshift(obj);
          this.channelCodeList = response.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getTicketType() {
      request({
        url: "/product/manage/list",
        method: "post",
        data: { activityid: this.queryParams.activityId },
      })
        .then((response) => {
          let typeOption = [];
          let TicketOPtion = [];
          response.rows.map((item) => {
            typeOption.push(item);
            if (this.activetyInfo.isBuyTicket == 1) {
              if (item.isSales == 1 && item.isShow == 1) {
                TicketOPtion.push(item);
              }
            } else {
              TicketOPtion = typeOption;
            }
          });
          this.TicketOPtion = TicketOPtion;
          this.TicketTypeOPtion = typeOption;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    upSuccess(response) {
      if (response.code == 500) {
        MessageBox.confirm(response.msg, "系统提示", {
          confirmButtonText: "确定",
          // cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {})
          .catch(() => {});
        this.fileList = [];
      }
      if (response.code == 200) {
        this.$modal.msgSuccess("操作成功");
        this.fileList = [];
        this.getList();
      }
    },
    getActiveList() {
      request({
        url: "/activityManage/list",
        method: "post",
        data: { pageNum: 1, pageSize: 10000 },
      })
        .then((response) => {
          this.activeOption = response.rows;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getuserType() {
      request({
        url: "/system/dict/data/list?pageNum=1&pageSize=100&dictType=activity_user_type&status=0",
        method: "get",
      })
        .then((response) => {
          this.userOption = response.rows;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    queryByOutRefundNo(row) {
      let url = "";
      if (row.paidMethod == 2) {
        url =
          "/alirefund/queryByOutRefundNo?refundNo=" +
          row.orderRefund.refundNum +
          "&orderNum=" +
          row.orderNum;
      }
      if (row.paidMethod == 1) {
        url =
          "/wxrefund/queryByOutRefundNo?refundNo=" + row.orderRefund.refundNum;
      }
      request({
        url: url,
        method: "get",
      })
        .then((response) => {
          if (response.code === 200 && response.msg === "SUCCESS") {
            this.$confirm("已成功发起退票,请耐心等候", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              closeOnClickModal: false,
              type: "success",
            })
              .then(async () => {
                this.getList(undefined);
              })
              .catch(() => {
                return false;
              });
            this.loading = false;
            this.refoundText = null;
            if (this.timer) {
              clearInterval(this.timer);
              this.timer = null;
            }
          } else if (response.code === 200 && response.msg == "PROCESSING") {
            if (this.timer === null) {
              this.timer = setInterval(() => {
                this.queryByOutRefundNo(row);
              }, 2000);
            }
          } else {
            this.$message.error("退票失败");
          }
        })
        .catch((error) => {
          console.log(error);
          this.$message.error("退票异常");
        });
    },
    getList(order) {
      this.queryParams.dealIdCard = false;
      this.loading = true;
      if (
        this.queryParams.startTimeStr &&
        this.queryParams.startTimeStr != ""
      ) {
        this.queryParams.startTime =
          this.queryParams.startTimeStr + " 00:00:00";
      } else {
        this.queryParams.startTime = undefined;
      }
      if (this.queryParams.endTimeStr && this.queryParams.endTimeStr != "") {
        this.queryParams.endTime = this.queryParams.endTimeStr + " 23:59:59";
      } else {
        this.queryParams.endTime = undefined;
      }
      request({
        url: "reg/list",
        method: "get",
        params: this.queryParams,
      })
        .then((response) => {
          if (response.code == 200) {
            response.rows.map((item) => {
              if (item.dynamicsJson) {
                Object.keys(item.dynamicsJson).map((val) => {
                  item[val] = item.dynamicsJson[val];
                });
              }
            });

            this.approvalList = response.rows;
            this.total = response.total;
            this.loading = false;
            if (order) {
              if (response.rows.length > 0) {
                response.rows.map((item) => {
                  if (item.orderNum == order) {
                    this.getOrderData(item);
                  }
                });
              }
            }
          } else {
            this.approvalList = [];
            this.total = 0;
            this.loading = false;
          }
        })
        .catch((err) => {
          console.log(err);
          this.loading = false;
        });
    },
    getOrderData(order) {
      request({
        url: "/order/manage/list",
        method: "get",
        params: {
          pageNum: 1,
          pageSize: 10,
          activityId: this.queryParams.activityId,
          orderNum: order.orderNum,
        },
      })
        .then((response) => {
          if (response.code == 200) {
            if (order && order.orderNum) {
              if (response.rows.length > 0) {
                response.rows.map((item) => {
                  if (item.orderNum == order.orderNum) {
                    if (Number(item.paidAmount) == 0) {
                      this.$modal.msgSuccess("操作成功");
                      this.getList();
                    } else {
                      this.queryByOutRefundNo(item);
                    }
                  }
                });
              }
            }
          } else {
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleUpdate(row) {
      this.UserOpen = true;
      setTimeout(() => {
        console.log(row);
        let dynamicsJson = row.dynamicsJson ? row.dynamicsJson : {};
        this.formValue = { ...dynamicsJson };
        this.flowform = row;
        this.flowform.nationalityCity = [row.province, row.city];
        this.appform.activityNo = row.activityId;
      }, 200);
    },
    handlePerson() {
      this.UserOpen = true;
      this.getFromList();
    },
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除姓名为"' + row.name + '"的数据？')
        .then(() => {
          request({
            url: "reg/delete/" + row.id,
            method: "get",
          })
            .then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("操作成功");
                this.getList();
              }
            })
            .catch((err) => {
              console.log(err);
            });
          // return delPost(obj);
        });
    },

    SubForm(formData) {
      this.$refs["flowform"].validate((valid) => {
        if (valid) {
          this.flowform.province =
            this.flowform.nationalityCity && this.flowform.nationalityCity[0]
              ? this.flowform.nationalityCity[0]
              : null;
          this.flowform.city =
            this.flowform.nationalityCity && this.flowform.nationalityCity[1]
              ? this.flowform.nationalityCity[1]
              : null;
          let url = "";
          if (this.flowform.id && this.flowform.id != "") {
            url = "/reg/update";
          } else {
            url = "/reg/save";
            this.flowform.valid = 1;
            this.flowform.approveStatus = "审批通过";
            this.flowform.activityId = this.queryParams.activityId;
            this.flowform.regFrom = "3";
          }
          delete this.flowform.dynamicsJson;
          request({
            url: url,
            method: "post",
            data: {
              id: this.flowform.id,
              dynamicsJson: formData,
              ...this.flowform,
            },
          })
            .then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.UserOpen = false;
              this.getList();
            })
            .catch((err) => {
              console.log(err);
            });
        }
      });
    },
    cancel() {
      this.UserOpen = false;
      this.formValue = {};
      this.flowform = {};
      this.appform = {};
      this.getList();
    },
    getFromList() {
      this.formParams.activityId = this.queryParams.activityId;
      request({
        url: "/form/list",
        method: "get",
        params: this.formParams,
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            let data = [...response.rows[0].formInfo[1]];
            this.formJSon = data;
            this.formCreateRule = data;

            response.rows[0].formInfo[0].map((item) => {
              this.formRegular.map((val) => {
                if (
                  item.defaultName == "provinceCity" ||
                  item.defaultName == "idCardType" ||
                  item.defaultName == "userType"
                ) {
                  if (item.defaultName == "provinceCity") {
                    if (
                      val.defaultName == "province" ||
                      val.defaultName == "city"
                    ) {
                      val.open = item.open;
                      val.required = item.required;
                    }
                  }
                  if (item.defaultName == "idCardType") {
                    if (
                      val.defaultName == "idCard" ||
                      val.defaultName == "cardTypeName"
                    ) {
                      val.open = item.open;
                      val.required = item.required;
                    }
                  }
                  if (item.defaultName == "userType") {
                    if (val.defaultName == "userTypeName") {
                      val.open = item.open;
                      val.required = item.required;
                    }
                  }
                } else {
                  if (item.defaultName == val.defaultName) {
                    val.open = item.open;
                    val.required = item.required;
                  }
                }
              });
            });
          } else {
            this.formJSon = [];
            this.formCreateRule = [];
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getFlowList(data) {
      this.apploading = true;
      request({
        url: "/flow/list?bus_pk=" + data,
        method: "get",
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            response.rows.map((item, index) => {
              item.key = index + 1;
            });
            this.waitList = response.rows;
            this.appform.flowno = response.rows[0].flowno;
            this.appform.bus_pk = data;
          }
          this.apploading = false;
        })
        .catch((err) => {
          this.apploading = false;
          console.log(err);
        });
    },

    handleClick(tab) {
      this.activeName = tab.index;
    },

    handleChange() {},
    handleAdd() {
      this.upLoadOpen = true;
    },
    handleDown(e) {
      this.download(
        "/reg/download",
        {
          activityId: this.queryParams.activityId,
        },
        `报名人员模板_${new Date().getTime()}.xlsx`
      );
    },
    handleDownTemp() {
      let obj = { ...this.queryParams };
      obj.dealIdCard = false;
      delete obj.pageNum;
      delete obj.pageSize;
      this.download(
        "/reg/export",
        {
          ...obj,
        },
        `报名人员数据_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style scoped rel="stylesheet/scss" lang="scss" >
::v-deep .form-create button {
  margin-left: 300px;
}
::v-deep #self .selfForm .el-form-item {
  margin-bottom: 5px;
}
::v-deep .form-create .el-form-item__label {
  // width: 100px !important;
}
::v-deep .form-create .el-form-item__content {
  // margin-left: 100px !important;
}
::v-deep .form-create .el-input {
  // width: 78% !important;
}
::v-deep .form-create .el-select {
  width: 100% !important;
}
::v-deep .el-dialog__body {
  padding-bottom: 0;
  padding-top: 0;
}
.el-tooltip__popper.is-dark {
  min-width: 10px !important;
  max-width: 300px !important;
}
::v-deep .form-create .el-select {
  width: 100%;
}
::v-deep .form-create .el-date-editor {
  width: 100%;
}
</style>