<template>
  <div id="steps_1">
    <el-form :model="stepsForm" ref="stepsForm" label-width="100px">
      <el-form-item label="展商名称" prop="businessCompanyName">
        <span style="color: #606266">{{ steps1.companyName }}</span>
      </el-form-item>
      <el-form-item
        label="展商介绍:"
        prop="businessDesc"
        :rules="rules.businessDesc"
      >
        <el-input
          type="textarea"
          v-model="stepsForm.businessDesc"
          placeholder="请输入展品介绍"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="联系人:"
        prop="contactName"
        :rules="rules.contactName"
      >
        <el-input
          v-model="stepsForm.contactName"
          style="width: 300px"
          placeholder="请输入联系人"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="联系电话:"
        prop="contactPhone"
        :rules="rules.contactPhone"
      >
        <el-input
          v-model.number="stepsForm.contactPhone"
          style="width: 300px"
          placeholder="请输入联系电话"
        ></el-input>
      </el-form-item>
      <el-form-item label="展品:" prop="productStr" :rules="rules.productStr">
        <el-button
          @click="addImg"
          :disabled="
            !(
              steps2.approveStatus == '审批中' ||
              steps2.approveStatus == '审批通过' ||
              steps2.approveStatus == '取消资格'
            )
              ? false
              : true
          "
          >新增展品</el-button
        >
        <el-table
          :data="fileListOld"
          style="width: 98%; margin-top: 20px"
          border
          class="table_steps_3"
        >
          <el-table-column label="展品名称" prop="name" />
          <el-table-column label="展品" prop="url">
            <template slot-scope="scope">
              <img :src="scope.row.url" style="height: 30px" />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="120"
            v-if="
              !(
                steps2.approveStatus == '审批中' ||
                steps2.approveStatus == '审批通过' ||
                steps2.approveStatus == '取消资格'
              )
            "
          >
            <template slot-scope="scope">
              <el-button
                @click.native.prevent="check(scope.$index, scope.row)"
                type="text"
                size="small"
              >
                查看
              </el-button>
              <el-button
                style="color: red"
                @click.native.prevent="deleteImg(scope.$index, scope.row)"
                type="text"
                size="small"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog
          title="添加展品"
          :visible.sync="dialogVisible"
          width="30%"
          :destroy-on-close="true"
        >
          <el-form :model="stepsForm" ref="imgForm" label-width="100px">
            <el-form-item
              label="展品名称"
              prop="name_img"
              :rules="isSure ? null : rulesImg.name_img"
            >
              <el-input
                v-model="stepsForm.name_img"
                placeholder="请输入展品名称"
                :disabled="isSure"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="展品"
              prop="url_img"
              :rules="isSure ? null : rulesImg.url_img"
              style="margin-top: 20px"
            >
              <img
                v-if="isSure"
                :src="this.fileList[0].url"
                style="height: 200px; width: 200px"
              />
              <el-upload
                v-else
                action="string"
                list-type="picture-card"
                :limit="1"
                ref="upload"
                :file-list="fileList"
                :auto-upload="false"
                :on-change="handleChange"
                :on-remove="handleRemove"
                :class="uploadDisabled"
              >
                <i class="el-icon-plus"
                  ><span style="font-size: 14px; vertical-align: middle"
                    >添加展品</span
                  ></i
                >
              </el-upload>
            </el-form-item>
          </el-form>

          <span slot="footer" class="dialog-footer">
            <el-button @click="cancelPro">取 消</el-button>
            <el-button type="primary" @click="imgSure()" v-if="!isSure"
              >确 定</el-button
            >
          </span>
        </el-dialog>
      </el-form-item>
      <el-form-item label="工作人员:">
        <el-table
          :data="stepsForm.workPerson"
          style="width: 98%"
          border
          class="table_steps_3"
          :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
          stripe
        >
          <el-table-column label="姓名">
            <template slot-scope="scope">
              <el-form-item
                :prop="'workPerson.' + scope.$index + '.name'"
                :rules="rules.name"
              >
                <el-input
                  v-model.trim="scope.row.name"
                  placeholder="请输入姓名"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="岗位">
            <template slot-scope="scope">
              <el-form-item
                :prop="'workPerson.' + scope.$index + '.position'"
                :rules="rules.position"
              >
                <el-input
                  v-model.trim="scope.row.position"
                  placeholder="请输入岗位"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="电话">
            <template slot-scope="scope">
              <el-form-item
                :prop="'workPerson.' + scope.$index + '.phone'"
                :rules="rules.phone"
              >
                <el-input
                  v-model="scope.row.phone"
                  placeholder="请输入电话"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="邮箱">
            <template slot-scope="scope">
              <el-form-item
                :prop="'workPerson.' + scope.$index + '.email'"
                :rules="rules.email"
              >
                <el-input
                  v-model="scope.row.email"
                  placeholder="请输入邮箱地址"
                ></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="120"
            v-if="
              !(
                steps2.approveStatus == '审批中' ||
                steps2.approveStatus == '审批通过' ||
                steps2.approveStatus == '取消资格'
              )
            "
          >
            <template slot-scope="scope">
              <el-button
                @click.native.prevent="addRow()"
                type="text"
                size="small"
              >
                新增
              </el-button>
              <el-button
                @click.native.prevent="
                  deleteRow(scope.$index, stepsForm.workPerson)
                "
                type="text"
                size="small"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item class="btn">
        <el-button
          type="primary"
          @click="submitForm('stepsForm')"
          :disabled="
            !(steps2.approveStatus == '审批驳回' || steps2 == '') ||
            activeInfo.activityStatus == 3
          "
          >提交</el-button
        >
        <span
          v-if="activeInfo.activityStatus == 3"
          style="font-size: 12px; margin-left: 12px; color: rgba(255, 0, 26)"
          >*活动已结束无法提交</span
        >
        <span
          v-else-if="!(steps2.approveStatus == '审批驳回' || steps2 == '')"
          style="font-size: 12px; margin-left: 12px; color: rgba(255, 0, 26)"
          >*内容正在审核中或审核已通过无法再次提交</span
        >
      </el-form-item>
    </el-form>
  </div>
</template>
  
<script>
import request from "@/utils/request";
export default {
  name: "steps_3",
  props: ["steps1", "steps2", "activeInfo"],
  data() {
    return {
      dialogVisible: false,
      rules: {
        name: [{ required: true, message: "工作人员姓名不能为空" }],
        position: [{ required: true, message: "工作人员岗位不能为空" }],
        phone: [
          { required: true, message: "工作人员电话不能为空" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请填写正确的手机号",
          },
        ],
        email: [
          { required: true, message: "工作人员邮箱不能为空" },
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],

        businessDesc: [{ required: true, message: "展商介绍不能为空" }],
        contactName: [{ required: true, message: "联系人姓名不能为空" }],
        contactPhone: [
          { required: true, message: "联系人电话不能为空" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请填写正确的手机号",
          },
        ],
        productStr: [{ required: true, message: "展品不能为空" }],
      },
      rulesImg: {
        name_img: [{ required: true, message: "展品名称不能为空" }],
        url_img: [{ required: false, message: "展品不能为空" }],
      },
      stepsForm: {
        // 联系人信息
        businessDesc: "",
        contactName: "",
        contactPhone: "",
        productDetails: [],
        workPerson: [{ name: "", position: "", phone: "", email: "" }],
        name_img: "",
        url_img: "",
        productStr: [],
      },
      fileList: [],
      fileListOld: [],
      files: [],
      delIds: [],
      uploadDisabled: "",
      attachmentId: [],
      imgList: "",
      isSure: false,
      product_id: "",
      isAdd: false,
      editIndex: null,
    };
  },
  created() {
    if (
      localStorage.getItem("activityNo") &&
      localStorage.getItem("activityNo") != ""
    ) {
      if (this.steps2 && this.steps2 != "") {
        this.stepsForm = {
          // 联系人信息
          businessDesc: this.steps2.businessDesc,
          contactName: this.steps2.contactName,
          contactPhone: this.steps2.contactPhone,
          productDetails: [],
          //    productDetails:JSON.parse(this.steps2.productDetails),
          workPerson: JSON.parse(this.steps2.workPerson),
          productStr: "success",
        };
        const fileList = JSON.parse(this.steps2.productDetails);
        this.product_id = fileList[0].product_id;
        fileList.map((item) => {
          this.fileListOld.push({
            name: item.name,
            url: item.image_url,
            id: item.id,
          });
        });
        this.uploadDisabled = "";
      }
    }
  },
  methods: {
    cancelPro() {
      this.dialogVisible = false;
      this.stepsForm.name_img = undefined;
      this.stepsForm.url_img = undefined;
    },
    addRow() {
      this.stepsForm.workPerson.push({
        name: "",
        position: "",
        phone: "",
        email: "",
      });
    },
    deleteRow(index, rows) {
      rows.splice(index, 1);
    },
    deleteImg(index, rows) {
      this.stepsForm.productDetails.splice(index, 1);
      this.fileListOld.splice(index, 1);
      this.delIds.push(rows.id);
      this.files.splice(index, 1);
      if (this.fileListOld.length == 0) {
        this.stepsForm.productStr = "";
      }
    },
    addImg() {
      this.isSure = false;
      this.dialogVisible = true;
      this.fileList = [];
      this.uploadDisabled = "";
    },
    check(index, rows) {
      this.editIndex = index;
      this.fileList = [];
      this.dialogVisible = true;
      this.stepsForm.name_img = rows.name;
      this.stepsForm.url_img = rows.url;
      this.isSure = true;
      this.fileList.push({ url: rows.url, name: rows.name });
      this.uploadDisabled = "disabled";
    },
    imgSure() {
      this.$refs["imgForm"].validate((valid) => {
        if (valid) {
          if (this.fileList.length == 0) {
            this.$message({
              message: "请上传展品",
              type: "warning",
            });
          } else {
            this.stepsForm.productDetails.push({
              name: this.stepsForm.name_img,
              image_url: this.imgList.url,
            });
            this.fileListOld.push({
              name: this.stepsForm.name_img,
              // url: this.imgList.url,
              url: this.stepsForm.url_img,
            });
            this.dialogVisible = false;
            this.stepsForm.name_img = "";
            this.stepsForm.url_img = "";
            this.stepsForm.productStr = "sucdcess";
            this.editIndex = null;
          }
        }
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.postAxios();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    postAxios() {
      let url = "";
      if (this.steps2 != "") {
        let formData = new FormData();
        const data = {
          userInfo: JSON.parse(localStorage.getItem("userInfo")).login,
          activityId: localStorage.getItem("activityNo"),
          businessId: this.steps1.id,
          id: this.steps2.id,
          applyUser: this.steps1.applyUser,
          businessCompanyName: this.steps1.companyName,
          businessDesc: this.stepsForm.businessDesc,
          contactName: this.stepsForm.contactName,
          contactPhone: this.stepsForm.contactPhone,
          productDetails: JSON.stringify(this.stepsForm.productDetails),
          workPerson: JSON.stringify(this.stepsForm.workPerson),
          product_id: this.product_id,
          busPk: this.steps2.busPk,
        };
        Object.keys(data).map((item) => {
          if (data[item]) {
            formData.append(item, data[item]);
          }
        });
        this.files.map((item) => {
          formData.append("files", item.raw);
        });
        formData.append("delIds", this.delIds == "" ? [] : this.delIds);
        request({
          url: "/display/product/modify",
          data: formData,
          method: "post",
        }).then((res) => {
          if (res.code == 200) {
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.$emit("getData");
          } else {
            this.$message({
              message: "修改失败",
              type: "warning",
            });
            this.$emit("getData");
          }
        });
      } else {
        let formData = new FormData();
        const data = {
          userInfo: JSON.parse(localStorage.getItem("userInfo")).login,
          activityId: localStorage.getItem("activityNo"),
          businessId: this.steps1.id,
          applyUser: this.steps1.applyUser,
          businessCompanyName: this.steps1.companyName,
          businessDesc: this.stepsForm.businessDesc,
          contactName: this.stepsForm.contactName,
          contactPhone: this.stepsForm.contactPhone,
          productDetails: JSON.stringify(this.stepsForm.productDetails),
          workPerson: JSON.stringify(this.stepsForm.workPerson),
        };
        Object.keys(data).map((item) => {
          if (data[item]) {
            formData.append(item, data[item]);
          }
        });
        this.files.map((item) => {
          formData.append("files", item.raw);
        });
        request({
          url: "/display/product/add",
          data: formData,
          method: "post",
        }).then((res) => {
          if (res.code == 200) {
            this.$message({
              message: "提交成功",
              type: "success",
            });
            this.$emit("getData");
          } else {
            this.$message({
              message: "提交失败",
              type: "warning",
            });
            this.$emit("getData");
          }
        });
      }
    },
    handleChange(file, fileList) {
      let flag = true;
      const imgType =
        file.raw.type === "image/jpeg" || file.raw.type === "image/png";
      const isLt500k = file.size / 1024 / 1024 < 0.5;
      if (!imgType) {
        flag = false;
        this.fileList = [];
        this.$message.error("上传展品只能是 JPG和png 格式!");
        return false;
      }
      if (!isLt500k) {
        flag = true;
        this.$message.error("上传展品大小不能超过 500k!");
        this.fileList = [];
        return false;
      }
      if (flag) {
        this.fileList = fileList;
        this.isAdd = true;
        const reader = new FileReader();
        reader.readAsDataURL(file.raw);
        reader.addEventListener(
          "load",
          () => {
            this.stepsForm.url_img = reader.result;
          },
          false
        );
        this.imgList = file;
        this.files.push(file);
        this.attachmentId.push(file.uid);
        if (this.attachmentId.length > 0) {
          this.uploadDisabled = "disabled";
        }
      }
    },
    handleRemove(file, fileList) {
      this.fileList = [];
      this.uploadDisabled = "";
      this.stepsForm.url_img = "";
      if (this.files.length > 0) {
        this.files.map((item, index) => {
          if (item.name == file.name) {
            this.files.splice(index, 1);
          }
        });
      }
    },
  },
};
</script>
  
<style lang="scss" scoped>
#steps_1 {
  width: 100%;

  ::v-deep .el-form {
    .disabled .el-upload--picture-card {
      display: none !important;
    }

    .el-form-item__label {
      //   width: 110px !important;
    }

    .el-input {
      width: 97%;
    }

    .el-textarea {
      height: 100px;
      width: 600px;

      textarea {
        width: 100%;
        height: 100%;
      }
    }

    .btn {
      .el-form-item__content {
        margin-left: 0;
        // margin-top: 80px;
        text-align: center;
      }
    }

    .table_steps_3 {
      .el-form-item__error {
        position: initial;
      }

      .el-form-item__content {
        margin-left: 0;
      }
    }
  }
}
</style>
  