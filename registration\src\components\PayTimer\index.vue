<template>
  <div style="font-size: 16px; font-weight: bold;">
    {{ timeData }}
  </div>
</template>

<script>
import moment from 'moment'
export default {
  name: 'OrderPay',
  props: {
    data:{required:true,type:undefined},
  },
  components:{
    
  },
  data(){
    return {
      timeData:"",
      Timer: null
    }
  },
  destroyed(){
    if(this.Timer){
      clearInterval(this.Timer)
      this.Timer = null
    }
  },
  mounted(){
    //倒计时截至时间
    let end = moment(moment(this.data.createTime).add(15, 'minutes'))
    //间隔时间
    let count = end.diff(moment(),'milliseconds')
    if(count>0){
      this.timeData = moment(count).format("mm:ss")
      this.Timer = setInterval(()=>{
        let count = end.diff(moment(),'milliseconds')
        if(count>=0){
          this.timeData = moment(count).format("mm:ss")
        }else{
          clearInterval(this.Timer)
          this.Timer = null
          this.$emit("payCallback")
        }
      },1000)
    }else{
      this.$emit("payCallback")
    }
  },
  destroyed(){
    clearInterval(this.Timer)
    this.Timer = null
  },
  methods:{
    
    
  },
   
  
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>

body{
  padding-right: 0px !important;
  overflow: hidden;
}

</style>
