<template>
  <div>
    <el-form
      ref="dialogData"
      :model="dialogData"
      :rules="rules"
      label-width="100px"
      @submit.native.prevent
      style="width: 800px"
    >
      <el-form-item label="图片" prop="pictureImg">
        <el-upload
          action="string"
          list-type="picture-card"
          :limit="1"
          ref="upload"
          :file-list="fileList"
          :auto-upload="false"
          :on-change="handleChange"
          :class="uploadDisabled"
          :on-remove="handleRemove"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
        <span style="font-size: 12px; color: #ff6a00"
          >上传图片大小不得超过2MB;建议尺寸：600px*150px，格式.jpg、.png的图片文件</span
        >
      </el-form-item>
    </el-form>
    <div class="formBtn" style="text-align: center; width: 800px">
      <el-button type="primary" @click.stop="submitDialog" :loading="btnloading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
import request from "@/utils/request";
export default {
  name: "WapImg",
  components: {},
  props: {},
  watch: {},
  data() {
    return {
      activityid: JSON.parse(sessionStorage.getItem("activity")).activityId,
      rules: {
        pictureImg: [
          { required: true, message: "请选择图片", trigger: "change" },
        ],
      },
      dialogData: {
        pictureImg: undefined,
      },
      fileList: [],
      uploadDisabled: "",
      btnloading: false,
      attachmentId: [],
    };
  },
  created() {
    this.getActivityImage();
  },
  methods: {
    getActivityImage() {
      let formData = new window.FormData();
      formData.append("activityId", this.activityid);
      formData.append("imageWays", 3);
      request({
        url: "activityManage/getActivityImage",
        method: "post",
        data: formData,
      })
        .then((response) => {
          if (response.code == 200) {
            if (response.data && response.data.imageUrl != "") {
              let obj = {
                name: response.data.imageName,
                url: response.data.imageUrl,
              };
              this.dialogData.pictureImg = response.data.imageName;
              this.fileList = [{ ...obj }];
              // this.fileList.push({name:val.oldpicture, url: val.picture })
              this.uploadDisabled = "disabled";
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    submitDialog() {
      this.$refs.dialogData.validate(async (valid) => {
        if (valid) {
          this.btnloading = true;
          let formData = new window.FormData();
          formData.append("activityId", this.activityid);
          if (this.files) {
            formData.append("file", this.files.raw);
          }

          formData.append("imageWays", 3);
          request({
            url: "activityManage/saveActivityImage",
            method: "post",
            data: formData,
          })
            .then((response) => {
              if (response.code == 200) {
                this.btnloading = false;
                this.$message.success("操作成功");
              } else {
                this.btnloading = false;
                this.$message.error("操作失败");
              }
            })
            .catch((err) => {
              this.btnloading = false;
              console.log(err);
            });
        }
      });
    },
    handleChange(file, fileList) {
      let flag = true;
      const imgType =
        file.raw.type === "image/jpeg" || file.raw.type === "image/png";
      const isLt500k = file.size / 1024 / 1024 < 2;
      if (!imgType) {
        flag = false;
        this.fileList = [];
        this.$message.error("上传图片只能是 JPG和png 格式!");
        return false;
      }
      if (!isLt500k) {
        flag = true;
        this.$message.error("上传图片大小不能超过 2M!");
        this.fileList = [];
        return false;
      }
      if (flag) {
        this.files = file;
        this.dialogData.pictureImg = file.name;
        this.attachmentId.push(file.uid);
        if (this.attachmentId.length > 0) {
          this.uploadDisabled = "disabled";
        }
      }
    },
    handleRemove() {
      this.uploadDisabled = "";
      this.dialogData.pictureImg = undefined;
    },
  },
};
</script>

<style  lang="scss">
.disabled .el-upload--picture-card {
  display: none !important;
}
</style>

