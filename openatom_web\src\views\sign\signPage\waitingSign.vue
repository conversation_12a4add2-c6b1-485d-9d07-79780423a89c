<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form ref="form" :inline="true" :model="searchForm">
        <el-form-item label="活动名称:" prop="activityName">
          {{ this.activityName }}
        </el-form-item>
        <el-form-item label="姓名：">
          <el-input size="small" v-model.trim="searchForm.name" clearable placeholder="请输入姓名">

          </el-input>
        </el-form-item>
        <el-form-item label="手机号：">
          <el-input size="small" v-model.trim="searchForm.phone" clearable placeholder="请输入手机号">

          </el-input>
        </el-form-item>
        <el-form-item label="签到码：">
          <el-input size="small" v-model.trim="searchForm.signCode" clearable placeholder="请输入签到码">

          </el-input>
        </el-form-item>
        <el-form-item style="margin-left:10px;">
          <el-button size="mini" type="primary" icon="el-icon-search" @click="search()">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="seq" label="序号" width="60">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="姓名" width="180">
      </el-table-column>
      <el-table-column prop="phone" label="手机号">
      </el-table-column>
      <el-table-column prop="email" label="邮箱">
      </el-table-column>
      <el-table-column width="80" :resizable="false" :show-overflow-tooltip="true" prop="signCode" label="签到码">
      </el-table-column>
      <el-table-column prop="company" label="单位">
      </el-table-column>
      <el-table-column prop="address" label="操作">
        <template slot-scope="{row}">
          <el-button size="mini" @click="sign(row.id)" type="warning">签到</el-button>
        </template>
      </el-table-column>
    </el-table>
     <pagination v-show="total > 0" :total="total" :page.sync="searchForm.pageNum" :limit.sync="searchForm.pageSize"
      @pagination="getData" />
    <el-dialog title="签到" :visible.sync="visible" width="30%">
      <el-form label-width="60px"><el-form-item label="备注"><el-input v-model="remark" type="textarea"
            placeholder="请填写备注信息（选填）" :autosize="{ minRows: 4, maxRows: 8 }"></el-input></el-form-item></el-form>
      <span slot="footer">
        <el-button  @click="visible = false">取 消</el-button>
        <el-button  type="primary" @click="confirm" :loading="btnloading">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
const defaultForm = {
  content: ''
}
const defaultSearchForm = {

  hasSign: false,
  pageNum: 1,
  pageSize: 10,
}
import { queryHasSignedList, signActive } from '@/api/sign/signPage'
export default {
  name: "waitingSign",
  components: {

  },
  data () {
    return {
      tableData: [],
      remark: '',
      visible: false,
      activityId: '',
      id: '',
      searchForm: { ...defaultSearchForm },
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      total:0,
      btnloading:false
    };
  },

  beforeCreate () { },
  created () {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activityId = activeInfo.activityId;
  },
  mounted () { this.getData() },
  update () { },
  methods: {
    resetSearch () {
      this.searchForm = { ...defaultSearchForm }
      this.signTimes = []
      this.getData()
    },
    search () {
      this.searchForm.pageNum = 1
      this.getData()
    },
    async getData () {
      this.searchForm.activityId = this.activityId
      let res = await queryHasSignedList(this.searchForm)
      this.tableData = res.rows
      this.total = res.total
    },
    sign (id) {
      this.id = id
      this.visible = true
    },
    async confirm () {
      this.btnloading=true
      let res = await signActive({
        "regId": this.id,
        "remark": this.remark
      })
      if (res.code === 200) {
        this.visible=false
        this.getData()
        this.$message.success(res.msg)
        this.btnloading=false
      } else {
        this.$message.error(res.msg)
         this.btnloading=false
      }
    },
  },
};
</script>
<style lang="scss"></style>
