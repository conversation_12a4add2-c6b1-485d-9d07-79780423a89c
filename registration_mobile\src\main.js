
import Vue from 'vue'
import App from './App'
import router from './router'
import 'element-ui/lib/theme-chalk/index.css';

import formCreate from '@form-create/element-ui'
import 'vant/lib/index.css';
import {
  Icon as VanIcon,
  Image as VanImage,
  Overlay,
  Dialog as VanDialog,
  Notify,
  NavBar,
  Radio as VanRadio,
  RadioGroup as VanRadioGroup,
  Tab as VanTab,
  Tabs as VanTabs,
  <PERSON>up as VanPopup,
  Loading as <PERSON>Loading,
  Popover as <PERSON>Popover,
  List as VanList,
  Cell as VanCell,
  NoticeBar as VanNoticeBar,
  CountDown
 } from "vant";
import {
  Input,
  Radio,
  RadioGroup,
  RadioButton,
  Checkbox,
  Select,
  Option,
  Button,
  DatePicker,
  TimePicker,
  Form,
  FormItem,
  Row,
  Col,
  Cascader,
  Link,
  Autocomplete,
}from "element-ui"



Vue.use(formCreate)

Vue.use(VanIcon)
Vue.use(VanImage);
Vue.use(Overlay);
Vue.use(VanDialog);
Vue.use(Notify);
Vue.use(NavBar);
Vue.use(VanRadio);
Vue.use(VanRadioGroup);
Vue.use(VanTab);
Vue.use(VanTabs);
Vue.use(VanPopup);
Vue.use(CountDown);
Vue.use(VanLoading);
Vue.use(VanPopover);
Vue.use(VanList);
Vue.use(VanCell);
Vue.use(VanNoticeBar);

Vue.use(Input);
Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(RadioButton);
Vue.use(Checkbox);
Vue.use(Select);
Vue.use(Option);
Vue.use(Button);
Vue.use(Form);
Vue.use(FormItem);
Vue.use(Cascader);
Vue.use(Link);
Vue.use(Row);
Vue.use(Col);
Vue.use(DatePicker);
Vue.use(TimePicker);
Vue.use(Autocomplete);

Vue.config.productionTip = false
new Vue({
  el: '#app',
  router,
  render: h => h(App)
})
