<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="活动名称" prop="searchParam">
        <el-input
          v-model="queryParams.searchParam"
          placeholder="请输入活动名称"
          clearable
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['activity:manage:save']"
          >新增</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 
    <el-row :gutter="10" class="mb8">
  
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row> -->

    <el-table
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        label="活动编号"
        align="center"
        prop="activityId"
        :show-overflow-tooltip="true"
      />

      <el-table-column
        label="活动名称"
        align="center"
        prop="activityName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="简介"
        align="center"
        prop="activityDescription"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.activityDescription }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="活动周期"
        align="center"
        prop="stratEndTimes"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.stratEndTime.length == 2">{{
            scope.row.stratEndTime[0] + "至" + scope.row.stratEndTime[1]
          }}</span>
          <span v-else>{{ scope.row.stratEndTime[0] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="管理员"
        align="center"
        prop="userNames"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createBy">
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="300"
      >
        <!-- v-hasPermi="['system:post:edit']" -->
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleActiveAdmin(scope.row)"
            v-hasPermi="['activity:manage:saveAdmin']"
            >分配管理员</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleFlow(scope.row)"
            v-hasPermi="['activity:manage:saveFlow']"
            >报名流程</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['activity:manage:update']"
            >修改</el-button
          >
          <!-- v-hasPermi="['system:post:remove']" -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['activity:manage:update']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="500px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item
          v-if="form.activityId && form.activityId != ''"
          label="活动编号"
          prop="activityId"
        >
          <el-input
            :disabled="form.activityId && form.activityId != ''"
            v-model="form.activityId"
            placeholder="请输入活动编号"
          />
        </el-form-item>
        <el-form-item label="活动名称" prop="activityName">
          <el-input v-model="form.activityName" placeholder="请输入活动名称" />
        </el-form-item>
        <el-form-item label="活动地点" prop="activityPlace">
          <el-input v-model="form.activityPlace" placeholder="请输入活动地点" />
        </el-form-item>
        <el-form-item label="活动简介" prop="activityDescription">
          <el-input
            v-model="form.activityDescription"
            type="textarea"
            :rows="5"
            placeholder="请输入活动简介"
          />
        </el-form-item>
        <el-form-item label="活动周期" prop="stratEndTime">
          <el-date-picker
            v-model="form.stratEndTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 分配报名流程 -->
    <el-dialog
      :title="'分配报名流程'"
      :visible.sync="flowOpen"
      width="800px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-form
        ref="flowform"
        :model="flowform"
        :rules="flowRules"
        label-width="110px"
      >
        <el-form-item label="活动编号:" prop="activityId">
          <div>{{ flowform.activityId }}</div>
        </el-form-item>
        <el-form-item label="活动名称:" prop="activityName">
          <div>{{ flowform.activityName }}</div>
        </el-form-item>
        <el-form-item label="活动简介:" prop="activityDescription">
          <div class="activeDec">{{ flowform.activityDescription }}</div>
        </el-form-item>
        <el-form-item label="活动周期:" prop="stratEndTime">
          <el-date-picker
            :disabled="true"
            v-model="flowform.stratEndTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="报名流程配置" prop="flowno">
          <el-select style="width: 300px" v-model="flowform.flowno" clearable>
            <el-option
              v-for="dict in flowOptions"
              :key="dict.flowno"
              :label="dict.flowname"
              :value="dict.flowno"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :disabled="flowDisabled"
          @click="submitFlowForm"
          >确 定</el-button
        >
        <el-button @click="Flowcancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 分配活动管理员 -->
    <el-dialog
      custom-class="selfDialog"
      :title="'分配活动管理员'"
      :visible.sync="activeOpen"
      width="1000px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-form
        ref="activeform"
        :model="flowform"
        :rules="flowRules"
        label-width="90px"
        class="selfForm"
      >
        <el-form-item label="活动编号:" prop="activityId">
          <div>{{ activeform.activityId }}</div>
        </el-form-item>
        <el-form-item label="活动名称:" prop="activityName">
          <div>{{ activeform.activityName }}</div>
        </el-form-item>
        <el-form-item label="活动地点" prop="activityPlace">
         <div>{{ activeform.activityPlace }}</div>
        </el-form-item>
        <el-form-item label="活动简介:" prop="activityDescription">
          <div class="activeDec">{{ activeform.activityDescription }}</div>
        </el-form-item>
        <el-form-item label="活动周期:" prop="stratEndTime">
          <el-date-picker
            :disabled="true"
            v-model="activeform.stratEndTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="活动管理员" prop="adminActiveUser">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="addActive"
            >新增管理员</el-button
          >
          <el-table
            v-loading="Activeloading"
            :data="activeList"
            @selection-change="handleSelectionChange"
            size="mini"
            height="250"
          >
            <el-table-column
              label="用户"
              align="center"
              prop="userName"
              :show-overflow-tooltip="true"
            />

            <el-table-column
              label="角色"
              align="center"
              prop="userRole"
              :show-overflow-tooltip="true"
            />
            <el-table-column label="创建时间" align="center" prop="createTime">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="创建人" align="center" prop="createBy">
            </el-table-column>

            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
              width="50"
            >
              <!-- v-hasPermi="['system:post:edit']" -->
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDeleteUser(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="activetotal > 0"
            :total="activetotal"
            :page.sync="activeQuery.pageNum"
            :limit.sync="activeQuery.pageSize"
            @pagination="getAdminList"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <!-- <el-button
          type="primary"
          :disabled="flowDisabled"
          @click="submitFlowForm"
          >确 定</el-button
        > -->
        <el-button @click="Activecancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 选择活动管理员 -->
    <el-dialog
      custom-class="selfDialog"
      :title="'选择活动管理员'"
      :visible.sync="sysUserOpen"
      width="500px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-form
        ref="sysUserform"
        :model="sysUserListform"
        label-width="100px"
        class="selfForm"
        :inline="true"
      >
        <el-form-item label="用户名称/账号" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户名称"
            clearable
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="getuserList"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
      <div style="max-height: 500px; overflow: auto">
        <el-table
          v-loading="loading"
          :data="userList"
          @selection-change="handleSelectionChange"
          size="mini"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            :selectable="enableSelect"
          />

          <el-table-column
            label="用户名称"
            align="center"
            key="userName"
            prop="userName"
            v-if="columns[0].visible"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            label="手机号码"
            align="center"
            key="phonenumber"
            prop="phonenumber"
            v-if="columns[1].visible"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            label="邮箱"
            align="center"
            key="email"
            prop="email"
            v-if="columns[2].visible"
            :show-overflow-tooltip="true"
          />
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getuserList"
          :page-sizes="[10, 50, 100, 500]"
          :page-size="10"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sysuserSubForm">确 定</el-button>
        <el-button @click="sysUsercancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPost,
  getPost,
  delPost,
  addPost,
  updatePost,
} from "@/api/system/post";
import request from "@/utils/request";
import moment from "moment";
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser,
  resetUserPwd,
  changeUserStatus,
  deptTreeSelect,
  atomList,
} from "@/api/system/user";
moment.locale("zh-cn");
export default {
  name: "Post",
  dicts: ["sys_normal_disable"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchParam: undefined,
        // postCode: undefined,
        // postName: undefined,
        // status: undefined,
      },
      // 表单参数
      form: {
        activityName: undefined,
        activityDescription: undefined,
        activityPlace: undefined,
      },
      // 表单校验
      rules: {
        activityName: [
          { required: true, message: "活动名称不能为空", trigger: "blur" },
        ],
        activityDescription: [
          { required: true, message: "活动简介不能为空", trigger: "blur" },
        ],
        stratEndTime: [
          { required: true, message: "活动周期不能为空", trigger: "blur" },
        ],
      },
      flowRules: {
        flowno: [
          { required: true, message: "请选择报名流程", trigger: "blur" },
        ],
      },
      flowOpen: false,
      flowform: { flowno: undefined },
      flowOptions: [],
      flowDisabled: false,
      activeOpen: false,
      activeform: {},
      activeList: [],
      Activeloading: false,
      activeQuery: {
        pageNum: 1,
        pageSize: 10,
      },
      activetotal: 0,
      sysUserOpen: false,
      sysUserListform: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
      },
      // 列信息
      columns: [
        { key: 1, label: `用户名称`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 6, label: `邮箱`, visible: true },
      ],
      userList: [],
      selectedUser: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询岗位列表 */
    getList() {
      this.loading = true;

      request({
        url: "/activityManage/list",
        method: "post",
        data: this.queryParams,
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            response.rows.map((item) => {
              if (item.startDate == item.endDate) {
                item.stratEndTime = [];
                item.stratEndTime.push(item.startDate);

                // item.stratEndTime = item.startDate;
              } else {
                item.stratEndTime = [];
                item.stratEndTime.push(item.startDate);
                item.stratEndTime.push(item.endDate);
                // item.stratEndTime = item.startDate + "至" + item.endDate;
              }
            });
          }
          this.postList = response.rows;
          this.total = response.total;
          this.loading = false;
        })
        .catch((err) => {
          console.log(err);
        });
      // listPost(this.queryParams).then((response) => {

      //   this.total = response.total;

      // });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.form = {};
      // this.reset();
    },
    Flowcancel() {
      this.flowOpen = false;
      this.flowform = { flowno: undefined };
    },
    Activecancel() {
      this.activeOpen = false;
      this.activeform = {};
    },
    sysUsercancel() {
      this.sysUserOpen = false;
      this.sysUserform = {};
    },
    // 表单重置
    reset() {
      this.form = {
        postId: undefined,
        postCode: undefined,
        postName: undefined,
        postSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedUser = [...selection];
      // this.ids = selection.map((item) => item.postId);
      // this.single = selection.length != 1;
      // this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      this.form = {};
      this.open = true;
      this.title = "添加活动";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.form.id = row.id;
      this.form.activityId = row.activityId;
      this.form.activityName = row.activityName;
      this.form.activityDescription = row.activityDescription;
      this.form.activityPlace = row.activityPlace;
      this.form.stratEndTime = [];
      // this.form.stratEndTime.push(new Date(row.startDate));
      // this.form.stratEndTime.push(new Date(row.endDate));
      this.form.stratEndTime.push(row.startDate);
      this.form.stratEndTime.push(row.startDate);
      this.open = true;
      this.title = "修改活动";
    },
    handleFlow(row) {
      this.flowOpen = true;
      let data = { ...row };
      this.flowform = data;

      this.flowform.stratEndTime = [];
      this.flowform.stratEndTime.push(new Date(data.startDate));
      this.flowform.stratEndTime.push(new Date(data.endDate));
      // this.flowform.flowno = row.flowNo;
      this.getFlowList();

      // row.activityId
      this.getFlowActivityCount(row.activityId);
    },
    handleActiveAdmin(row) {
      this.activeOpen = true;
      let data = { ...row };
      this.activeform = data;
      this.activeform.stratEndTime = [];
      this.activeform.stratEndTime.push(new Date(data.startDate));
      this.activeform.stratEndTime.push(new Date(data.endDate));

      this.activeQuery.activityId = data.activityId;
      this.getAdminList();
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.startDate = this.form.stratEndTime[0];
          this.form.endDate = this.form.stratEndTime[1];
          if (this.form.activityId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    submitFlowForm() {
      this.$refs["flowform"].validate((valid) => {
        if (valid) {
          request({
            url: "/activityManage/saveActivityFlow",
            method: "post",
            data: this.flowform,
          })
            .then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.flowOpen = false;
              this.getList();
            })
            .catch((err) => {
              console.log(err);
            });
        }
      });
    },
    getFlowList() {
      request({
        url: "flows/listflow",
        method: "get",
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            this.flowOptions = response.rows;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getFlowActivityCount(activityid) {
      request({
        url: "flows/getFlowActivityCount?activityid=" + activityid,
        method: "get",
      })
        .then((response) => {
          if (response.code == 200) {
            if (response.data == true) {
              this.flowDisabled = false;
            } else {
              this.flowDisabled = true;
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId || this.ids;
      let obj = {};
      obj.id = row.id;
      obj.activityId = row.activityId;
      obj.isValidate = 0;
      this.$modal
        .confirm('是否确认删除活动编号为"' + row.activityId + '"的数据？')
        .then(function () {
          return delPost(obj);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    getAdminList() {
      this.Activeloading = true;
      let formData = new window.FormData();
      Object.keys(this.activeQuery).map((item) => {
        if (this.activeQuery[item]) {
          formData.append(item, this.activeQuery[item]);
        }
      });
      request({
        url: "activityManage/getAdminList",
        method: "post",
        // data: this.activeQuery,
        data: formData,
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            this.activeList = response.rows;
            this.activetotal = response.total;
            this.Activeloading = false;
          } else {
            this.Activeloading = false;
          }
        })
        .catch((err) => {
          this.Activeloading = false;
          console.log(err);
        });
    },
    addActive(row) {
      this.sysUserOpen = true;
      this.sysUserform = row;
      this.getuserList();
    },
    /** 查询用户列表 */
    getuserList() {
      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.userList = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    sysuserSubForm() {
      console.log(this.activeform);
      if (this.selectedUser.length > 0) {
        this.selectedUser.map((item) => {
          item.activityId = this.activeform.activityId;
        });
        request({
          url: "activityManage/saveActivityAdmin",
          method: "post",
          data: { userList: this.selectedUser },
        })
          .then((response) => {
            if (response.code == 200) {
              this.$modal.msgSuccess("操作成功");
              this.sysUserOpen = false;
              this.getAdminList();
            }
          })
          .catch((err) => {
            console.log(err);
          });
      } else {
        this.$message.error("请选择要分配的管理员");
      }
    },
    enableSelect(row, index) {
      let useStr = "";
      this.activeList.map((item) => {
        useStr += item.userName + ",";
      });
      if (useStr.indexOf(row.userName) == -1 && row.userId != 1) {
        return true;
      }
    },
    handleDeleteUser(row) {
      this.$modal
        .confirm('是否确认删除用户为"' + row.userName + '"的活动管理员？')
        .then(() => {
          request({
            url: "activityManage/deleteActivityAdmin",
            method: "post",
            data: { id: row.id },
          })
            .then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("操作成功");
                this.sysUserOpen = false;
                this.getAdminList();
              }
            })
            .catch((err) => {
              console.log(err);
            });
          // return delPost(obj);
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/post/export",
        {
          ...this.queryParams,
        },
        `post_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss">
.activeDec {
  height: 150px;
  overflow: auto;
  border: 1px solid #ccc;
  border-radius: 3px;
  padding: 5px;
}
.selfForm .el-form-item {
  margin-bottom: 10px;
}
.selfDialog .el-dialog__body {
  padding: 10px;
}
// ::v-deep .el-tooltip__popper{
//   min-width: 10px !important;
//   max-width: 300px !important;
// }
.el-tooltip__popper.is-dark {
  min-width: 10px !important;
  max-width: 300px !important;
}
</style>
  
