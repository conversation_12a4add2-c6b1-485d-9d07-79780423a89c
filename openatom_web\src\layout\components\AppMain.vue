<template>
  <section class="app-main">
    <!-- <div class="activityMainMsg" style="padding:10px">
      <div style="float: left; color: #606266; font-weight: 400">
        <div style="padding:10px 0"> 活动名称：{{ this.activityMsg.activityName }}<br /> </div>
        <span>
          活动周期：{{
            this.activityMsg.startDate + " ~ " + this.activityMsg.endDate
          }}
        </span>
        <span style="padding-left: 100px;">
          活动地点：{{ this.activityMsg.activityPlace }}
        </span>
        <span style="padding-left: 100px">
          活动状态：{{
            this.activityMsg.activityStatus == 1
              ? "未发布"
              : this.activityMsg.activityStatus == 2
              ? "已发布"
              : "已结束"
          }}
        </span>
      </div>
      <div style="clear: both"></div>
    </div> -->
    <transition name="fade-transform" mode="out-in">
      <!-- <keep-alive :include="cachedViews">
        <router-view v-if="!$route.meta.link" :key="key" />
      </keep-alive> -->
      <router-view v-if="!$route.meta.link" :key="key" />
    </transition>
    <iframe-toggle />
  </section>
</template>

<script>
import iframeToggle from "./IframeToggle/index";

export default {
  name: "AppMain",
  components: { iframeToggle },
  data(){
    return {
      activityMsg:{}
    }
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
    key() {
      return this.$route.path;
    },
  },
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activityMsg = { ...activeInfo };
  },
};
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
    overflow: auto;
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>
