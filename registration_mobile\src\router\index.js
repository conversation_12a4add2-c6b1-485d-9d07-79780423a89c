import Vue from 'vue'
import Router from 'vue-router'
import { develop } from "@/utils/baseKey.js";
const RegistEnter = ()=>import('@/components/RegistEnter')
const UserRegist = ()=>import('@/components/UserRegist')
const RegistViewNew = ()=>import('@/components/RegistViewNew')
const AlipayForm = ()=>import('@/components/OrderPay/AlipayForm.vue')
Vue.use(Router)

  let routesData =  [
    {
      path: '/',
      name: 'RegistEnterWap',
      component: RegistEnter
    },
    {
      path: '/mobile/auth/login',
      name: 'RegistEnterWap',
      component: RegistEnter
    },
    {
      path: '/weixinAuth',
      name: 'RegistEnterWap',
      component: RegistEnter
    },
    {
      path: '/UserRegist',
      name: 'UserRegistWap',
      component: UserRegist
    },
    {
      path: '/RegistView',
      name: 'RegistViewNewWap',
      component: RegistViewNew
    },
    // {
    //   path: '/AlipayForm/:param',
    //   name: 'AlipayForm',
    //   component: AlipayForm
    // },
    {
      path: '/AlipayForm',
      name: 'AlipayFormWap',
      component: AlipayForm
    },
  ]



// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err)
}
let router = new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  // base:'/registration_mobile/',
  base: develop() == "prew" ? "registration_mobileTest" : "registration_mobile",
  routes: routesData
});
router.beforeEach((to, from, next) => {
  let userInfo=localStorage.getItem("userInfo")?JSON.parse(localStorage.getItem("userInfo")):null
  // if(userInfo){
    if (window._oafs) {
      window._oafs.setDefault({
        pageId: to.name,
        userId: userInfo&&userInfo.id ? userInfo.id : undefined,
        extra: {
          path: to.path,
        },
      });
      window._oafs.pv();
    }
  // }
  next();
});
export default router;
// export default new Router({
//   mode: 'history', // 去掉url中的#
//   scrollBehavior: () => ({ y: 0 }),
//   base:'/registration_mobile/',
//   // base:'/registration_mobileTest/',
//   routes: routesData
// })