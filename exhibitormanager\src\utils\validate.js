export function checkPrice(rule, value, callback) {
    // const reg = /^\d+\.?\d*$/
    // if(!(value>=0&&value<=99999.99)){
    //   callback(new Error('取值范围在0-99999.99之间'))
    const reg=/^(([1-9]{1}[0-9]{0,4})|([0]{1}))((\.{1}[0-9]{1,2}$)|$)/
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      if ((!reg.test(value)) && value !== '') {
      //  callback(new Error('只能是整数或者小数'))
        callback(new Error('取值范围在0-99999.99之间,精度2位小数'))
      } else{
        callback()
      }
    }
  }