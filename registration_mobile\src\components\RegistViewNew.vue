<template>
  <div>
    <div class="registEnter">
      <van-nav-bar
        title="报名查询/Registration Inquiry"
        left-text="返回/Back"
        left-arrow
        @click-left="canback"
      />
      <div class="regView">
        <div class="loginUserMsg">
          <div class="userAvtar">
            <van-image round width="3rem" height="3rem" :src="url" />
          </div>
          <div class="userName">{{ userName }}</div>
          <div class="userMsg">Atomgit账号</div>
          <div style="clear: both"></div>
        </div>

        <div>
          <van-tabs
            v-model="active"
            style="margin: 10px 0px"
            animated
            @change="refrashData"
          >
            <van-tab title="我的报名/My Registration" name="1">
              <div v-if="isLoading" class="loadingCss">
                <van-loading size="24px" type="spinner" color="#1989fa" vertical
                  >加载中...</van-loading
                >
              </div>

              <div
                v-for="userMsg in userData"
                v-if="userData.length > 0 && isLoading === false"
              >
                <div class="registTable">
                  <div class="registItem">
                    <div class="registUserName">
                      <span>
                        <van-icon name="manager-o" color="#1989fa" size="20" />
                      </span>
                      <span style="font-weight: bold; font-size: 13px">{{
                        userMsg.name
                      }}</span>
                      <span
                        v-if="userMsg.approveStatus === '审批中'"
                        style="float: right"
                        ><i
                          class="el-icon-time"
                          style="
                            color: #4978ef;
                            padding: 3px;
                            font-weight: bold;
                          "
                        ></i
                        >审批中</span
                      >
                      <span
                        v-else-if="userMsg.approveStatus === '审批通过/Approved'"
                        style="float: right"
                        ><i
                          class="el-icon-circle-check"
                          style="
                            color: red;
                            padding: 3px;
                            color: #59cf45;
                            font-weight: bold;
                          "
                        ></i
                        >审批通过</span
                      >
                      <span
                        v-else-if="userMsg.approveStatus === '审批驳回'"
                        style="float: right"
                        ><i
                          class="el-icon-circle-close"
                          style="color: red; padding: 3px; font-weight: bold"
                        ></i
                        >审批驳回</span
                      >
                      <span
                        v-else-if="userMsg.approveStatus === '退款'"
                        style="float: right"
                        ><i
                          class="el-icon-circle-check"
                          style="
                            color: red;
                            padding: 3px;
                            color: #59cf45;
                            font-weight: bold;
                          "
                        ></i
                        >退款成功</span
                      >
                      <span v-else style="float: right"
                        ><i
                          class="el-icon-circle-check"
                          style="
                            color: #59cf45;
                            padding: 3px;
                            font-weight: bold;
                          "
                        ></i
                        >报名成功</span
                      >
                    </div>
                    <div style="padding: 5px; line-height: 30px">
                      <span style="background-color: #f8ede3">
                        {{
                          userMsg && userMsg.userTypeName
                            ? userMsg.userTypeName
                            : ""
                        }}
                      </span>
                      <span
                        v-if="userMsg.approveStatus === '审批通过'"
                        style="color: #3b85fe; float: right"
                        @click="showInTicket(userMsg)"
                        >入场票/Event pass</span
                      >
                      <div
                        style="
                          clear: both;
                          margin-top: 10px;
                          overflow: hidden;
                          white-space: nowrap;
                          text-overflow: ellipsis;
                        "
                      >
                        {{
                          userMsg && userMsg.activityName
                            ? userMsg.activityName
                            : ""
                        }}
                      </div>
                      <div
                        style="
                          width: 100%;
                          text-align: right;
                          line-height: 14px;
                          color: #7c7b7b;
                          font-size: 10px;
                        "
                      >
                        {{
                          userMsg && userMsg.createTime
                            ? userMsg.createTime
                            : ""
                        }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div
                v-if="userData && userData.length === 0 && isLoading === false"
                class="noData"
              >
                <i class="el-icon-folder-opened" style="font-size: 22px"></i>
                <div>暂无数据/No Data Available</div>
              </div>
            </van-tab>
            <van-tab title="我的订单/My Order" name="2">
              <div v-if="isLoading" class="loadingCss">
                <van-loading size="24px" type="spinner" color="#1989fa" vertical
                  >加载中...</van-loading
                >
              </div>

              <div
                v-for="order in orderMsgList"
                v-if="orderMsgList.length > 0 && isLoading === false"
              >
                <div class="registTable">
                  <div class="registItem" v-if="order">
                    <div class="registUserName">
                      <span style="font-size: 11px">订单编号:</span>
                      <span style="font-weight: bold; font-size: 11px">{{
                        order.orderNum
                      }}</span>
                      <span
                        v-if="order.orderStatus === '10'"
                        style="float: right; font-size: 11px; color: red"
                      >
                        等待付款
                        <PayTimer
                          :createTime="order.createTime"
                          style="font-size: 16px; font-weight: bold"
                          @callbackOrderExpire="callbackOrderExpire"
                        ></PayTimer>
                      </span>
                      <span
                        v-if="order.orderStatus === '20'"
                        style="float: right; font-size: 11px"
                        >已付款</span
                      >
                      <span
                        v-if="order.orderStatus === '30'"
                        style="float: right; font-size: 11px"
                        >已取消</span
                      >
                      <span
                        v-if="order.orderStatus === '40'"
                        style="float: right; font-size: 11px"
                        >交易关闭</span
                      >
                      <span
                        v-if="order.orderStatus === '50'"
                        style="float: right; font-size: 11px"
                        >退款中</span
                      >
                      <span
                        v-if="order.orderStatus === '60'"
                        style="float: right; font-size: 11px"
                        >退款完成</span
                      >
                      <span
                        v-if="order.orderStatus === '70'"
                        style="float: right; font-size: 11px"
                        >退款失败</span
                      >
                    </div>
                    <div style="padding: 5px; line-height: 25px">
                      <div style="padding: 5px; height: 85px">
                        <div
                          class="productIcon"
                          style="padding: 10px 0px"
                          @click="showOrder(order)"
                        >
                          <img src="@/assets/logo.svg" class="image" />
                          <div style="font-size: 14px; font-weight: bold">
                            {{ order.ordersDetails[0].product.name }}
                          </div>
                        </div>

                        <div
                          class="productAttribute"
                          v-if="order.paidAmount && order.orderStatus === '10'"
                          style="
                            border-left: 1px dashed #e3ecf8;
                            padding-left: 5px;
                          "
                          @click="showOrder(order)"
                        >
                          <div class="moneyShow">
                            <span style="font-size: 14px; line-height: 20px"
                              >待支付</span
                            >
                          </div>
                          <div class="moneyShow" style="color: red">
                            <span style="font-size: 20px; font-weight: bold">{{
                              "¥" + order.paidAmount
                            }}</span>
                          </div>
                          <div>数量*{{ order.ordersDetails[0].buyCounts }}</div>
                          <div>
                            订单来源:{{
                              order.sourceId === 1 ? "PC端" : "手机端"
                            }}
                          </div>
                        </div>
                        <div
                          class="productAttribute"
                          v-else
                          style="
                            border-left: 1px dashed #e3ecf8;
                            padding-left: 5px;
                          "
                          @click="showOrder(order)"
                        >
                          <div class="moneyShow">
                            <span
                              style="font-size: 14px"
                              v-if="order.orderStatus === '20'"
                              >实付¥</span
                            >
                            <span
                              style="font-size: 20px; font-weight: bold"
                              v-if="order.orderStatus === '20'"
                              >{{ order.paidAmount }}</span
                            >
                          </div>
                          <div
                            class="moneyShow"
                            style="
                              color: rgb(124, 123, 123);
                              text-decoration: line-through;
                            "
                            v-if="order.orderStatus === '20'"
                          >
                            <span style="font-size: 12px">原价¥</span>
                            <span style="font-size: 12px">{{
                              order.ordersDetails[0].product.price
                            }}</span>
                          </div>
                          <div
                            class="moneyShow"
                            style="color: rgb(124, 123, 123)"
                            v-else
                          >
                            <span style="font-size: 12px">原价¥</span>
                            <span style="font-size: 12px">{{
                              order.ordersDetails[0].product.price
                            }}</span>
                          </div>
                          <div>数量*{{ order.ordersDetails[0].buyCounts }}</div>
                          <div>
                            订单来源:{{
                              order.sourceId === 1 ? "PC端" : "手机端"
                            }}
                          </div>
                        </div>
                      </div>

                      <div style="clear: both"></div>

                      <div
                        style="
                          line-height: 14px;
                          color: #7c7b7b;
                          font-size: 10px;
                        "
                      >
                        <div v-if="order.paidTime">
                          支付时间：{{ order.paidTime }}
                        </div>
                        <div v-else>下单时间：{{ order.createTime }}</div>
                        <div style="clear: both"></div>
                      </div>
                      <div
                        style="margin: auto; text-align: right; padding: 5px"
                      >
                        <el-button
                          :disabled="isLoading"
                          v-if="order.orderStatus === '10'"
                          size="mini"
                          round
                          @click="cancelOrder(order)"
                          >取消订单</el-button
                        >
                        <el-button
                          :disabled="isLoading"
                          v-if="order.paidAmount && order.orderStatus === '10'"
                          type="success"
                          size="mini"
                          round
                          @click="goPayNow(order)"
                          >去付款</el-button
                        >
                        <el-button
                          :disabled="isLoading"
                          v-else-if="order.orderStatus === '10'"
                          type="danger"
                          size="mini"
                          round
                          @click="goPayNow(order)"
                          >去结算</el-button
                        >
                        <el-button
                          :disabled="isLoading"
                          v-if="
                            order.orderStatus === '30' ||
                            order.orderStatus === '40'
                          "
                          size="mini"
                          round
                          @click="deleteOrder(order)"
                          >删除订单</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div
                v-if="
                  orderMsgList &&
                  orderMsgList.length === 0 &&
                  isLoading === false
                "
                class="noData"
              >
                <i class="el-icon-folder-opened" style="font-size: 22px"></i>
                <div>暂无数据/No Data Available</div>
              </div>
            </van-tab>
          </van-tabs>

          <van-dialog
            v-model="showOrderMsg"
            title="报名人员信息"
            show-cancel-button
            cancel-button-text="关闭"
            :show-confirm-button="false"
          >
            <div style="min-height: 350px">
              <el-form style="padding: 10px" v-if="orderUserData.length > 0">
                <el-form-item label="活动名称">
                  <div style="margin: auto; text-align: right">
                    {{ orderUserData[0].activityName }}
                  </div>
                </el-form-item>
                <el-form-item label="姓名" v-if="orderUserData[0].name">
                  <div style="margin: auto; text-align: right">
                    {{ orderUserData[0].name }}
                  </div>
                </el-form-item>
                <el-form-item label="性别" v-if="orderUserData[0].sex">
                  <div style="margin: auto; text-align: right">
                    {{ orderUserData[0].sex }}
                  </div>
                </el-form-item>
                <el-form-item label="邮箱" v-if="orderUserData[0].email">
                  <div style="margin: auto; text-align: right">
                    {{ orderUserData[0].email }}
                  </div>
                </el-form-item>
                <el-form-item label="手机号" v-if="orderUserData[0].phone">
                  <div style="margin: auto; text-align: right">
                    {{ orderUserData[0].phone }}
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </van-dialog>

          <van-popup
            v-model="showOrderCode"
            closeable
            position="bottom"
            :style="{ height: '90%' }"
          >
            <div v-if="showOrderCode">
              <div
                style="
                  margin: auto;
                  text-align: left;
                  padding: 10px 20px;
                  font-size: 15px;
                  font-weight: bold;
                "
              >
                入场票信息/Event Pass
              </div>
              <div style="padding: 0px 10px; font-size: 12px" v-if="activeMsg">
                <img
                  :src="imgUrl"
                  class="image"
                  style="width: 100%; border-radius: 10px; overflow: hidden"
                />
                <div style="margin: auto; text-align: left; line-height: 28px">
                  <!-- <div class="codeTitle">{{ activeMsg.activityName }}</div> -->
                     <div class="codeTitle">{{ codeMsg.activityName }}</div>
                  <div>
                    <!-- {{
                      activeMsg
                        ? activeMsg.startDate + " ~ " + activeMsg.endDate
                        : ""
                    }} -->
                      {{
                      codeMsg
                        ? codeMsg.activityStartDate + " ~ " + codeMsg.activityEndDate
                        : ""
                    }}
                  </div>
                  <div class="codeTitle">地点/Location</div>
                  <!-- <div>{{ activeMsg.activityPlace }}</div> -->
                     <div>{{ codeMsg.activityPlace }}</div>
                  <div class="codeTitle">签到码/Ticket Code</div>
                  <div>
                    {{ codeMsg && codeMsg.signCode ? codeMsg.signCode : null }}
                  </div>
                  <div class="codeTitle">电子票/E-Ticket</div>
                  <div style="margin: auto; text-align: center">
                    <img
                      v-if="codeMsg && codeMsg.qrCode"
                      :src="codeMsg.qrCode"
                      class="image"
                      style="
                        padding-top: 0px;
                        border-radius: 5px;
                        padding-bottom: 20px;
                      "
                    />
                    <div v-else>暂无数据/No Data Available</div>
                  </div>
                </div>
              </div>
            </div>
          </van-popup>

          <van-dialog
            v-model="showPayFinish"
            title="请确认支付是否已完成"
            :show-confirm-button="false"
          >
            <div style="padding: 10px; margin: 10px">
              <div
                style="
                  width: 100%;
                  margin: auto;
                  padding: 10px;
                  text-align: center;
                  color: red;
                  border-bottom: 1px solid #ccc;
                  border-top: 1px solid #ccc;
                "
                @click="clickPayFinishState(0)"
              >
                支付已完成
              </div>
              <div
                style="
                  width: 100%;
                  margin: auto;
                  padding: 10px;
                  text-align: center;
                "
                @click="clickPayFinishState(1)"
              >
                支付遇到问题，重新支付
              </div>
            </div>
          </van-dialog>
        </div>
      </div>
    </div>

    <van-popup
      v-model="showPay"
      closeable
      @close="cancelPay"
      position="bottom"
      :close-on-click-overlay="false"
      :style="{ height: '90%' }"
    >
      <OrderPay
        v-if="showPay"
        @callbackOrderExpire="callbackOrderExpire"
        :orderNum="selectedOrder"
        @callbackSuccess="callbackSuccess"
      />
    </van-popup>
  </div>
</template>

<script>
import {
  getRegistUserList,
  orderList,
  orderRegList,
  orderDelete,
  regGetOne,
  orderCancel,
  wapGoPay,
  queryOrderByOutTradeNo,
  getActivityImage,
} from "@/utils/apiManage";
import { Dialog, Notify } from "vant";
import moment from "moment";
import OrderPay from "@/components/OrderPay";
import PayTimer from "@/components/PayTimer";
import defauleImgUrl from "@/assets/mainkv.jpg";
export default {
  name: "RegistView",
  props: {},
  components: {
    OrderPay,
    PayTimer,
  },
  data() {
    return {
      userData: [],
      userName: "",
      url: "",
      active: "1",
      orderType: "",
      showOrderMsg: false,
      orderMsgList: [],
      orderUserData: [],
      codeMsg: null,
      showOrderCode: false,
      activeMsg: JSON.parse(localStorage.getItem("activity")),
      showPay: false,
      selectedOrder: null,
      isLoading: false,
      showPayFinish: false,
      selectedOrderMsg: null,
      imgUrl: "",
    };
  },
  destroyed() {
    localStorage.removeItem("showOrder");
  },
  mounted() {
    if (
      localStorage.getItem("showOrder") === true ||
      localStorage.getItem("showOrder") === "true"
    ) {
      this.active = "2";
    } else {
      this.active = "1";
    }
    let userInfo = JSON.parse(localStorage.getItem("userInfo"));
    this.userName = userInfo.login;
    this.url = userInfo.avatar_url;
    this.refrashData();
  },
  created() {},
  methods: {
    //获取活动图片
    getActivityImageFront(activityNo) {
      return getActivityImage(activityNo, 1).then((res) => {
        if (res.code == 200 && res.data) {
          this.imgUrl =
            res.data.imageUrl && res.data.imageUrl != ""
              ? res.data.imageUrl
              : defauleImgUrl;
        } else {
          this.imgUrl = defauleImgUrl;
        }
      });
    },
    callbackSuccess() {
      this.refrashData();
    },
    showOrder(record) {
      this.orderUserData = [];
      this.showOrderMsg = true;
      return orderRegList({ orderDetailsId: record.ordersDetails[0].id }).then(
        (res) => {
          if (res && res.data) {
            this.orderUserData = res.data;
          } else {
            Notify({ type: "warning", message: JSON.stringify(res) });
          }
        }
      );
    },
    callbackOrderExpire() {
      //订单过期，返回查看订单列表页面
      this.showPay = false;
      this.refrashData();
    },
    //显示入场票信息
    showInTicket(userMsg) {
      this.codeMsg = null;
      this.showOrderCode = true;
      this.imgUrl = "";

      return regGetOne(userMsg.id).then((res) => {
        this.getActivityImageFront(userMsg.activityId);
        if (res && res.data) {
          this.codeMsg = res.data;
          this.codeMsg.activityName = userMsg.activityName;
          this.codeMsg.activityPlace = userMsg.activityPlace;
          this.codeMsg.activityStartDate = userMsg.activityStartDate;
          this.codeMsg.activityEndDate = userMsg.activityEndDate;
        } else {
          Notify({ type: "warning", message: JSON.stringify(res) });
        }
      });
    },
    //返回报名页面
    canback() {
      localStorage.removeItem("redirectType");
      this.$router.replace({ path: "/" });
    },
    //查看所有报名
    getViewList() {
      this.isLoading = true;
      this.userData = [];
      return getRegistUserList()
        .then((res) => {
          this.isLoading = false;
          if (res && res.data) {
            this.userData = res.data;
          } else {
            Notify({ type: "warning", message: JSON.stringify(res) });
          }
        })
        .catch((res) => {
          Notify({ type: "warning", message: JSON.stringify(res) });
        });
    },
    //获取订单列表
    getOrderList() {
      this.isLoading = true;
      this.orderMsgList = [];
      return orderList({ orderStatus: this.orderType })
        .then((res) => {
          this.isLoading = false;
          if (res && res.data && res.data.length >= 0) {
            res.data.map((item, index) => {
              if (item.orderStatus === "10") {
                let start = moment();
                let end = moment(moment(item.createTime).add(15, "minutes"));
                let count = end.diff(start, "milliseconds");
                if (count <= 0) {
                  return (item.orderStatus = "40");
                }
              }
            });
            res.data.map((item) => {
              item.createTime = item.createTime
                ? moment(item.createTime).format("YYYY-MM-DD HH:mm:ss")
                : null;
              item.paidTime = item.paidTime
                ? moment(item.paidTime).format("YYYY-MM-DD HH:mm:ss")
                : null;
              return item;
            });
            this.orderMsgList = res.data;
          } else {
            Notify({ type: "warning", message: JSON.stringify(res) });
          }
        })
        .catch((res) => {
          Notify({ type: "warning", message: JSON.stringify(res) });
        });
    },

    refrashData() {
      this.selectedOrder = null;
      this.showPay = false;
      if (this.active === "2") {
        this.getOrderList();
      } else {
        this.getViewList();
      }
    },
    //取消订单
    cancelOrder(order) {
      Dialog.confirm({
        title: "提示",
        message: "确认取消该订单!",
      })
        .then(() => {
          order.createTime = null;
          this.isLoading = true;
          return orderCancel(order).then((res) => {
            this.isLoading = false;
            if (res) {
              Notify({ type: "success", message: "订单取消成功！" });
              this.refrashData();
            } else {
              Notify({ type: "warning", message: JSON.stringify(res) });
            }
          });
        })
        .catch(() => {
          this.isLoading = false;
          this.refrashData();
        });
    },
    //删除订单
    deleteOrder(order) {
      Dialog.confirm({
        title: "提示",
        message: "确认删除该订单!",
      })
        .then(() => {
          this.isLoading = true;
          return orderDelete(order.orderNum).then((res) => {
            this.isLoading = false;
            if (res) {
              Notify({ type: "success", message: "删除订单成功！" });
            } else {
              Notify({ type: "warning", message: JSON.stringify(res) });
            }
            this.refrashData();
          });
        })
        .catch(() => {
          this.isLoading = false;
          this.refrashData();
        });
    },

    orderTypeChange() {
      this.refrashData();
    },
    submitOrder(order) {
      this.selectedOrder = order.orderNum;
      this.showPay = true;
    },
    //显示订单核算页面
    goPayNow(order) {
      this.selectedOrder = order.orderNum;
      this.selectedOrderMsg = order;
      this.showPay = true;
    },
    //支付订单
    submitPayMoney() {
      this.isLoading = true;
      let param = {
        orderNum: this.selectedOrderMsg.orderNum,
        couponCode: null,
        paidAmount: null,
        paidMethod: this.payType,
        sourceId: this.selectedOrderMsg.sourceId,
      };
      return wapGoPay(param)
        .then((res) => {
          this.isLoading = false;
          if (res.code === 200 && res.msg === "支付成功!") {
            this.orderSuccessPay(res.msg);
          } else if (res.code === 200 && res.data) {
            //需要微信支付
            if (res.data.isCalc) {
              //去结算
              this.showPay = true;
            } else {
              //去支付

              window.location.href = res.data;
              setTimeout(() => {
                this.showPayFinish = true;
              }, 1000);
            }
          } else if (res.code === 5004) {
            if (res.data && res.data.indexOf("优惠码数量失败") > -1) {
              Dialog.alert({
                title: "系统提示",
                message: " 优惠码使用失败！",
              });
            } else {
              this.orderCannotPay(res.msg);
            }
          } else if (res.code === 5006) {
            this.orderCannotPay(res.msg);
          } else if (res.code === 5007) {
            if (this.selectedOrderMsg.sourceId === 1) {
              //PC
              Dialog.alert({
                title: "系统提示",
                message: "请前往PC端打开订单进行支付",
              });
            } else {
              Dialog.alert({
                title: "系统提示",
                message: res.msg,
              });
            }
          } else {
            //报错
            Dialog.alert({
              title: "系统提示",
              message: res.msg,
            });
          }
        })
        .catch((res) => {
          this.isLoading = false;
          this.refrashData();
        });
    },
    //订单支付失败
    orderCannotPay(msg) {
      Dialog.alert({
        title: "系统提示",
        message: msg,
      }).then(() => {
        this.refrashData();
      });
    },
    //订单支付成功
    orderSuccessPay(msg) {
      Dialog.alert({
        title: "系统提示",
        message: msg,
      }).then(() => {
        this.refrashData();
      });
    },
    //支付提醒按钮点击查询订单状态
    clickPayFinishState(val) {
      this.showPayFinish = false;
      this.queryOrderByOutTradeNo();
    },
    //查询支付状态
    queryOrderByOutTradeNo() {
      return queryOrderByOutTradeNo(this.selectedOrder)
        .then((res) => {
          if (res.code === 200 && res.msg === "支付成功!") {
            this.orderSuccessPay(res.msg);
          } else if (res.code === 5004) {
            //拒绝支付
            this.orderCannotPay(res.msg);
          } else if (res.code === 5006) {
            this.orderCannotPay(res.msg);
          } else if (res.code === 5007) {
            if (this.selectedOrderMsg.sourceId === 1) {
              //PC
              Dialog.alert({
                title: "系统提示",
                message: "请前往PC端打开订单进行支付",
              });
            } else {
              Dialog.alert({
                title: "系统提示",
                message: res.msg,
              });
            }
          } else {
            Dialog.alert({
              title: "系统提示",
              message: res.msg,
            }).then(() => {
              this.refrashData();
            });
          }
        })
        .catch((error) => {
          Dialog.alert({
            title: "系统提示",
            message: error,
          }).then(() => {
            this.refrashData();
          });
        });
    },
    //取消支付
    cancelPay() {
      this.showPay = false;
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
body {
  padding-right: 0px !important;
  overflow: hidden;
}
.el-form-item__label:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}
.el-form-item__label {
  padding-right: 10px !important;
}
h3 {
  margin: 40px 0 0;
}
a {
  color: #42b983;
}
.regView {
  margin: 10px 5%;
  width: 90%;
}
.userAvtar,
.userName {
  height: 50px;
  float: left;
  line-height: 50px;
  padding-left: 10px;
  font-weight: bold;
  font-size: 13px;
}
.userMsg {
  float: right;
  height: 50px;
  line-height: 50px;
  color: #5b6b80;
}
.registEnter {
  margin: 0px;
  padding: 0px 0px 20px 0px;
}
.loginUserMsg {
  padding: 20px;
  background-color: #ffffff;
  line-height: 50px;
  overflow: hidden;
  border-radius: 10px;
  margin-top: 10px;
}
.registTitle {
  font-weight: bold;
  margin: 13px 10px 10px 10px;
  text-align: left;
}
.registTable {
  width: 100%;
  background-color: #ffffff;
  border-radius: 10px;
}
.registItem {
  padding: 10px 5px;
  margin: 10px;
  border-radius: 10px;
  font-size: 12px;
  text-align: left;
}
.registUserName {
  border-bottom: 1px solid #cdcdcd;
  padding: 5px;
}
.noData {
  padding: 10px 5px;
  background-color: #ffffff;
  border-radius: 10px;
  text-align: center;
  color: #a2abbd;
  font-size: 24px;
  line-height: 30px;
  margin-top: 10px;
}
.productIcon {
  float: left;
}
.productAttribute {
  float: right;
  margin: 5px;
  text-align: right;
  min-width: 100px;
  min-height: 70px;
  font-size: 14px;
}
.payMoneyMsg {
  padding: 10px;
  margin: auto;
  text-align: right;
  font-size: 16px;
}
.codeTitle {
  font-size: 15px;
  font-weight: bold;
  color: #030b1a;
}
.moneyShow {
  color: red;
  padding: 0px;
}
.loadingCss {
  border-radius: 10px;
  width: 100%;
  height: 100px;
  background-color: #ffffff;
  margin-top: 10px;
  padding-top: 20px;
}
</style>
