<template>
  <div class="registEnter">
    <van-nav-bar
      title="提交订单"
    />
    <van-overlay :show="showLoading" style="z-index: 99999; position: absolute;height: 100%;">
      <div class="wrapper">
        <van-loading type="spinner" color="#1989fa" class="block" />
      </div>
    </van-overlay>

    <div v-if="isShowAliPay === false">

      <div class="productMsg" v-if="orderMsg" >
        <div style="margin:auto;text-align:left;padding:5px 10px 0px 10px">
          <div style="float: left; line-height: 18px;">商品信息</div>
          <!-- <div class="countDownTime" style="float: right; line-height: 18px;padding: 0px;margin: 0px;">等待付款<van-count-down style="display: inline; color: red;font-size: 16px;padding-left: 10px;font-weight: bold;" :time="countDownTime" format="mm:ss" /></div> -->
          <div style="clear: both;"></div>
        </div>
        <div style="border:1px solid #DCDFE6;border-radius:10px;height:80px;padding:10px;margin:5px 10px">
          <div class="productIcon" style="margin:auto;text-align:left;padding: 5px 0px;">
            <img src="@/assets/logo.svg" class="image" >
            <div style="font-size:14px; font-weight:bold;">{{ orderMsg.ordersDetails[0].product.name }}</div>
          </div>
          <div class="productAttribute" v-if="orderMsg" style="border-left: 1px dashed #DCDFE6;padding-left:5px">
            <!-- <div class="moneyShow" v-if="orderMsg.ordersDetails[0].product.isActivityPrice === 1">
              <span style="font-size: 12px;">¥</span>
              <span style="font-size: 22px;">{{ orderMsg.ordersDetails[0].product.activityPrice }}</span>
              <div style="font-size: 11px;text-decoration:line-through;color:#303133;line-height: 12px;">{{ "原价:"+ orderMsg.ordersDetails[0].product.price }}</div>
            </div>
            <div class="moneyShow" v-else>
              <span style="font-size: 12px;">¥</span>
              <span style="font-size: 22px;">{{ orderMsg.ordersDetails[0].product.price }}</span>
            </div> -->
            <div> 数量*{{ orderMsg.ordersDetails[0].buyCounts }}
            </div>
          </div>
        </div>
        <div style="clear:both"></div>
      </div>

      <div class="orderDetail">
        <el-form style="padding:10px">

          <el-form-item
              label="校验码"
              v-if="isCalc && orderMsg.ordersDetails[0].product.price>0"
            >
            <div style="margin:auto;text-align:right;">
              <el-checkbox v-model="isUsePreferential" @change="couponChange" disabled>是否使用校验码</el-checkbox>
              <el-input v-if="isUsePreferential" placeholder="请输入校验码" v-model="preferential" size="small" style="width:210px"/>
              <el-button v-if="isUsePreferential && preferential" size="medium" type="primary" @click="couponBlur()">查询使用</el-button>
            </div>
          </el-form-item>

          <!-- <el-form-item
            label="支付方式"
            :rules="{required:true, message: '请选择支付方式'} "
          >
          <div style="margin:auto;text-align:right">
            <van-radio-group v-model="payType" style="margin:auto;text-align:right;display: inline-flex;">
              <van-radio name="1"><van-icon name="wechat" color="#00c805" size="2rem" style="margin-right: 10px;"/></van-radio>
              <van-radio name="2"><van-icon name="alipay" color="#09a7e6" size="2rem" /></van-radio>
            </van-radio-group>
          </div>
          </el-form-item> -->
          <el-form-item
            label="订单编号"
          >
          <div style="margin:auto;text-align:right">
            {{ orderMsg.orderNum }}
          </div>
          </el-form-item>
          <el-form-item
            label="下单时间"
          >
          <div style="margin:auto;text-align:right">
            {{ orderMsg.createTime }}
          </div>
          </el-form-item>
        </el-form>

        <!-- <div class="payMoneyMsg">
          <span class="moneyShow" style=" font-size:16px;" v-if="isUsePreferential && coupon">
            共减<span style="font-weight:bold;color: red;"> ¥{{ couponPrice ? couponPrice : 0 }}</span>
          </span>
          需付款<span style="font-weight:bold;color: red;font-size: 24px;"> ¥{{isCalc ? needPay :price }}</span>
        </div> -->

        <div style="text-align: center; padding: 10px 0px;">
          <el-button :disabled = "isLoading" type="primary" @click="()=>this.submitPayMoney()">确认</el-button>
        </div>

      </div>

    </div>
    <!-- 微信 -->
    <van-dialog v-model="showPayFinish" :title="'请确认支付是否已完成'" :show-confirm-button="false">
      <div style="padding: 10px;margin: 10px;">
        <div style="width: 100%;margin:auto; padding: 10px; text-align: center; color: red; border-bottom: 1px solid #ccc; border-top: 1px solid #ccc;" @click="clickPayFinishState(0)">支付已完成</div>
        <div style="width: 100%;margin:auto; padding: 10px; text-align: center;" @click="clickPayFinishState(1)">支付遇到问题，重新支付</div>
      </div>
    </van-dialog>

  </div>
</template>

<script>
import{orderGoPay,wapGoPay,wapAliGoPay,couponGetOne,queryOrderByOutTradeNo,orderIsCalc,aliqueryOrderByOutTradeNo }from '@/utils/apiManage'
import citys from '@/utils/city.js'
import {Dialog,Notify } from "vant"
import moment from 'moment'
import * as math from 'mathjs'
let timer = null
export default {
  name: 'OrderPay',
  props: {
    orderNum:{required:true,type:undefined},
  },
  data(){
    return {
      step:1,
      isLoading:false,
      activeMsg:null,
      fApi:{},
      isUsePreferential: true,
      ticketList:[],
      ticketType:null,
      payType: '2',
      orderMsg:null,
      preferential:"",
      isChecked:false,
      protocolMsg:null,
      htmlMsg:null,
      formInfo:[],
      value: {},
      cityOption:citys,
      cardOption:[],
      options: [],
      showLoading:false,
      regFormData:{},
      coupon:null,
      needPay:0,
      countDownTime:0,
      couponPrice:null,
      showPayFinish:false,
      payFinishStateBtn:null,
      payStatelist:[
        {lable:'支付已完成',val:'0'},
        {lable:'支付遇到问题，重新支付',val:'1'},
      ],
      isWeichatBrowser:false,
      isShowAliPay:false,
      alipayFormData:null,
      isCalc:null,
      price:null,

    }
  },
  destroyed(){
    if(timer){
      clearTimeout(timer)
      timer = null
    }
  },
  mounted(){

    let result = /MicroMessenger/i.test(window.navigator.userAgent)
    if (result) {
      this.isWeichatBrowser = true
    }
    this.orderIsCalc()
    this.goPayNow()
  },
  methods:{
    backForm(){
      this.step = 2
    },
    backTicket(){
      this.step = 1
    },
    couponChange(){
      this.coupon = null
      this.preferential = ""
      this.couponPrice = 0
      this.needPay = this.orderMsg.ordersDetails[0].product.isActivityPrice === 1 ? this.orderMsg.ordersDetails[0].product.activityPrice : this.orderMsg.ordersDetails[0].product.price
    },
    clickPayFinishState(val){
      this.showPayFinish = false
      this.queryOrderByOutTradeNo()
    },
    queryOrderByOutTradeNo(){
      if(this.isShowAliPay){
        return aliqueryOrderByOutTradeNo(this.orderNum).then(res => {
          this.getPayState(res)
        }).catch((error)=>{
          Dialog.alert({
            title: '系统提示',
            message: error
          }).then(() => {
            this.$emit("callbackSuccess")
          });
        })
      }else{
        return queryOrderByOutTradeNo(this.orderNum).then(res => {
            this.getPayState(res)
        }).catch((error)=>{
          Dialog.alert({
            title: '系统提示',
            message: error
          }).then(() => {
            this.$emit("callbackSuccess")
          });
        })
      }
    },
    getPayState(res){
      if(res.code === 200 && res.msg ==='支付成功!'){
        this.orderSuccessPay(res.msg)
      }else if(res.code === 5004){//拒绝支付
        this.orderCannotPay(res.msg)
      }else if(res.code === 5006){
        this.orderCannotPay(res.msg)
      }else if(res.code === 40004){
        this.orderCannotPay("交易订单未支付")
      }else{
        Dialog.alert({
          title: '系统提示',
          message: res.msg
        }).then(() => {
          this.$emit("callbackSuccess")
        });
      }
    },
    goPayNow(){
      return orderGoPay({
        "orderNum": this.orderNum
      }).then(res => {
        if(res.code === 200 && res.data){
          res.data.createTime = res.data.createTime ? moment(res.data.createTime).format('YYYY-MM-DD HH:mm:ss') : null
          this.orderMsg = res.data
          if(this.orderMsg.ordersDetails[0].product.isActivityPrice === 1){
            this.needPay = Number(this.orderMsg.ordersDetails[0].product.activityPrice) > 0 ?Number(this.orderMsg.ordersDetails[0].product.activityPrice) : 0
          }else{
            this.needPay = Number(this.orderMsg.ordersDetails[0].product.price) > 0 ?Number(this.orderMsg.ordersDetails[0].product.price) : 0
          }

          let start = moment()
          let end = moment(moment(this.orderMsg.createTime).add(15, 'minutes'))
          let count = end.diff(start,'milliseconds')
          if(count>0 && !timer){
            timer = setTimeout(()=>{
              clearTimeout(timer)
              timer = null
              this.$emit("callbackOrderExpire")
            },count)
            this.countDownTime = count
          }else{
            Notify({ type: 'warn', message: '该订单支付超时!' });
          }
        }
      }).catch((error)=>{
        alert(JSON.stringify(error))
        this.$emit("callbackOrderExpire")
      })
    },
    orderIsCalc(){
      return orderIsCalc({
        "orderNum": this.orderNum
      }).then(res => {
        if(res.code === 200 && res.data){
          this.isCalc = res.data.isCalc
          this.price = res.data.price ? res.data.price : null
        }
      }).catch((error)=>{

      })
    },

    submitPayMoney(){
      this.isLoading = true
      if(this.payType === undefined){
        this.isLoading = false
        Dialog.alert({
          title: '系统提示',
          message: "请选择支付方式！"
        })
          return
      }
      let param ={
        // "paidAmount": this.orderMsg.ordersDetails[0].product.price,
        "orderNum": this.orderMsg.orderNum,
        "couponCode": null,
        "paidAmount": this.isCalc ? this.needPay :this.price,
        "paidMethod": this.payType,
        "sourceId": this.orderMsg.sourceId,
      }
      // 2024.09.02 蒲天阳修改：支付金额必须为0
        if(param.paidAmount != 0){
          Dialog.alert({
            title: '系统提示',
            message: "请输入正确的校验码！"
          })
            this.isLoading = false
            return
        }
      if(this.isUsePreferential && this.preferential.length>0 && this.coupon){
        param.couponCode = this.preferential
      }
      else if(this.isUsePreferential){
        this.isLoading = false
        // if(this.preferential.length<=0){
        //   Dialog.alert({
        //     title: '系统提示',
        //     message: "请输入校验码！"
        //   })

        // }else{
        //   Dialog.alert({
        //     title: '系统提示',
        //     message: "请点击使用校验码！"
        //   })
        // }
        // return null
      }

      if(this.payType === "1"){//微信
        this.isShowAliPay = false
        this.weixinPay(param)
      }else if(this.payType === "2"){//支付宝

        this.isShowAliPay = true
        this.aliPay(param)
      }
    },
    weixinPay(param){
      wapGoPay(param).then(res => {
        this.isLoading = false
        if(res.code ===200 && res.msg==="支付成功!"){
          this.orderSuccessPay(res.msg)
        }else if(res.code === 200 && res.data ){//需要微信支付
          window.location.href=res.data;
          setTimeout(()=>{
            this.showPayFinish=true
          },1000)
        }else if(res.code === 5004){
          this.resetMoney()
          if(res.data && res.data.indexOf("校验码数量失败")>-1){
            Dialog.alert({
              title: '系统提示',
              message:' 校验码使用失败！'
            })
          }else{
            this.orderCannotPay(res.msg)
          }

        }else if(res.code === 5006){
          this.resetMoney()
          this.orderCannotPay(res.msg)
        }else if(res.code === 5007){//201商户订单号重复
          this.clearCoupon()
          if( res.msg && res.msg.indexOf('201')>-1){//pc报201商户订单号重复
            Dialog.alert({
              title: '系统提示',
              message: "请前往PC端打开订单进行支付"
            })
          }else{
            Dialog.alert({
              title: '系统提示',
              message: res.msg
            })
          }
        }else{//报错
          this.resetMoney()
          Dialog.alert({
            title: '系统提示',
            message: res.msg
          })
        }
      }).catch((res)=>{
        this.resetMoney()
      })

    },
    aliPay(param){
      wapAliGoPay(param).then(res => {
        this.isLoading = false
        if(res.code ===200 && res.msg==="支付成功!"){
          this.orderSuccessPay(res.msg)
        }else if(res.code === 200 && res.data ){
          this.alipayFormData=res.data;
          let page = this.$router.resolve({name:'AlipayFormWap', query: { alipayFormData: this.alipayFormData }, });
          window.open(page.href,'_blank')
          setTimeout(()=>{
          this.showPayFinish=true
        },1000)
        }else if(res.code === 5004){
          this.resetMoney()
          if(res.data && res.data.indexOf("校验码数量失败")>-1){
            Dialog.alert({
              title: '系统提示',
              message:' 校验码使用失败！'
            })
          }else{
            this.orderCannotPay(res.msg)
          }

        }else if(res.code === 5006){
          this.resetMoney()
          this.orderCannotPay(res.msg)
        }else if(res.code === 5007){
          this.clearCoupon()
          Dialog.alert({
            title: '系统提示',
            message: res.msg
          })
        }else{//报错
          this.resetMoney()
          Dialog.alert({
            title: '系统提示',
            message: res.msg
          })
        }
      }).catch((res)=>{
        this.resetMoney()
      })
    },
    resetMoney(){
      this.preferential = ""
      this.couponPrice = null
      this.coupon = null
      this.isLoading = false

      if(this.orderMsg.ordersDetails[0].product.isActivityPrice === 1){
        this.needPay =Number(this.orderMsg.ordersDetails[0].product.activityPrice)
      }else{
        this.needPay =Number(this.orderMsg.ordersDetails[0].product.price)
      }
    },
    orderCannotPay(msg){
      Dialog.alert({
        title: '系统提示',
        message: msg
      }).then(() => {
        this.$emit("callbackOrderExpire")
      });
    },
    orderSuccessPay(msg){
      Dialog.alert({
        title: '系统提示',
        message: msg
      }).then(() => {
        this.$emit("callbackSuccess")
      });
    },
    protocolClick(record){
      this.htmlMsg = record.protocolContent
      this.showProtocol = true
    },
    couponBlur(){
      this.preferential = this.preferential ? this.preferential.trim() : ""
      if(this.preferential.length>0){
        return couponGetOne({
          "couponCode": this.preferential,
          "ticketId": this.orderMsg.ordersDetails[0].productId
        }).then(res => {
          if(res.code === 200 && res.data ){
            this.coupon = res.data
            if(this.orderMsg.ordersDetails[0].product.isActivityPrice === 1){
              let activityPrice = math.bignumber(this.orderMsg.ordersDetails[0].product.activityPrice)
              let couponPrice = math.bignumber(res.data.couponPrice)
              this.needPay = (math.subtract(activityPrice,couponPrice)<=0 )? 0 :math.format(math.subtract(activityPrice,couponPrice))
              this.couponPrice = math.format(math.subtract(activityPrice,this.needPay))
            }else{
              let price=math.bignumber(this.orderMsg.ordersDetails[0].product.price)
              let couponPrice =  math.bignumber(res.data.couponPrice)
              this.needPay =(math.subtract(price,couponPrice)<=0 )? 0 :math.format(math.subtract(price,couponPrice))
              this.couponPrice = math.format(math.subtract(price,this.needPay))
            }
          }else{
            this.preferential = ""
            this.couponPrice = null
            this.coupon = null
            this.needPay =this.orderMsg.ordersDetails[0].product.isActivityPrice === 1 ? this.orderMsg.ordersDetails[0].product.activityPrice:this.orderMsg.ordersDetails[0].product.price
            Dialog.alert({
              title: '系统提示',
              message: res.msg
            })
          }
        })
      }else{
        Dialog.alert({
          title: '系统提示',
          message: "请输入校验码"
        })
      }
    },
    clearCoupon(){
      this.couponPrice = null
      this.coupon = null
      if(this.orderMsg.ordersDetails[0].product.isActivityPrice === 1){
        this.needPay = Number(this.orderMsg.ordersDetails[0].product.isActivityPrice) > 0 ?Number(this.orderMsg.ordersDetails[0].product.activityPrice) : 0
      }else{
        this.needPay = Number(this.orderMsg.ordersDetails[0].product.price) > 0 ?Number(this.orderMsg.ordersDetails[0].product.price) : 0
      }
    }
  },


}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>

body{
  padding-right: 0px !important;
  overflow: hidden;
}
.el-form-item__label:before{
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}
.el-form-item__label{
  margin-bottom: 5px !important;
}

h3 {
  margin: 40px 0 0;
}
a {
  color: #42b983;
}
.registEnter{
  padding-bottom: 20px;
  /* margin: 0px 4%;
  width: 90%; */
  text-align: center;
}
.el-cascader{
  width: 100%;
}
.el-date-editor{
  width: 100% !important;
}
.el-select--smal{
  width: 100% !important;
}
.el-form-item__content,.el-radio-group{
  text-align: left !important;
}
.el-radio{
  margin-right: 10px !important;
}
.el-select, .el-select--small{
  width: 100% !important;

}
.el-form-item{
  margin-bottom: 15px !important;
}
.formEdite{
  text-align: left;
  margin: 10px 5% 0px 5%;
  padding: 20px 5%;
  width: 90%;
  background-color: #ffffff;
  border-radius: 10px;
}
.moneyShow{
  padding: 0px 0px;
}
.productMsg,.orderDetail{
  background:#ffffff;
  margin:5px;
  border-radius:5px
}
.productIcon{
  float:left;
  padding-left:10px;

}
.productAttribute{
  float:right;
  margin:auto;
  text-align:left;
  padding:5px;
}
.payMoneyMsg{
  padding:10px;
  margin:auto;
  text-align:right;
  font-size:16px;
}
.countDownTime{
  padding:10px;
  margin:auto;
  text-align:right;
  font-size:12px;
}



</style>
