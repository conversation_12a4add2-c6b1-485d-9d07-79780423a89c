<template>
    <van-count-down style="display: inline; color: red;font-size: 12px;" :time="timeData" format="mm:ss" />
</template>
  
  <script>
  import moment from 'moment'
  export default {
    name: 'OrderPay',
    props: {
        createTime:{required:true,type:undefined},
    },
    components:{
      
    },
    data(){
      return {
        timeData:"",
        Timer:null
      }
    },
    destroyed(){
      clearInterval(this.Timer)
      clearTimeout(this.Timer)
      this.Timer = null
    },
    mounted(){
      let start = moment()
      let end = moment(moment(this.createTime).add(15, 'minutes'))
      let count = end.diff(start,'milliseconds')
      if(count>0){
        this.Timer = setTimeout(()=>{
            this.$emit("callbackOrderExpire")
            clearTimeout(this.Timer)
            this.Timer = null
        },count)
        this.timeData = count
      }


    },
    methods:{
      
      
    },
     
    
  }
  </script>
  
  <!-- Add "scoped" attribute to limit CSS to this component only -->
  <style scoped>
  
  body{
    padding-right: 0px !important;
    overflow: hidden;
  }
  
  </style>
  