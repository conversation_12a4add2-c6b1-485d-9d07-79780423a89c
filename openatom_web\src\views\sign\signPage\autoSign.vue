<template>
  <div class="cus-sign">
    <el-form :model="searchForm" ref="searchForm" label-width="150px" class="demo-searchForm" style="width: 100%"
      @submit.native.prevent="stopHandleSubmit">
      <el-form-item label="活动名称:" prop="activityName">
        {{ this.activityName }}
      </el-form-item>
      <el-form-item label="姓名:" prop="name">
        {{ searchForm.name ? searchForm.name : "未查到相关信息" }}
      </el-form-item>
      <el-form-item label="手机号码:" prop="phone">
        {{ searchForm.phone ? searchForm.phone : "未查到相关信息" }}
      </el-form-item>
      <el-form-item label="邮箱:" prop="email">
        {{ searchForm.email ? searchForm.email : "未查到相关信息" }}
      </el-form-item>
      <el-form-item label="票种信息:" prop="ticketName">
        {{ searchForm.ticketName ? searchForm.ticketName : "未查到相关信息" }}
      </el-form-item>
      <el-form-item label="单位:" prop="company">
        {{ searchForm.company ? searchForm.company : "未查到相关信息" }}
      </el-form-item>
      <el-form-item label="身份证号:" prop="id_card">
        {{ searchForm.id_card ? searchForm.id_card : "未查到相关信息" }}
      </el-form-item>
      <el-form-item label="设备扫码:" prop="qrCode">
        <el-input ref="inputCode" v-model="searchForm.qrCode" style="width: 500px" @keyup.native.enter="handleEnter">
        </el-input>
        <span style="color:red;vertical-align:sub;padding-left:3px">请务必确保输入法为英文</span>
      </el-form-item>
      <el-form-item label="点击获取身份信息:" prop="getInfo">
        <el-button type="primary" @click="getInfo">获取身份信息</el-button>
      </el-form-item>
      <el-form-item label="签到状态:" prop="signFlag">
        <!-- {{this.signFlag}} -->
        <div style="font-size: 18px">
          <span :style="{ color: this.color }">
            {{ this.signFlag }}
          </span>
          <span :style="{ color: this.errColor }">
            {{ this.errMsg != "" ? "(" + this.errMsg + ")" : "" }}
          </span>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import request from "@/utils/request";
import moment from "moment";


import { textPrintData } from "@/utils/print/printData/Text";

moment.locale("zh-cn");
const defaultSearchForm = {
  phone: "",
  name: "",
  qrCode: "",

  pageNum: 1,
  pageSize: 10,
};
export default {
  data() {
    return {
      searchForm: {
        ...defaultSearchForm,
      },
      searchForm: { ...defaultSearchForm },
      rules: {},
      total: 0,
      searchInfo: {},
      checkList: [],
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      tableData: [],
      templateSelection: null,
      remark: "",
      activityId: "",
      btnloading: false,
      errMsg: "",
      signFlag: "未签到",
      color: "",
      errColor: "",
    };
  },
  props: {
    nMPrintSocket: {
      type: Object,
      default: null
    },
    printSocketOpen: {
      type: Boolean,
      default: false
    },
    jsonObj: {
      type: Object,
      default: null
    },
    batchPrintJob: {
      type: Function,
      default: () => { }
    }
  },
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activityId = activeInfo.activityId;
  },
  mounted() {
    this.$refs.inputCode.focus();
  },
  methods: {
    stopHandleSubmit(e) {
      e.preventDefault();
    },
    handleEnter(e) {
      if (this.searchForm.qrCode && this.searchForm.qrCode != "") {
        this.errMsg = "";
        this.errColor = "";
        this.signFlag = "";
        this.color = "";
        request({
          url:
            "/sign/getRegInfoByQrCode/" +
            this.activityId +
            "/" +
            this.searchForm.qrCode.trim(),
          method: "get",
        })
          .then((response) => {
            if (response.code == 200) {
              if (response.data) {
                this.searchForm = response.data;
                if (
                  response.data.signInfo &&
                  JSON.stringify(response.data.signInfo) != "{}"
                ) {
                  this.signFlag = "该人员已签到，请勿重复扫码";
                  this.color = "green";
                  this.errColor = "red";
                  this.errMsg =
                    "签到日期:" +
                    moment(response.data.signInfo.sign_time).format(
                      "YYYY-MM-DD HH:mm:ss"
                    );
                  this.$refs.inputCode.focus();
                } else {
                  this.signSuccess();
                }
                // 调用打印机功能打印签到二维码
                console.log(response, 'response')
                this.printMessage({ name: response.data?.name ?? "", company: response.data?.company ?? "" })
              }
            } else {
              this.searchForm = {
                phone: "",
                name: "",
                email: "",
                ticketName: "",
                company: "",
                id_card: "",
              };
              this.signFlag = "";
              this.color = "";
              this.errColor = "";
              this.errMsg = "";
              this.$refs.inputCode.focus();
            }
          })
          .catch((err) => {
            if (err && err.msg) {
              this.searchForm = {
                phone: "",
                name: "",
                email: "",
                ticketName: "",
                company: "",
                id_card: "",
                // qr_code:""
              };
              this.signFlag = "";
              this.color = "";
              this.errColor = "red";
              this.errMsg = err.msg;
              this.$refs.inputCode.focus();
            } else {
              this.searchForm = {
                qr_code: "",
              };
              this.signFlag = "";
              this.color = "";
              this.errMsg = "请扫描正确的二维码";
              this.errColor = "red";
              this.$refs.inputCode.focus();
            }
          });
      } else {
        this.errMsg = "请扫描正确的二维码";
        this.errColor = "red";
      }
    },
    signSuccess() {
      request({
        url: "/sign/success/" + this.activityId + "/" + this.searchForm.qr_code,
        method: "get",
      })
        .then((response) => {
          if (response.code == 200) {
            this.signFlag = "签到成功";
            this.color = "green";
            this.errMsg = "";
            this.errColor = "";
          } else {
            this.signFlag = "签到失败";
            this.color = "";
            this.errColor = "red";
            this.errMsg = "";
          }
        })
        .catch((err) => {
          this.signFlag = "签到失败";
          this.color = "";
          this.errColor = "red";
          this.errMsg = "";
          console.log(err);
        });
    },
    async fetchData(url) {
      try {
        const response = await fetch(url);
        // 检查响应状态是否正常
        if (!response.ok) {
          this.$message.error(response);
        }
        // 解析 JSON 数据
        return await response.json();
      } catch (error) {
        this.$message.error(error);
      }
    },
    getInfo() {
      this.fetchData('http://127.0.0.1:7846/readIDCard')
        .then(data => {
          console.log('获取的数据:', data);
          this.searchForm.qrCode = data.wzInfo;
          this.handleEnter();
        })
        .catch(error => {
          this.$message.error(error);
        });
    },
    printMessage(data) {
      if (!this.$props?.printSocketOpen) return console.log("打印服务未开启");
      let contentArr = [];
      contentArr.push(textPrintData(data));
      this.$props?.batchPrintJob(contentArr);
    },
  },
};
</script>
<style lang="scss">
.cus-sign {
  // display: flex;
  // align-items: center;
  // justify-content: center;
  width: 1000px;
  margin: 0 auto;

  .sign-button {
    border-radius: 10px;
    font-size: 15px;
    font-weight: bold;
    background: #fa9a2c;
    box-shadow: 0px 6px 10px 0px #fff1a5;
    box-shadow: 0px 6px 6px 0px #f9ffa5 inset;
    box-shadow: 0px -8px 8px 0px #fa772c inset;
  }
}
</style>
