<template>
  <div class="app-container">
    <div class="statistical" v-loading="loading" element-loading-text="获取图表数据中" element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(255, 255, 255, 0.9)">
      <Application></Application>
      <RegisterSource></RegisterSource>
      <Sign></Sign>
      <Ticket></Ticket>
      <Channel></Channel>
    </div>
  
  </div>
</template>
<script>
import Application from './ChartItem/Application'
import RegisterSource from './ChartItem/RegisterSource'
import Sign from './ChartItem/Sign'
import Ticket from './ChartItem/Ticket'
import Channel from './ChartItem/Channel.vue'
export default {
  props: {},
  components: {
    Application,
    RegisterSource,
    Sign,
    Ticket,
    Channel
  },
  data () {
    return {
      loading: false,
    }
  },
  destroyed () {
  },
  mounted () {
    this.loading = true
    setTimeout(() => {
      this.loading = false
    }, 500);
  },
  methods: {}
}
</script>
<style lang='scss'>
.statistical {
  height: calc(100vh - 104px);
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  .chart-content {
    // height: calc(50vh - 52px);
    width: calc(50% - 20px);
    padding: 15px;
    border: 1px solid rgb(201, 198, 198);
    // box-shadow: 3px 3px 3px 3px rgb(201, 198, 198);

    .top-chart {
      height: 225px;
    }

    // background: #f00;
  }

  // .chart-content:hover {
  //   object-fit: contain;
  //   -webkit-animation: scale 3s infinite;
  //   animation: scale 3s infinite;
  //   -webkit-transform: scale(1.02);
  //   transform: scale(1.02);
  // }

}
</style>
