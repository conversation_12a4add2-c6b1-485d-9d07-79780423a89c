<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd" >
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>答卷页面</title>
  <link href="js/plugs/jquery-ui-1.10.3.custom/css/mycss/jquery-ui-1.10.3.custom.css" rel="stylesheet" type="text/css" />
  <link rel="stylesheet" type="text/css" href="css/preview-dev.css" />
  <link rel="stylesheet" type="text/css" href="js/plugs/colpick-jQuery/css/colpick.css" />
  <link rel="stylesheet" type="text/css" href="js/plugs/validate/jquery.validate.css"/>
  <link rel="stylesheet" type="text/css" href="css/survey-common.css" />
  <link rel="stylesheet" type="text/css" href="css/answer-p.css" />
  <script src="js/dw/header.js" type="text/javascript"></script>
  <script type="text/javascript" src="js/plugs/jquery-ui-1.10.3.custom/js/jquery-1.10.1.js"></script>
  <script type="text/javascript" src="js/plugs/jquery-ui-1.10.3.custom/js/jquery-ui-1.10.3.custom.js"></script>
  <script type="text/javascript" src="js/plugs/colpick-jQuery/js/colpick.js"></script>
  <link rel="stylesheet" href="js/plugs/jQuery-File-Upload-9.19.2/css/jquery.fileupload.css">
  <link rel="stylesheet" href="js/plugs/jQuery-File-Upload-9.19.2/css/jquery.fileupload-ui.css">
  <script src="js/plugs/jQuery-File-Upload-9.19.2/js/vendor/jquery.ui.widget.js"></script>
  <script src="js/plugs/jQuery-File-Upload-9.19.2/js/jquery.fileupload.js"></script>
  <script src="js/plugs/jQuery-File-Upload-9.19.2/js/jquery.iframe-transport.js"></script>
  <script type="text/javascript" src="js/plugs/laydate/laydate.js"></script>
  <script type="text/javascript" src="js/common/ans-common.js"></script>
  <script type="text/javascript" src="js/common/common.js"></script>
  <script type="text/javascript" src="js/dw/an/an-pcm-common.js"></script>
  <!-- <script type="text/javascript" src="js/dw/an/ans-p-data.js"></script>
  <script type="text/javascript" src="js/dw/an/ans-p.js"></script> -->
  <script src="https://openatom.cn/trivial-services/stats/index/entry?id=8&auto=no"></script>
  <script type="text/javascript">
    $(document).click(function(){
      $(".tabbarDialog",parent.document).hide();
      $(".js-tabselected",parent.document).removeClass("js-tabselected");
    });
    $(document).ready(function(){
        let codeNode = window.location.href.split("=")[1];
        let url=develop=="test"?"https://www.ican365.net/atomgitTest/bk/front/login/oauth/access_token":develop=="prod"?"https://openatomcon.openatom.cn/atomgit/bk/front/login/oauth/access_token":"https://openatomcon.openatom.cn/atomgit/bk/front/login/oauth/access_token"
        $.ajax({
      url: url,
      contentType: 'application/json', 
      data: JSON.stringify({code:codeNode}),
      type: "post",
      // headers:{Authorization:headerComfig},
      success: function (httpResult){
          console.log(httpResult)
        if(httpResult.code===200){
            document.cookie = "Acces-Token="+httpResult.accessToken+";path=/";
             window.location.href=window.location.origin+"/dwsurvey/static/diaowen/answer-p.html?sid="+window.localStorage.getItem("answerId")
        }
      },
      error: function(xmlHttpRequest, textStatus, errorThrown){
        // alert(xmlHttpRequest);
       
      }
    });
  });


  </script>
</head>
<body>
<div id="wrap">
  <div id="preview_head_top" style='text-align: center;background: #aaa;padding: 10px;color: #f3f3f3;display: none;'>当前为预览模式</div>
  <input type="hidden" id="id" name="id" value="">
  <input type="hidden" id="sid" name="sid" value="${survey.sid }">
  <input type="hidden" id="ctxApp" name="ctx" value="/api/dwsurvey/app">
  <input type="hidden" id="ctx" name="ctx" value="/api/dwsurvey/anon">
  <input type="hidden" id="prevHost" value="http://ent.surveyform.cn/">
  <input type="hidden" id="tag" name="tag" value="${tag }">
  <input type="hidden" id="laydatetag" name="laydatetag" value="laydatetag">
  <input type="hidden" id="anClient" name="anClient" value="p">
  <input type="hidden" id="answerLoadTag" name="answerLoadTag" value="0">
  <input type="hidden" id="breakpoint1" name="breakpoint1" value="0">
  <form id="surveyForm" action="/dwsurvey/bk/api/dwsurvey/anon/response/save.do" method="post" >
    <input type="hidden" id="surveyId" name="surveyId" value="${survey.id }">
    <input type="hidden" id="ruleCode" name="ruleCode" value="">
    <input type="hidden" id="surveyLogId" name="surveyLogId" value="${survey.surveyLogId }">
    <div id="dw_body" style="padding-top:10px;">
      <div id="dw_body_content">
        <div id="dwSurveyHeader">

          <div id="dwSurveyTitle" class="noLogoImg">
            <div id="dwSurveyName" class="editAble dwSvyName">加载中</div>
          </div>
          <div id="dwSurveyNote">
            <div id="dwSurveyNoteTools">参考样例</div>
            <div id="dwSurveyNoteEdit"  class="editAble">加载中</div>
          </div>
        </div>

        <div id="dwSurveyQuContent" style="min-height: 300px;">
          <div id="dwSurveyQuContentBg">
            <!-- <div style="border-top: 3px solid #81AB00;margin:0px auto;padding-bottom: 15px;"></div> -->
            <ul id="dwSurveyQuContentAppUl">
              <!-- 题目内容 -->




            </ul>
          </div>
        </div>
        <div id="resultProgressRoot">
          <div class="progress-label">完成度：0%</div>
          <div id="resultProgress" class="progressbarDiv"></div>
        </div>

      </div>
    </div>
  </form>

</div>

<div style="display: none;">

  <div>
    <ul id="radioQuModel" >
      <li class="li_surveyQuItemBody" >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="RADIO" >
            <input type="hidden" class="quId" value="${en.id }"  >
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="hv" value="">
            <input type="hidden" class="answerTag" value="0" >
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <div class="quLogicInputCase">

            </div>
          </div>
          <div class="surveyQuItem">

            <div class="surveyQuItemContent">
              <div class="quCoTitle">
                <div class="quCoNum">${i.count }、</div>
                <div class="editAble quCoTitleEdit" >${en.quTitle}</div>
              </div>
              <div class="quCoItem">

              </div>
            </div>

          </div>
        </div>
      </li>
    </ul>

    <ul id="checkboxQuModel" >
      <li class="li_surveyQuItemBody" >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="CHECKBOX">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="answerTag" value="0" >
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <input type="hidden" class="paramInt01" value="${en.paramInt01 }">
            <input type="hidden" class="paramInt02" value="${en.paramInt02 }">
            <div class="quLogicInputCase">

            </div>
            <input type="hidden" class="checkbox_input_tag" name="qu_${en.quType }_${en.id }" value="tag_qu_${en.quType }_${en.id }_" />
          </div>
          <div class="surveyQuItem">

            <div class="surveyQuItemContent">
              <div class="quCoTitle">
                <div class="quCoNum">${i.count }、</div>
                <div class="editAble quCoTitleEdit" >${en.quTitle}</div>
              </div>
              <div class="quCoItem">

              </div>

            </div>

          </div>
        </div>
      </li>
    </ul>

    <ul id="fillblankQuModel">
      <li class="li_surveyQuItemBody" >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="FILLBLANK">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="checkType" value="${en.checkType }">
            <input type="hidden" class="answerTag" value="0" >
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <input type="hidden" class="paramInt01" value="${en.paramInt01}" >
            <div class="quLogicInputCase">

            </div>
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent">
              <div class="quCoTitle">
                <div class="quCoNum">${i.count }、</div>
                <div class="editAble quCoTitleEdit" >${en.quTitle}</div>
              </div>
              <div class="quCoItem"><ul>
                <li class="quCoItemUlLi">
                  <div class="quFillblankItem">

                  </div>
                </li>
              </ul>
              </div>
            </div>

          </div>
        </div>
      </li>
    </ul>

    <ul id="scoreQuModel">
      <li class="li_surveyQuItemBody" >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="SCORE">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <div class="quLogicInputCase">

            </div>
            <input type="hidden" class="hidden_input_tag"  name="qu_${en.quType }_${en.id }" value="item_qu_${en.quType }_${en.id }_" />
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent">
              <div class="quCoTitle">
                <div class="quCoNum">${i.count }、</div>
                <div class="editAble quCoTitleEdit" >${en.quTitle}</div>
              </div>
              <div class="quCoItem">
                <table class="quCoItemTable" cellpadding="0" cellspacing="0">

                </table>
              </div>
            </div>

          </div>
        </div>
      </li>
    </ul>>

    <ul id="orderQuModel">
      <li class="li_surveyQuItemBody" >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="ORDERQU">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="answerTag" value="0" >
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <div class="quLogicInputCase">

            </div>
            <div class="quItemInputCase">

            </div>
            <input type="hidden" class="hidden_input_tag" name="qu_${en.quType }_${en.id }" value="item_qu_${en.quType }_${en.id }_" />
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent">
              <div class="quCoTitle">
                <div class="quCoNum">${i.count }、</div>
                <div class="editAble quCoTitleEdit" >${en.quTitle}</div>
              </div>
              <div class="quCoItem quOrderByCoItem">
                <div class="quOrderByRight">
                  <table class="quOrderByTable" style="padding:5px;">

                  </table>
                </div>
                <div  class="quOrderByLeft">
                  <ul class="quOrderByLeftUl">

                  </ul>
                </div>
                <div style="clear: both;"></div>
              </div>
            </div>

          </div>
        </div>
      </li>
    </ul>>

    <ul id="pageQuModel">
      <li class="li_surveyQuItemBody"  >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="PAGETAG">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <div class="quLogicInputCase">

            </div>
          </div>
          <div class="surveyQuItem">
            <div class="pageBorderTop nohover"  ></div>
            <div class="surveyQuItemContent" style="padding-top:12px; height:30px;min-height: 30px;">
              <a href="#" class="sbtn24 sbtn24_0 nextPage_a" >下一页</a>&nbsp;&nbsp;
              <input type="hidden" name="nextPageNo" value="${pageNo }">
            </div>
          </div>
        </div>
      </li>
    </ul>

    <ul id="paragraphQuModel">
      <li class="li_surveyQuItemBody"  >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="PARAGRAPH">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <div class="quLogicInputCase">

            </div>
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent" style="min-height: 35px;">
              <div class="quCoTitle" style="background: rgb(243, 247, 247);">
                <div class="editAble quCoTitleEdit" style="padding-left: 15px;">${en.quTitle}</div>
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>

    <ul id="mfillblankQuModel">
      <li class="li_surveyQuItemBody" >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="MULTIFILLBLANK">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <!--            <input type="hidden" class="answerTag" value="0" >-->
            <div class="quLogicInputCase">

            </div>
            <input type="hidden" class="hidden_input_tag" name="qu_${en.quType }_${en.id }" value="text_qu_${en.quType }_${en.id }_" />
          </div>
          <div class="surveyQuItem">

            <div class="surveyQuItemContent">
              <div class="quCoTitle">
                <div class="quCoNum">${i.count }、</div>
                <div class="editAble quCoTitleEdit" >${en.quTitle}</div>
              </div>
              <div class="quCoItem">
                <table class="mFillblankTable" cellpadding="0" cellspacing="0">

                </table>

              </div>
            </div>

          </div>
        </div>
      </li>
    </ul>

    <ul id="uploadFileQuModel">
      <li class="li_surveyQuItemBody" >
        <div class="surveyQuItemBody">
          <div class="initLine"></div>
          <div class="quInputCase" style="display: none;">
            <input type="hidden" class="quType" value="UPLOADFILE">
            <input type="hidden" class="quId" value="${en.id }">
            <input type="hidden" class="orderById" value="${en.orderById }"/>
            <input type="hidden" class="isRequired" value="${en.isRequired }">
            <input type="hidden" class="checkType" value="${en.checkType }">
            <input type="hidden" class="answerTag" value="0" >
            <input type="hidden" class="paramInt01" value="${en.paramInt01 }">
            <input type="hidden" class="paramInt02" value="${en.paramInt02 }">

            <input type="hidden" class="randOrder" value="${en.randOrder }">
            <div class="quLogicInputCase"></div>
          </div>
          <div class="surveyQuItem">
            <div class="surveyQuItemContent">
              <div class="quCoTitle">
                <div class="quCoNum">${i.count }、</div>
                <div class="editAble quCoTitleEdit" >${en.quTitle}</div>
              </div>
              <div class="quCoItem"><ul>
                <li class="quCoItemUlLi">
                  <div class="quFillblankItem">
                    <div class="uploadFileTypeNoteSpan" style="color: grey;line-height: 23px;padding-bottom: 5px;">
                      <span class="uploadFileTypeNote" ></span>
                      <span>单个文件最大支持 <label class="uploadFileMaxSize">${en.paramInt02}</label> M</span>
                    </div>
                    <div class="upFileDiv"></div>
                    <div>
                      <!-- The fileinput-button span is used to style the file input field as button -->
                      <span class="sbtn sbtn_0 fileinput-button">
                        <span>上传文件</span>
                        <input class="fileupload" type="file" name="file" multiple>
                        <input class="fileuploadPath" type="hidden" uphidinputname="qu_${en.quType }_${en.id }" >
                      </span>
                      <!-- The global progress bar -->
                      <div class="progress" style="display: none;margin-top: 10px;">
                        <div class="progress-bar progress-bar-success"></div>
                      </div>
                      <!-- The container for the uploaded files -->
                      <div class="files"></div>
                    </div>

                    <input type="hidden" class="answerTag" value="0" >

                    <div class="dwComEditMenuBtn" ></div>
                  </div>
                </li>
              </ul>
              </div>
            </div>

          </div>
        </div>
      </li>
    </ul>

    <ul id="dwSubmitModel">
      <li class="li_surveyQuItemBody surveySubmitBody" >
        <div class="surveyQuItemBody">
          <div class="surveyQuItem">
            <div id="jcaptchaImgBody" class="r-qu-body" style="display: none;">
              <div class="frmItem" >
                <label class="frm_label">验证码</label>
                <div class="frm_controls">
											<span class="auth-code" id="verifycodeImgArea">
											<input name="jcaptchaInput" type="text" class="" style="width:100px;" autocomplete="off">
											<img id="register-jcaptchaImg" onclick="refreshAutoCode('register-jcaptchaImg')" src="/dwsurvey/bk/api/dwsurvey/anon/jcap/jcaptcha.do" alt="验证码" height="40"></span>
                  <a href="javascript:refreshAutoCode('register-jcaptchaImg');" style="margin-left: 5px;" hidefocus="true">换一张</a>
                  <span class="frm_desc">输入下面图片的字符，区分大小写</span>
                  <p class="valid-msg fail" style="display: none;"><i>●</i><span class="msg_content">验证码错误，请重新输入</span></p>
                </div>
              </div>
              <div class="errorItem" style="display: none;"><label class="error">验证码错误，请重新输入！</label></div>
            </div>
            <input type="hidden" class="quType" value="submitSurveyBtn">
            <input type="hidden" class="pageNo" value="${pageNo}" >
            <div class="surveyQuItemContent" style="padding: 12px 0px;height: 30px;min-height: 30px;">
              <a href="#" id="submitSurvey" class="sbtn24 sbtn24_0 submitSurvey" disabled>提&nbsp;交</a>&nbsp;&nbsp;
              <input type="hidden" name="prevPageNo" value="${pageNo-1 }">
              <input type="hidden" name="nextPageNo" value="${pageNo }">
            </div>
          </div>
        </div>
      </li>
    </ul>

  </div>

  <div id="quLogicItemModel">
    <div class="quLogicItem">
      <input type="hidden" class="cgQuItemId" value="${quLogicEn.cgQuItemId }"/>
      <input type="hidden" class="skQuId" value="${quLogicEn.skQuId }"/>
      <input type="hidden" class="logicId" value="${quLogicEn.id }"/>
      <input type="hidden" class="geLe" value="${quLogicEn.geLe }"/>
      <input type="hidden" class="scoreNum" value="${quLogicEn.scoreNum }"/>
      <input type="hidden" class="logicType" value="${quLogicEn.logicType }"/>
    </div>
  </div>

  <ul id="quRadioItem_default">
    <li class="quCoItemUlLi">
      <div class="dwQuOptionItemContent">
        <label class="dwRedioStyle dwQuInputLabel" ></label>
        <input type="radio" name="qu_${en.quType }_${en.id }" value="${item.id }" ><label class="editAble quCoOptionEdit quCoOptionPadding">${item.optionName }</label>
        <input type='text' class='inputSytle_1 dwQuOptionItemNote'  style="width:200px;padding:5px;display: none;"   name="text_qu_${en.quType }_${en.id }_${item.id }"  />
        <div class="quItemInputCase">
          <input type="hidden" class="isNote" value="${item.isNote }">
          <input type="hidden" class="checkType" value="${item.checkType }">
          <input type="hidden" class="isRequiredFill" value="${item.isRequiredFill }">
        </div>
      </div>
    </li>
  </ul>

  <div id="radioSelectOption">
    <div class="quItemInputCase" itemid="${item.id }">
      <input type="hidden" class="isNote" value="${item.isNote }">
      <input type="hidden" class="checkType" value="${item.checkType }">
      <input type="hidden" class="isRequiredFill" value="${item.isRequiredFill }">
    </div>
    <input type='text' class='inputSytle_1 dwQuOptionItemNote'  style="width:200px;padding:5px;display: none;"   name="text_qu_${en.quType }_${en.id }_${item.id }"  />
  </div>

  <ul id="quCheckboxItem_default">
    <li class="quCoItemUlLi">
      <div class="dwQuOptionItemContent">
        <label class="dwCheckboxStyle dwQuInputLabel" ></label>
        <input type="checkbox" name="tag_qu_${en.quType }_${en.id }_${item.id }"  value="${item.id }" ><label class="editAble quCoOptionEdit quCoOptionPadding">${item.optionName }</label>
        <input type='text' class='inputSytle_1 dwQuOptionItemNote'  style="width:200px;padding:5px;display:none;"  name="text_tag_qu_${en.quType }_${en.id }_${item.id }" />
        <div class="quItemInputCase">
          <input type="hidden" class="isNote" value="${item.isNote }">
          <input type="hidden" class="checkType" value="${item.checkType }">
          <input type="hidden" class="isRequiredFill" value="${item.isRequiredFill }">
        </div>
      </div>
    </li>
  </ul>

  <table id="quScoreItemModel">
    <tr class="quScoreOptionTr">
      <td class="quCoItemTableTd quOptionEditTd">
        <label class="editAble quCoOptionEdit">${item.optionName }</label>
        <input class="dwScoreOptionId" value="${item.id }" disabled="disabled" type="hidden"/>
        <input type="hidden" class="answerTag" value="0" >
      </td>
      <td class="quCoItemTableTd">
        <table class="scoreNumTable" border="0" cellspacing="0" cellpadding="1" ><tr>  </tr></table>
        <input name="item_qu_${en.quType }_${en.id }_${item.id }" value=""  type="hidden" class="scoreNumInput" >
      </td>
      <td class="quCoItemTableTd scoreNumText">分</td>
    </tr>
  </table>

  <table id="quOrderItemRightModel">
    <tr class="quOrderByTableTr"><td class="quOrderyTableTd">${itemVarStatus.count }</td><td class="quOrderTabConnect"></td></tr>
  </table>

  <ul id="quOrderItemLeftModel">
    <li class="quCoItemUlLi">
      <label class="editAble quCoOptionEdit">
        <span>${item.optionName }</span>
        <input  name="item_qu_${en.quType }_${en.id }_${item.id }"  value="" type="hidden" class="quOrderItemHidInput" >
        <input  value="" type="hidden" class="quItemId" >
      </label>
    </li>
  </ul>

  <table id="mFillblankTableModel" >
    <tr class="mFillblankTableTr">
      <td align="right" class="mFillblankTableEditTd"><label class="editAble quCoOptionEdit">${item.optionName }</label>
        <input class="dwMFillblankOptionId" value="${item.id }" disabled="disabled" type="hidden"/>
        <input type="hidden" class="answerTag" value="0" >
      </td>
      <td><input name="text_qu_${en.quType }_${en.id }_${item.id }"  type="text" style="width:200px;padding:5px;" class="inputSytle_1 dwMFillblankInput"></td>
    </tr>
  </table>

</div>

<div id="fixedMsg" style="position: fixed;top: 0px;width: 100%;padding: 10px;text-align: center;font-size: 18px;letter-spacing: 4px;line-height: 56px;background-color: #111;background-color: rgba(17,17,17,0.5);color: #fff;color: rgba(255,255,255,0.5);z-index: 200;display: none;"></div>
<script type="text/javascript">
  var bfbFloat=0;
  $("#resultProgress").progressbar({value: bfbFloat,orientation: "vertical"});
</script>

<!-- Diaowen.net Button BEGIN -->
<div id="webSiteFixedRight" class="websiteFixed" style="position: fixed;right: 0px;top: 50px;z-index: 9999;">
  <a id="mobileTdId" href="＃" style="background: #1C658B;width: 15px;background: #8CBCD1;display: block;padding: 5px;padding-top: 10px;padding-bottom:10px;font-weight: bold;color: white;cursor: pointer;float: right;vertical-align: middle;text-decoration: none;font-size: 12px;">手机地址</a>
  <img class="mobileAnswerQR" alt="" src="" height="130" style="padding: 10px;background: white;display: none;" />
</div>
<!-- Diaowen.net Button END -->

</body>
</html>
