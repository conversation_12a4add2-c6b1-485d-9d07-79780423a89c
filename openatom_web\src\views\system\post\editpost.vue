<template>
  <div class="app-container">
    <div style="width: 600px; margin: 0 auto">
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-form-item
          v-if="form.activityId && form.activityId != ''"
          label="活动编号"
          prop="activityId"
        >
          <el-input
            :disabled="form.activityId && form.activityId != ''"
            v-model="form.activityId"
            placeholder="请输入活动编号"
          />
        </el-form-item>
        <el-form-item label="活动类型" prop="activityType">
          <el-select
            v-model="form.activityType"
            placeholder="请选择活动类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in typeList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动属性" prop="activityNameType">
          <el-select
            v-model="form.activityNameType"
            placeholder="请选择活动属性"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in activetypeList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动名称" prop="activityName">
          <el-input
            v-model="form.activityName"
            placeholder="请输入活动名称"
            maxlength="150"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="是否自动审批" prop="isAutoApproval">
          <el-select
            v-model="form.isAutoApproval"
            placeholder="请选择是否自动审批"
            clearable
            style="width: 100%"
          >
            <el-option label="是" :value="1" />
            <el-option label="否" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item prop="isBuyTicket">
          <span slot="label"
            >是否售票
            <el-tooltip class="item" effect="dark" placement="top-start">
              <template slot="content">
                <div>是否售票选择后,无法修改,请谨慎操作</div>
              </template>
              <i
                class="el-icon-warning-outline"
                style="padding-right: 5px"
              ></i> </el-tooltip
          ></span>
          <el-select
            :disabled="form.activityId != undefined"
            v-model="form.isBuyTicket"
            placeholder="请选择是否售票"
            clearable
            style="width: 100%"
          >
            <el-option label="是" :value="1" />
            <el-option label="否" :value="-1" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否需要展商" prop="haveExhibitors">
          <!-- <el-input v-model="form.isAutoApproval" placeholder="请选择是否自动审批" /> -->
          <el-select
            v-model="form.haveExhibitors"
            placeholder="请选择是否需要展商"
            clearable
            style="width: 100%"
          >
            <el-option label="是" :value="1" />
            <el-option label="否" :value="2" />
          </el-select>
        </el-form-item>
         <el-form-item label="短信邮件发送" prop="sendType">
          <el-select
            v-model="form.sendType"
            placeholder="请选择短信邮件发送类型"
            clearable
            style="width: 100%"
          >
           <el-option label="正常发送(包含电子票链接)" :value="1" />
            <el-option label="正常发送(不包含电子票链接)" :value="2" />
              <el-option label="不发送" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="活动地点" prop="activityPlace">
          <el-input
            v-model="form.activityPlace"
            placeholder="请输入活动地点"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="活动简介" prop="activityDescription">
          <el-input
            v-model="form.activityDescription"
            type="textarea"
            :rows="5"
            placeholder="请输入活动简介"
          />
        </el-form-item>
        <el-form-item label="活动周期" prop="stratEndTime">
          <el-date-picker
            v-model="form.stratEndTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="submitForm" :loading="btnloading"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>
    <!-- </el-dialog> -->
  </div>
</template>

<script>
import {
  listPost,
  getPost,
  delPost,
  addPost,
  updatePost,
} from "@/api/system/post";
import request from "@/utils/request";
import moment from "moment";
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser,
  resetUserPwd,
  changeUserStatus,
  deptTreeSelect,
  atomList,
} from "@/api/system/user";
import { checkActiveName } from "@/utils/validate";
moment.locale("zh-cn");
export default {
  name: "editpost",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 岗位表格数据
      postList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchParam: undefined,
        // postCode: undefined,
        // postName: undefined,
        // status: undefined,
      },
      // 表单参数
      form: {
        activityName: undefined,
        activityDescription: undefined,
        activityPlace: undefined,
        activityType: undefined,
        stratEndTime: undefined,
        isAutoApproval: undefined,
        activityNameType: undefined,
        isBuyTicket: 1,
        haveExhibitors: undefined,
        sendType:undefined
      },
      // 表单校验
      rules: {
        activityName: [
          { required: true, message: "活动名称不能为空", trigger: "blur" },
          { required: true, validator: checkActiveName, trigger: "blur" },
        ],
        activityDescription: [
          { required: true, message: "活动简介不能为空", trigger: "blur" },
        ],
        stratEndTime: [
          { required: true, message: "活动周期不能为空", trigger: "blur" },
        ],
        activityType: [
          { required: true, message: "活动类型不能为空", trigger: "change" },
        ],
        isAutoApproval: [
          { required: true, message: "是否审批不能为空", trigger: "change" },
        ],
        activityPlace: [
          { required: true, message: "活动地点不能为空", trigger: "blur" },
          { required: true, validator: checkActiveName, trigger: "blur" },
        ],
        activityNameType: [
          { required: true, message: "活动属性不能为空", trigger: "change" },
        ],
        isBuyTicket: [
          { required: true, message: "是否购票不能为空", trigger: "change" },
        ],
        haveExhibitors: [
          { required: true, message: "是否需要展商不能为空", trigger: "change" },
        ],
          sendType:[
           { required: true, message: "短信邮件发送类型不能为空", trigger: "change" },
        ]
      },
      flowRules: {
        flowno: [
          { required: true, message: "请选择报名流程", trigger: "blur" },
        ],
      },
      flowOpen: false,
      flowform: { flowno: undefined },
      flowOptions: [],
      flowDisabled: false,
      activeOpen: false,
      activeform: {},
      activeList: [],
      Activeloading: false,
      activeQuery: {
        pageNum: 1,
        pageSize: 10,
      },
      activetotal: 0,
      sysUserOpen: false,
      sysUserListform: {},
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
      },
      // 列信息
      columns: [
        { key: 1, label: `用户名称`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 6, label: `邮箱`, visible: true },
      ],
      userList: [],
      selectedUser: [],
      typeList: [],
      activetypeList: [],
      btnloading: false,
    };
  },
  created() {
    this.getTypeList();
    this.getActivetypeList();
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.form = { ...activeInfo };
    this.form.activityType = activeInfo.activityType.toString();
    // this.form={...activeInfo}
    this.form.stratEndTime = [];
    this.form.stratEndTime.push(activeInfo.startDate);
    this.form.stratEndTime.push(activeInfo.endDate);
    this.form.activityNameType = activeInfo.activityNameType.toString();
  },
  methods: {
    getTypeList() {
      request({
        url: "/system/dict/data/list?pageNum=1&pageSize=100&dictType=activity_type&status=0",
        method: "get",
      })
        .then((response) => {
          this.typeList = response.rows;
        })
        .catch((err) => {
          console.log(err);
        });
    },

    // 取消按钮
    cancel() {
      this.$parent.ActiveEditCancel();
      this.form = {
        activityName: undefined,
        activityDescription: undefined,
        activityPlace: undefined,
        activityType: undefined,
        stratEndTime: undefined,
        isAutoApproval: undefined,
        activityNameType: undefined,
        isBuyTicket: 1,
        haveExhibitors: undefined,
      };
      // this.$router.push("/activity/post");
    },
    getActivetypeList() {
      request({
        url: "/system/dict/data/list?pageNum=1&pageSize=100&dictType=activity_name_type&status=0",
        method: "get",
      })
        .then((response) => {
          this.activetypeList = response.rows;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        // this.form.isBuyTicket = 1;
        if (valid) {
          this.btnloading = true;
          this.form.startDate = this.form.stratEndTime[0];
          this.form.endDate = this.form.stratEndTime[1];
          if (this.form.activityId != undefined) {
            updatePost(this.form)
              .then((response) => {
                this.$modal.msgSuccess("修改成功");

                sessionStorage.setItem("activity", JSON.stringify(this.form));

                this.$parent.ActiveEditCancel();
                this.$parent.setActiveImfo(this.form);
                this.btnloading = false;
              })
              .catch(() => {
                this.btnloading = false;
              });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.btnloading = false;
            });
          }
        }
      });
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss">
.activeDec {
  height: 150px;
  overflow: auto;
  border: 1px solid #ccc;
  border-radius: 3px;
  padding: 5px;
}
.selfForm .el-form-item {
  margin-bottom: 10px;
}
.selfDialog .el-dialog__body {
  padding: 10px;
}
// ::v-deep .el-tooltip__popper{
//   min-width: 10px !important;
//   max-width: 300px !important;
// }
.el-tooltip__popper.is-dark {
  min-width: 10px !important;
  max-width: 300px !important;
}
</style>
