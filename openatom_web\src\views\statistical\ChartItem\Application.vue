<template>
  <div class="chart-content">
    <div class="top-chart"></div>
    <el-table size="mini" :data="tableData">
      <el-table-column label="" prop="name"></el-table-column>
      <el-table-column
        v-for="(item, index) in xData"
        :key="index"
        :label="item"
        :prop="item"
      ></el-table-column>
    </el-table>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { queryTicket } from "@/api/statistical/index";
export default {
  props: {},
  components: {},
  data() {
    return {
      chart1: null,
      activityId: "",
      chartData: null,
      yData: [],
      xData: [],
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      tableData: [],
      option: {
        title: {
          text: "票种统计(报名通过)",
          left: "top",
        },
        grid: { left: "16%", right: "10%", bottom: "10%", top: "25%" },
        tooltip: {
          trigger: "axis",
          axisPointer: { type: "shadow" },
          formatter: "{b}: {c} (张)",
        },

        xAxis: {
          type: "category",
          data: [],
        },
        yAxis: {
          name: "单位：张",
          type: "value",
          axisLine: {
            show: true,
          },
        },
        series: [
          {
            label: {
              show: true,
              position: "top",
              valueAnimation: true,
            },
            barGap: 0,
            barWidth: 20,
            data: [],
            type: "bar",
            showBackground: true,
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
          },
        ],
      },
    };
  },
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activityId = activeInfo.activityId;
  },
  destroyed() {},
  mounted() {
    this.initChart();
   
  },
  methods: {
    async initChart() {
      this.xData = [];
      this.yData = [];
      let res = await queryTicket(this.activityId);

      this.chartData = res.data;
      let data = { ...res.data };
      data.name = "票数";

      if (JSON.stringify(res.data) !== "{}") {
        this.tableData.push(data);
      }
      for (const key in res.data) {
        if (Object.hasOwnProperty.call(res.data, key)) {
          const element = res.data[key];
          this.xData.push(key);
          this.yData.push(element);
        }
      }

      this.drawChart();
    },
    drawChart() {
      this.chart1 = echarts.init(
        document.getElementsByClassName("top-chart")[0]
      );
      this.option.series[0].data = this.yData;
      this.option.xAxis.data = this.xData;
      this.chart1.setOption(this.option);
    //    window.onresize = () => {
    //   this.chart1.resize();
    // };
    },
  },
};
</script>
<style lang='scss'></style>
