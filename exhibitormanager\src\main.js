import Vue from 'vue'
import 'element-ui/lib/theme-chalk/index.css';
// import iView from 'iview'
// import 'iview/dist/styles/iview.css'
import formCreate from '@form-create/element-ui'
// import Element from 'element-ui'
import App from './App'
import router from './router'

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

// Vue.use(Element)
// Vue.use(iView)
Vue.use(formCreate)
Vue.config.productionTip = false

import {
  Input,
  Radio,
  RadioGroup,
  RadioButton,
  Checkbox,
  Select,
  Option,
  OptionGroup,
  Button,
  DatePicker,
  TimePicker,
  Form,
  FormItem,
  Row,
  Col,
  Cascader,
  Link,
  Card,
  MessageBox,
  Message,
  Menu,
  MenuItem,
  Avatar,
  Steps,
  Step,
  Dialog,
  Popover,
  Image,
  Tooltip,
  Descriptions,
  DescriptionsItem,
  tabs,
  TabPane,
  Loading,
  Popconfirm,
  Collapse,
  collapseItem,
  Table,
  TableColumn,
  Upload,
  Pagination
  // CheckboxButton,
  // CheckboxGroup,
  // ButtonGroup,
  // Calendar,
  // TimeSelect,
  // InputNumber,
  // Switch,
  // Progress,
  // Rate,
}from "element-ui"
Vue.use(Upload);
Vue.use(tabs);
Vue.use(TabPane);
Vue.use(Menu);
Vue.use(Descriptions);
Vue.use(DescriptionsItem);
Vue.use(MenuItem);
Vue.use(Avatar);
Vue.use(Steps);
Vue.use(Step);
Vue.use(Dialog);
Vue.use(Popover);
Vue.use(Image);
Vue.use(Tooltip);
Vue.use(Input);
Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(RadioButton);
Vue.use(Checkbox);
Vue.use(Select);
Vue.use(Option);
Vue.use(OptionGroup);
Vue.use(Button);
Vue.use(TimePicker);
Vue.use(DatePicker);
Vue.use(Form);
Vue.use(FormItem);
Vue.use(Row);
Vue.use(Col);
Vue.use(Cascader);
Vue.use(Card);
Vue.use(Link);
Vue.use(Loading);
Vue.use(Popconfirm);
Vue.use(Collapse)
Vue.use(collapseItem)
Vue.use(Table);
Vue.use(TableColumn);
Vue.use(Pagination)
// Vue.use(MessageBox);
// Vue.use(CheckboxButton);
// Vue.use(CheckboxGroup);
// Vue.use(ButtonGroup);
// Vue.use(Calendar);
// Vue.use(TimeSelect);
// Vue.use(InputNumber);
// Vue.use(Switch);
// Vue.use(Progress);
// Vue.use(Rate); 
Vue.prototype.$msgbox = MessageBox
Vue.prototype.$message = Message;
// Vue.prototype.$alert = MessageBox.alert;
// Vue.prototype.$confirm = MessageBox.confirm;
// Vue.prototype.$prompt = MessageBox.prompt;
Vue.prototype.$ELEMENT = { size: 'medium', zIndex: 3000 };
new Vue({
  el: '#app',
  router,
  render: h => h(App)
})
