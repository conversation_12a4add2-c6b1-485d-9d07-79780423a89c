{"name": "zeroclipboard/zeroclipboard", "description": "The ZeroClipboard library provides an easy way to copy text to the clipboard using an invisible Adobe Flash movie and a JavaScript interface.", "version": "2.2.0-beta.1", "type": "library", "keywords": ["flash", "clipboard", "copy", "cut", "paste", "zclip", "clip", "clippy"], "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "http://jonrohan.me/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jamesgreene.net/", "role": "Developer"}], "homepage": "http://zeroclipboard.org/", "support": {"source": "https://github.com/zeroclipboard/zeroclipboard.git", "issues": "https://github.com/zeroclipboard/zeroclipboard/issues"}}