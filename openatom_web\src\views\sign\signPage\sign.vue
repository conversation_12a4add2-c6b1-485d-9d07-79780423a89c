<template>
  <div class="cus-sign">
    <el-form :model="searchForm" ref="searchForm" label-width="100px" class="demo-searchForm" style="width: 100%">
      <el-form-item label="活动名称:" prop="activityName">
        {{ this.activityName }}
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="searchForm.name" style="width: 500px"> </el-input>
      </el-form-item>
      <el-form-item label="手机号码" prop="region">
        <el-input v-model="searchForm.phone" style="width: 500px"> </el-input>
      </el-form-item>
      <el-form-item label="签到码" prop="signCode">
        <el-input v-model="searchForm.signCode" style="width: 500px">
          <el-button slot="append" type="primary" icon="el-icon-search" @click="search">查询</el-button>
        </el-input>
      </el-form-item>

      <div>
        <el-form-item label="查询结果">
          <el-table width="100%" border :data="tableData" @row-click="changeSelect" highlight-current-row size="mini"
            empty-text="暂无数据，点击上方搜索按钮查询数据">
            <el-table-column align="center" width="30">
              <template slot-scope="scope">
                <!-- 可以手动的修改label的值，从而控制选择哪一项 -->
                <el-radio class="radio" v-model="templateSelection" :label="scope.row.id"
                  :disabled="scope.row.signStatus == '已签到' ? true : false">&nbsp;</el-radio>
              </template>
            </el-table-column>
            <el-table-column width="90" prop="name" label="姓名" :show-overflow-tooltip="true" :resizable="false">
            </el-table-column>

            <el-table-column :resizable="false" :show-overflow-tooltip="true" prop="phone" label="手机号">
            </el-table-column>
            <el-table-column width="80" :resizable="false" :show-overflow-tooltip="true" prop="email" label="邮箱">
            </el-table-column>
            <el-table-column width="90" prop="ticketName" label="票种信息" :show-overflow-tooltip="true" :resizable="false">
            </el-table-column>
            <el-table-column width="80" :resizable="false" :show-overflow-tooltip="true" prop="signCode" label="签到码">
            </el-table-column>

            <el-table-column :resizable="false" :show-overflow-tooltip="true" prop="company" label="单位">
            </el-table-column>
            <el-table-column :resizable="false" :show-overflow-tooltip="true" prop="idCard" label="身份证号">
            </el-table-column>
            <el-table-column width="80" :resizable="false" :show-overflow-tooltip="true" prop="signStatus" label="签到状态">
            </el-table-column>
          </el-table>
          <pagination v-show="total > 0" :total="total" :page.sync="searchForm.pageNum"
            :limit.sync="searchForm.pageSize" @pagination="search" />
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="region">
        <el-input v-model="remark" style="width: 500px" type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"></el-input>
      </el-form-item>
      <el-form-item style="text-align: center">
        <el-button class="sign-button" type="warning" @click="submitForm('searchForm')"
          :loading="btnloading">立即签到</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { signActive, queryHasSignedList } from "@/api/sign/signPage";

import { textPrintData } from "@/utils/print/printData/Text";

const defaultSearchForm = {
  phone: "",
  name: "",
  signCode: "",

  pageNum: 1,
  pageSize: 10,
};
export default {
  data() {
    return {
      searchForm: {
        ...defaultSearchForm,
      },
      searchForm: { ...defaultSearchForm },
      rules: {},
      total: 0,
      searchInfo: {},
      checkList: [],
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      tableData: [],
      templateSelection: null,
      remark: "",
      activityId: "",
      btnloading: false,
    };
  },
  props: {
    nMPrintSocket: {
      type: Object,
      default: null
    },
    printSocketOpen: {
      type: Boolean,
      default: false
    },
    jsonObj: {
      type: Object,
      default: null
    },
    batchPrintJob: {
      type: Function,
      default: () => { }
    }
  },
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activityId = activeInfo.activityId;
  },
  methods: {
    changeSelect(row) {
      if (row.signStatus == "待签到") {
        this.templateSelection = row.id;
        this.checkList = this.tableData.filter((item) => item.id === row.id);
      }
    },
    async search() {
      this.searchForm.activityId = this.activityId;
      if (
        this.searchForm.name ||
        this.searchForm.phone ||
        this.searchForm.signCode
      ) {
        this.searchForm.activityId = this.activityId;
        let res = await queryHasSignedList(this.searchForm);
        if (res.rows && res.rows.length > 0) {
          this.tableData = res.rows;
          this.total = res.total;
        } else {
          this.$message.error("暂未查询到相关人员");
          this.total = 0;
          this.tableData = [];
        }
      } else {
        this.total = 0;
        this.tableData = [];
      }
    },
    async submitForm(formName) {
      if (this.templateSelection) {
        this.btnloading = true
        let res = await signActive({
          regId: this.templateSelection,
          remark: this.remark,
        });
        if (res.code === 200) {
          // this.searchForm = { ...defaultSearchForm };
          this.printMessage({ name: this?.tableData?.[0]?.name ?? "", company: this?.tableData?.[0]?.company ?? "" })
          this.remark = "";
          this.search();
          this.templateSelection = null;
          this.$message.success(res.msg);
          this.btnloading = false
        } else {
          this.templateSelection = null;
          this.$message.error(res.msg);
          this.btnloading = false
        }
      } else {
        this.templateSelection = null;
        this.$message.error("您还未选择签到的人员信息，请选择后再进行操作");
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    printMessage(data) {
      if (!this.$props?.printSocketOpen) return console.log("打印服务未开启");
      let contentArr = [];
      contentArr.push(textPrintData(data));
      this.$props?.batchPrintJob(contentArr);
    },
  },

};
</script>
<style lang="scss">
.cus-sign {
  // display: flex;
  // align-items: center;
  // justify-content: center;
  width: 1000px;
  margin: 0 auto;

  .sign-button {
    border-radius: 10px;
    font-size: 15px;
    font-weight: bold;
    background: #fa9a2c;
    box-shadow: 0px 6px 10px 0px #fff1a5;
    box-shadow: 0px 6px 6px 0px #f9ffa5 inset;
    box-shadow: 0px -8px 8px 0px #fa772c inset;
  }
}
</style>
