import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'
const AccesToken="Acces-Token"
export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON>ey, token)
}
export function removeToken() {
  return Cookies.remove(TokenKey)
}
export function getAccesToken(){
  return Cookies.get(AccesToken)
}
export function setAccesToken(token) {
  return Cookies.set(AccesToken, token)
}
export function removeAccesToken() {
  return Cookies.remove(AccesToken)
}
