var offsetX = 0;
var offsetY = 0;
var width = 80;
var height = 40;
var rotate = 90;
var marginX = 10.0;
var marginY = 8.0;
var sheetHeight = (height - marginY * 2) / 2.0;
var titleWidth = width - marginX * 2;
var fontSize = 3.2;

//文本打印数据
export const textPrintData = (data) => {

  return {
    InitDrawingBoardParam: {
      width: width,
      height: height,
      rotate: rotate,
      path: "ZT001.ttf",
      verticalShift: 0,
      HorizontalShift: 0,
    },

    elements: [
      {
        type: "text",
        json: {
          "x": marginX + offsetX,
          "y": marginY + offsetY,
          "height": sheetHeight,
          "width": titleWidth,
          "value": `${data?.name}`,
          "fontFamily": "宋体",
          "rotate": 0,
          "fontSize": fontSize * 2,
          "textAlignHorizonral": 0,
          "textAlignVertical": 1,
          "letterSpacing": 0.0,
          "lineSpacing": 1.0,
          "lineMode": 6,
          "fontStyle": [true, false, false, false]
        },
      },
      {
        type: "text",
        json: {
          "x": marginX + offsetX,
          "y": marginY + sheetHeight * 1 + offsetY,
          "height": sheetHeight,
          "width": titleWidth,
          "value": `${data?.company}`,
          "fontFamily": "宋体",
          "rotate": 0,
          "fontSize": fontSize * 1.5,
          "textAlignHorizonral": 0,
          "textAlignVertical": 1,
          "letterSpacing": 0.0,
          "lineSpacing": 1.0,
          "lineMode": 6,
          "fontStyle": [false, false, false, false]
        },
      },

    ],
  };
}
