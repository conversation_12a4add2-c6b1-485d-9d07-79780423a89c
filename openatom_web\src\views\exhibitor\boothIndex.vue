<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="展位名称" prop="positionName">
            <el-input
              v-model="queryParams.positionName"
              placeholder="请输入用户名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="展位状态"
              clearable
              style="width: 240px"
            >
              <el-option :label="'已分配'" :value="1"></el-option>
              <el-option :label="'未分配'" :value="0"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              >新增</el-button
            >
          </el-form-item>
        </el-form>

        <el-table
          v-loading="loading"
          :data="
            boothList.slice(
              (currentPage - 1) * pageSize,
              currentPage * pageSize
            )
          "
        >
          <el-table-column
            label="序号"
            align="center"
            key="index"
            prop="index"
            type="index"
          />
          <el-table-column
            label="展位名称"
            align="center"
            key="positionName"
            prop="positionName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="展位编号"
            align="center"
            key="positionNumber"
            prop="positionNumber"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="面积(平方米)"
            align="center"
            key="positionAreaSize"
            prop="positionAreaSize"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="分配状态"
            align="center"
            key="status"
            prop="status"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.status == 1 ? "已分配" : "未分配" }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            v-if="columns[6].visible"
            width="160"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column> -->
          <el-table-column
            label="操作"
            align="center"
            width="160"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope" v-if="scope.row.status == 0">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          style="margin-top: 10px"
          align="right"
          v-show="boothList.length > 0"
          :current-page.sync="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="10"
          layout="total, sizes, prev, pager,next"
          :total="boothList.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-col>
    </el-row>
    <el-dialog
      :title="form.id != '' && form.id ? '修改' : '新建'"
      :visible.sync="open"
      width="600px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="展位名称" prop="positionName">
          <el-input v-model="form.positionName" placeholder="请输入展位名称" />
        </el-form-item>
        <el-form-item label="展位编号" prop="positionNumber">
          <el-input
            v-model="form.positionNumber"
            placeholder="请输入展位编号"
          />
        </el-form-item>
        <el-form-item label="展位面积" prop="positionAreaSize">
          <el-input
            v-model="form.positionAreaSize"
            placeholder="请输入展位面积"
          >
            <template slot="append">平方米</template>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import request from "@/utils/request";
import { getToken } from "@/utils/auth";
import { checkPrice } from "@/utils/validate";
export default {
  name: "boothIndex",
  data() {
    return {
      queryParams: {
        // pageNum: 1,
        // pageSize: 10,
        activityId: JSON.parse(sessionStorage.getItem("activity")).activityId,
      },
      loading: false,
      total: 0,
      boothList: [],
      open: false,
      form: {
        positionName: undefined,
        positionNumber: undefined,
        positionAreaSize: undefined,
      },
      currentPage: 1,
      pageSize: 10,
      rules: {
        positionName: [
          { required: true, message: "展位名称不能为空", trigger: "blur" },
        ],
        positionNumber: [
          { required: true, message: "展位编号不能为空", trigger: "blur" },
        ],
        positionAreaSize: [
          { required: true, message: "展位面积不能为空", trigger: "blur" },
          { validator: checkPrice, trigger: "blur" },
        ],
      },
    };
  },
  created() {},
  mounted() {
    this.getList();
  },
  methods: {
    handleQuery() {
      this.currentPage = 1;
      this.getList();
    },
    // 当当前页改变
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    submitForm() {
      let url = "";
      if (this.form.id && this.form.id != "") {
        url = "/display/position/manage/update";
      } else {
        url = "/display/position/manage/add";
      }
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.activityId = this.queryParams.activityId;
          request({
            url: url,
            method: "post",
            data: this.form,
          })
            .then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("操作成功");
                this.open = false;
                this.cancel();
                this.getList();
                this.cancel();
              }
            })
            .catch((err) => {
              console.log(err);
            });
        }
      });
    },
    cancel() {
      this.open = false;
      this.form = {
        positionName: undefined,
        positionNumber: undefined,
        positionAreaSize: undefined,
      };
    },
    handleAdd() {
      this.open = true;
    },
    handleDelete(row) {
      this.$modal
        .confirm('是否删除展位为"' + row.positionName + '"的数据项？')
        .then(() => {
          request({
            url:
              "/display/position/manage/del/" +
              row.id +
              "/" +
              row.positionNumber,
            method: "delete",
          })
            .then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("操作成功");
                this.getList();
              } else {
                this.$message.error("操作失败");
              }
            })
            .catch((err) => {
              console.log(err);
            });
        })
        .catch(() => {});
    },
    handleUpdate(row) {
      let data = { ...row };
      this.open = true;
      this.form = data;
    },
    getList() {
      this.loading = true;
      request({
        url: "/display/position/manage/list",
        method: "get",
        params: this.queryParams,
      })
        .then((response) => {
          this.loading = false;
          this.boothList = response.data;
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss">
</style>
  