<template>
  <div class="registEnter">
    <!-- 支付宝 -->
    <div id="alipay"><iframe :srcdoc="alipayFormData" :height="maxHeight" style=" width:100%;border: none"></iframe></div>
  </div>
</template>

<script>
export default {
  name: 'OrderPay',
  props: {
  },
  data(){
    return {
      alipayFormData:this.$route.query ? this.$route.query.alipayFormData : "",
      maxHeight:window.innerHeight-200+'px'
    }
  },
  destroyed(){
  },
  mounted(){
    
  },
  methods:{
  },
   
  
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>

body{
  padding-right: 0px !important;
  overflow: hidden;
}
.el-form-item__label:before{
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}
.el-form-item__label{
  margin-bottom: 5px !important;
}

h3 {
  margin: 40px 0 0;
}
a {
  color: #42b983;
}
.registEnter{
  padding-bottom: 20px;
  /* margin: 0px 4%;
  width: 90%; */
  text-align: center;
}
.el-cascader{
  width: 100%;
}
.el-date-editor{
  width: 100% !important;
}
.el-select--smal{
  width: 100% !important;
}
.el-form-item__content,.el-radio-group{
  text-align: left !important;
}
.el-radio{
  margin-right: 10px !important;
}
.el-select, .el-select--small{
  width: 100% !important;

}
.el-form-item{
  margin-bottom: 15px !important;
}
.formEdite{
  text-align: left;
  margin: 10px 5% 0px 5%;
  padding: 20px 5%;
  width: 90%;
  background-color: #ffffff;
  border-radius: 10px;
}
.moneyShow{
  padding: 0px 0px;
}
.productMsg,.orderDetail{
  background:#ffffff;
  margin:5px;
  border-radius:5px
}
.productIcon{
  float:left;
  padding-left:10px;
  
}
.productAttribute{
  float:right;
  margin:auto;
  text-align:left;
  padding:5px;
}
.payMoneyMsg{
  padding:10px;
  margin:auto;
  text-align:right;
  font-size:16px;
}
.countDownTime{
  padding:10px;
  margin:auto;
  text-align:right;
  font-size:12px;
}



</style>
