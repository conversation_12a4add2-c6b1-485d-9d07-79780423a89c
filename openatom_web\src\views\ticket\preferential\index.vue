<template>
  <div class="app-container">
    <el-form ref="form" :inline="true" :model="form">
      <el-form-item label="优惠码/票种/所属单位:">
        <el-input
          size="small"
          v-model.trim="form.searchParam"
          clearable
          placeholder="请输入优惠码/票种/所属单位（团体）"
        />
      </el-form-item>
      <el-form-item style="margin-left: 10px">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-search"
          @click="search(1)"
          >查询</el-button
        >
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleDownTemp"
          v-if="activetyInfo.isBuyTicket == 1"
          >导出</el-button
        >
        <!-- <el-button size="mini" icon="el-icon-refresh" @click="resetSearch">重置</el-button> -->
        <el-button
          size="mini"
          type="warning"
          icon="el-icon-plus"
          @click="addpromotionCode"
          v-if="activetyInfo.isBuyTicket == 1"
          >生成优惠码</el-button
        >
      </el-form-item>
    </el-form>
    <!-- <div class="tableBtnGroup">
     
    </div> -->
    <el-table
      ref="preferentialTable"
      :data="tableData"
      :height="tableHight"
      style="width: 100%"
    >
      <el-table-column
        :resizable="false"
        align="center"
        label="序号"
        type="index"
        width="50"
      />
      <el-table-column
        :resizable="false"
        align="center"
        label="活动名称"
        :show-overflow-tooltip="true"
      >
        {{ this.activityName }}
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="couponCode"
        label="优惠码"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="name"
        label="票种"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="couponPrice"
        label="优惠价格（元）"
      >
        <template v-slot="scope">
          <span>
            {{
              scope.row.couponPrice === null ? "免费" : scope.row.couponPrice
            }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="usedCount"
        label="已用张数"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="totalCount"
        label="总张数"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        width="300"
        prop="company"
        label="所属单位（团体）"
      />
      <el-table-column
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="isSale"
        label="是否有效"
      >
        <template v-slot="scope">
          <span> {{ scope.row.isValid === 2 ? "否" : "是" }}</span>
        </template>
      </el-table-column>
      <el-table-column :resizable="false" width="160" label="操作">
        <template v-slot="scope">
          <el-button
            :disabled="scope.row.usedCount > 0"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDeletePreferential(scope.row.id)"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdatePreferential(scope.row)"
            >修改</el-button
          >
          <el-button
            v-if="scope.row.isValid === 1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDisable(scope.row)"
            >失效</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEnable(scope.row)"
            >启用</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="tableData && tableData.length > 0"
      :total="total"
      :page.sync="pageData.pageNum"
      :limit.sync="pageData.pageSize"
      @pagination="handleCurrentChange"
    />
    <addPreferentialCode
      ref="dialogForm"
      :showDialog="showDialog"
      :dialogTitle="dialogTitle"
      :rowData="rowData"
      @cancelDialogFun="cancelDialogFun"
    />
  </div>
</template>
<script>
import preferential from "@/api/ticket/preferential/index";
import addPreferentialCode from "@/views/ticket/preferential/addPreferentialCode";
import coupon from "@/api/ticket/preferential";
export default {
  name: "Preferential",
  components: {
    addPreferentialCode,
  },
  data() {
    return {
      activetyInfo: JSON.parse(sessionStorage.getItem("activity")),
      activityId: JSON.parse(sessionStorage.getItem("activity")).activityId,
      form: {
        searchParam: "",
      },
      tableData: [],
      rowData: {},
      dialogTitle: "生成优惠码",
      tableHight: "534px",
      showDialog: false,
      pageData: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      total: 0,
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
    };
  },
  mounted() {
    this.search(1);
  },
  methods: {
    handleDownTemp() {
      this.download(
        "/coupon/export",
        {
          activityId: this.activityId,
        },
        `优惠卷数据_${new Date().getTime()}.xlsx`
      );
    },
    cancelDialogFun(data) {
      this.showDialog = data;
    },
    addpromotionCode() {
      this.showDialog = true;
      this.$nextTick(() => {
        this.rowData = {
          id: "",
          couponCode: "",
          ticketId: "",
          couponPrice: null,
          totalCount: null,
          company: "",
          name: "",
          usedCount: 0,
        };
      });
      this.dialogTitle = "生成优惠码";
      this.getCouponCode();
    },
    // 获取优惠码
    getCouponCode() {
      coupon.getGenerateCode().then((res) => {
        if (res.code === 200) {
          this.rowData.couponCode = res.msg;
        }
      });
    },
    init(val) {
      const formData = new FormData();
      formData.append("pageNum", val);
      formData.append("pageSize", this.pageData.pageSize);
      formData.append("searchParam", this.form.searchParam);
      formData.append("activityId", this.activityId);
      preferential.getPreferentialList(formData).then((res) => {
        if (res.code === 200) {
          this.tableData = res.rows;
          this.total = res.total;
        }
      });
    },
    search(val) {
      this.pageData.pageNum = val;
      this.init(val);
    },
    resetSearch() {
      this.form = {
        searchParam: "",
      };
      this.pageData.pageNum = 1;
      this.init(1);
    },
    handleDeletePreferential(data) {
      this.$confirm(`确定删除活动编码为${this.activityId}的数据`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(() => {
          preferential.deleteGenerateCode(data).then((res) => {
            if (res.code === 200) {
              setTimeout(() => {
                this.$message({
                  type: "success",
                  message: "删除成功",
                });
                this.resetSearch();
              }, 500);
            }
          });
        })
        .catch(() => {
          return false;
        });
    },
    handleUpdatePreferential(data) {
      this.showDialog = true;
      this.dialogTitle = "修改";
      this.rowData = {
        id: data.id,
        couponCode: data.couponCode,
        ticketId: data.ticketId,
        price: data.price,
        couponPrice: data.couponPrice,
        totalCount: data.totalCount,
        company: data.company,
        name: data.name + "(" + data.price + ")",
        usedCount: data.usedCount,
      };
    },
    handleDisable(data) {
      this.$confirm(`确定失效活动编码为${this.activityId}的数据`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(() => {
          let obj = {
            id: data.id,
            isValid: 2,
          };
          preferential.updateGenerateCode(obj).then((res) => {
            if (res.code === 200) {
              this.init();
              this.$message({
                type: "success",
                message: "设置失效成功",
              });
              this.resetSearch();
            }
          });
        })
        .catch(() => {
          return false;
        });
    },
    handleEnable(data) {
      this.$confirm(`确定启用活动编码为${this.activityId}的数据`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(() => {
          let obj = {
            id: data.id,
            isValid: 1,
          };
          preferential.updateGenerateCode(obj).then((res) => {
            if (res.code === 200) {
              this.init();
              this.$message({
                type: "success",
                message: "设置失效成功",
              });
              this.resetSearch();
            }
          });
        })
        .catch(() => {
          return false;
        });
    },
    handleCurrentChange(val) {
      this.search(val.page);
    },
  },
};
</script>

<style scoped lang="scss">
.tableBtnGroup {
  margin-bottom: 20px;
}
</style>

