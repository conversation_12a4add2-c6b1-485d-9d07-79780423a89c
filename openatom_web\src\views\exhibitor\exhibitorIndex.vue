<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="待审批" name="0">
        <pendyApproval v-if="activeName==0"></pendyApproval>
      </el-tab-pane>
      <el-tab-pane label="已审批" name="1">
        <approved v-if="activeName==1"></approved>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import pendyApproval from "./pendyApproval.vue";
import approved from "./approved.vue";
export default {
  components: { pendyApproval, approved },
  data() {
    return {
      activeName: "0",
    };
  },
  methods: {
    handleClick(tab, event) {
      this.activeName = tab.index;
    },
  },
};
</script>
