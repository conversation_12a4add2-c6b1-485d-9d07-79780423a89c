<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
      v-if="activeName == 0"
    >
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输人员姓名"
          clearable
        />
      </el-form-item>

      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输人员手机号"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['activity:flow:update']"
          :disabled="selected.length > 0 ? false : true"
          >批量审批</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      :data="approvalList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column
        :resizable="false"
        align="center"
        label="活动名称"
        :show-overflow-tooltip="true"
      >
        {{ queryParams.active_Name }}
      </el-table-column>
      <el-table-column
        label="票种"
        align="center"
        prop="ticketName"
        width="150px"
        :show-overflow-tooltip="true"
      />
      <template v-for="item in formRegular">
        <el-table-column
          v-if="item.open"
          :resizable="true"
          :key="item.defaultName"
          :label="item.title"
          :prop="item.defaultName"
          :show-overflow-tooltip="true"
        ></el-table-column>
      </template>
      <el-table-column
        label="审批状态"
        align="center"
        prop="approveStatus"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="100"
      >
        <!-- v-hasPermi="['system:post:edit']" -->
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['activity:flow:update']"
            >审批</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page-sizes="pageSizes"
      :page-size="10"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      custom-class="selfDialog"
      :title="'审批'"
      :visible.sync="UserOpen"
      width="800px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-collapse v-model="activepanel">
        <el-collapse-item title="报名人信息" name="useInfo">
          <div id="self" style="height: 300px; overflow: auto">
            <el-form
              ref="flowform"
              :model="flowform"
              label-width="100px"
              :inline="false"
              class="selfForm"
              :label-position="'top'"
            >
              <el-form-item label="票种" prop="ticketName">
                <el-input :disabled="true" v-model="flowform.ticketName" />
              </el-form-item>
              <el-form-item
                label="人员类型"
                prop="userTypeName"
                v-if="formRegular[0].open"
              >
                <el-input :disabled="true" v-model="flowform.userTypeName" />
              </el-form-item>
              <el-form-item label="姓名" prop="name" v-if="formRegular[1].open">
                <el-input v-model="flowform.name" :disabled="true" />
              </el-form-item>
              <el-form-item label="性别" prop="sex" v-if="formRegular[4].open">
                <el-input v-model="flowform.sex" :disabled="true" />
              </el-form-item>
              <el-form-item
                label="国籍"
                prop="nationality"
                v-if="formRegular[5].open"
              >
                <el-input v-model="flowform.nationality" :disabled="true" />
              </el-form-item>
              <el-form-item
                label="省市"
                prop="nationalityCity"
                v-if="formRegular[6].open"
              >
                <el-input v-model="flowform.nationalityCity" :disabled="true" />
              </el-form-item>
              <el-form-item
                label="手机号"
                prop="phone"
                v-if="formRegular[2].open"
              >
                <el-input v-model="flowform.phone" :disabled="true" />
              </el-form-item>
              <el-form-item
                label="邮箱"
                prop="email"
                v-if="formRegular[3].open"
              >
                <el-input v-model="flowform.email" :disabled="true" />
              </el-form-item>
              <el-form-item
                label="证件类型"
                prop="cardTypeName"
                v-if="formRegular[8].open"
              >
                <el-input v-model="flowform.cardTypeName" :disabled="true" />
              </el-form-item>
              <el-form-item
                label="证件号"
                prop="idCard"
                v-if="formRegular[8].open"
              >
                <el-input v-model="flowform.idCard" :disabled="true" />
              </el-form-item>
              <el-form-item
                label="工作单位"
                prop="company"
                v-if="formRegular[10].open"
              >
                <el-input v-model="flowform.company" :disabled="true" />
              </el-form-item>
              <el-form-item label="职务" prop="job" v-if="formRegular[11].open">
                <el-input v-model="flowform.job" :disabled="true" />
              </el-form-item>
            </el-form>
            <form-create
              :rule="formCreateRule"
              :value.sync="formValue"
              :option="option"
            ></form-create>
          </div>
        </el-collapse-item>
        <el-collapse-item title="审批流程" name="approval">
          <el-form
            ref="appform"
            :model="appform"
            :rules="apprule"
            label-width="100px"
          >
            <el-form-item label="审批意见" prop="remarks">
              <el-input type="textarea" v-model="appform.remarks" />
            </el-form-item>
            <el-form-item label="审批结果" prop="action">
              <el-select v-model="appform.action">
                <el-option value="同意">同意</el-option>
                <el-option value="驳回">驳回</el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <el-table v-loading="apploading" :data="waitList">
            <el-table-column
              label="序号"
              align="center"
              prop="key"
              :show-overflow-tooltip="true"
              width="100px"
            />

            <el-table-column
              label="审批人"
              align="center"
              prop="oper_user"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="操作"
              align="center"
              prop="oper_info"
              :show-overflow-tooltip="true"
            >
            </el-table-column>

            <el-table-column
              label="说明"
              align="center"
              prop="remarks"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              label="时间"
              align="center"
              prop="oper_date"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="SubForm" :loading="btnloading"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      custom-class="selfDialog"
      :title="'批量审批'"
      :visible.sync="batchOpen"
      width="800px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-form
        ref="batchform"
        :model="batchform"
        :rules="batchrule"
        label-width="100px"
      >
        <el-form-item label="审批意见" prop="remarks">
          <el-input type="textarea" v-model="batchform.remarks" />
        </el-form-item>
        <el-form-item label="审批结果" prop="action">
          <el-select v-model="batchform.action">
            <el-option value="同意">同意</el-option>
            <!-- <el-option value="驳回">驳回</el-option> -->
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="batchSubForm" :loading="btnloading"
          >确 定</el-button
        >
        <el-button @click="batchCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
import formCreate from "@form-create/iview";
export default {
  name: "wait",
  data() {
    return {
      btnloading: false,
      activeName: "0",
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: undefined,
        valid: 1,
        dealIdCard: true,
        needApp: "true",
      },
      activeOption: [],
      userOption: [],
      approvalList: [],
      loading: false,
      total: 0,
      pageSizes: [10, 20, 50, 100],
      UserOpen: false,
      activepanel: "approval",
      formParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: undefined,
      },
      formCreateRule: [],
      formValue: {},
      flowform: {},
      option: {
        form: { labelPosition: "top" },
        submitBtn: { show: false },
      },
      appform: {},
      apprule: {
        action: [
          { required: true, message: "请选择审批结果", trigger: "blur" },
        ],
        remarks: [
          { required: false, message: "请填写审批意见", trigger: "blur" },
        ],
      },
      waitList: [],
      apploading: false,
      selected: [],
      batchOpen: false,
      batchform: {},
      batchrule: {
        action: [
          { required: true, message: "请选择审批结果", trigger: "blur" },
        ],
        remarks: [
          { required: false, message: "请填写审批意见", trigger: "blur" },
        ],
      },
      formRegular: [
        {
          open: false,
          required: false,
          value: undefined,
          sort: 1,
          defaultName: "userTypeName",
          title: "用户类型",
        },
        {
          open: true,
          required: true,
          value: undefined,
          sort: 2,
          defaultName: "name",
          title: "姓名",
        },
        {
          open: true,
          required: true,
          value: undefined,
          sort: 3,
          defaultName: "phone",
          title: "手机号码",
        },
        {
          open: true,
          required: true,
          value: undefined,
          sort: 4,
          defaultName: "email",
          title: "邮箱",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 5,
          defaultName: "sex",
          title: "性别",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 6,
          defaultName: "nationality",
          title: "国籍",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 7,
          defaultName: "province",
          title: "省份",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 8,
          defaultName: "city",
          title: "城市",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 9,
          defaultName: "cardTypeName",
          title: "证件类型",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 10,
          defaultName: "idCard",
          title: "证件号码",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 11,
          defaultName: "company",
          title: "工作单位",
        },
        {
          open: false,
          required: false,
          value: undefined,
          sort: 12,
          defaultName: "job",
          title: "职务",
        },
      ],
    };
  },
  watch: {
    "queryParams.activityId": {
      handler(val, oldval) {
        if (val != oldval) {
          this.getList();
          this.queryParams.activityId = val;
          this.getFromList();
        }
      },
      immediate: true,
    },
  },
  beforeCreate() {},
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.queryParams.activityId = activeInfo.activityId;
    this.queryParams.active_Name = activeInfo.activityName;
    //
  },
  mounted() {
    this.getuserType();
    this.getActiveList();
  },
  update() {
    //this.getActiveList();
  },
  methods: {
    getActiveList() {
      request({
        url: "/activityManage/list",
        method: "post",
        data: { pageNum: 1, pageSize: 10000 },
      })
        .then((response) => {
          this.activeOption = response.rows;
          // this.queryParams.activityId = response.rows[0].activityId;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getuserType() {
      request({
        url: "/system/dict/data/list?pageNum=1&pageSize=100&dictType=activity_user_type&status=0",
        method: "get",
      })
        .then((response) => {
          this.userOption = response.rows;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getList() {
      request({
        url: "reg/list",
        method: "get",
        params: this.queryParams,
      })
        .then((response) => {
          this.approvalList = response.rows;
          this.total = response.total;
          this.loading = false;
          this.pageSizes =
            this.total < 100 ? [10, 20, 50, 100] : [10, 50, 100, this.total];
        })
        .catch((err) => {
          console.log(err);
        });
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleUpdate(row) {
      this.UserOpen = true;
      this.getFromList();
      setTimeout(() => {
        this.formValue = row.dynamicsJson ? row.dynamicsJson : {};
        this.flowform = row;
        this.flowform.nationalityCity = row.province
          ? row.province + "/" + row.city
          : "";
        this.appform.activityNo = row.activityId;
        this.getFlowList(row.busPk);
      }, 200);
    },

    SubForm() {
      this.$refs["appform"].validate((valid) => {
        if (valid) {
          this.btnloading = true;
          this.appform.bus_info = this.appform.action;

          request({
            url: "/flow/update",
            method: "post",
            data: this.appform,
          })
            .then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.UserOpen = false;
              this.getList();
              this.btnloading = false;
            })
            .catch((err) => {
              console.log(err);
              this.btnloading = false;
            });
        }
      });
    },
    batchSubForm() {
      this.$refs["batchform"].validate((valid) => {
        if (valid) {
          this.btnloading = true;
          let bus_pk = [];
          this.selected.map((item, index) => {
            bus_pk.push(item.busPk);
            if (index == this.selected.length - 1) {
              this.batchform.bus_info = this.batchform.action;
              this.batchform.buspklist = bus_pk;
              this.batchform.activityNo = this.queryParams.activityId;
              this.batchform.flowno =
                JSON.parse(sessionStorage.getItem("activity")).isBuyTicket == -1
                  ? 1
                  : 2;
              request({
                url: "/flow/batchupdate",
                method: "post",
                data: this.batchform,
              })
                .then((response) => {
                  this.$modal.msgSuccess("操作成功");
                  this.batchOpen = false;
                  this.getList();
                  this.btnloading = false;
                })
                .catch((err) => {
                  console.log(err);
                  this.btnloading = false;
                });
            }
          });
        }
      });
    },
    batchCancel() {
      this.batchOpen = false;
    },
    cancel() {
      this.UserOpen = false;
      this.formValue = {};
      this.flowform = {};
      this.appform = {};
    },
    getFromList() {
      this.formParams.activityId = this.queryParams.activityId;
      request({
        url: "/form/list",
        method: "get",
        params: this.formParams,
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            let data = [...response.rows[0].formInfo];
            if (data && data.length > 0) {
              if (data[1] && data[1].length > 0) {
                data[1].map((item) => {
                  item.$required = false;
                  let obj = {};
                  obj.disabled = true;
                  item.props = obj;
                });
              }
              data[0].map((item) => {
                this.formRegular.map((val) => {
                  if (
                    item.defaultName == "provinceCity" ||
                    item.defaultName == "idCardType" ||
                    item.defaultName == "userType"
                  ) {
                    if (item.defaultName == "provinceCity") {
                      if (
                        val.defaultName == "province" ||
                        val.defaultName == "city"
                      ) {
                        val.open = item.open;
                      }
                    }
                    if (item.defaultName == "idCardType") {
                      if (
                        val.defaultName == "idCard" ||
                        val.defaultName == "cardTypeName"
                      ) {
                        val.open = item.open;
                      }
                    }
                    if (item.defaultName == "userType") {
                      if (val.defaultName == "userTypeName") {
                        val.open = item.open;
                      }
                    }
                  } else {
                    if (item.defaultName == val.defaultName) {
                      val.open = item.open;
                    }
                  }
                });
              });
            }

            this.formCreateRule = data[1];
          } else {
            this.formCreateRule = [];
          }
          //   this.formList = response.rows;
          //   this.total = response.total;
          //   this.tableLoading = false;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getFlowList(data) {
      this.apploading = true;
      request({
        url: "/flow/list?bus_pk=" + data,
        method: "get",
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            response.rows.map((item, index) => {
              item.key = index + 1;
            });
            this.waitList = response.rows;
            this.appform.flowno = response.rows[0].flowno;
            this.appform.bus_pk = data;
          }
          this.apploading = false;
        })
        .catch((err) => {
          this.apploading = false;
          console.log(err);
        });
    },

    handleClick(tab) {
      this.activeName = tab.index;
    },
    handleSelectionChange(selection) {
      this.selected = [...selection];
    },
    handleAdd() {
      this.batchOpen = true;
    },
  },
};
</script>
<style scoped rel="stylesheet/scss" lang="scss">
::v-deep .form-create button {
  display: none;
}
::v-deep #self .el-form-item {
  margin-bottom: 5px;
}
::v-deep .form-create .el-form-item__label {
  // width: 100px !important;
}
::v-deep .form-create .el-form-item__content {
  // margin-left: 100px !important;
}
::v-deep .form-create .el-input {
  // width: 78% !important;
}
::v-deep .form-create .el-select {
  width: 100% !important;
}
::v-deep .el-dialog__body {
  padding: 15px;
  padding-top: 0;
}
.el-tooltip__popper.is-dark {
  min-width: 10px !important;
  max-width: 300px !important;
}
::v-deep .form-create .el-select {
  width: 100%;
}
::v-deep .form-create .el-date-editor {
  width: 100%;
}
</style>