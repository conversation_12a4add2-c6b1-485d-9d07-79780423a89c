{"name": "exhibitormanager", "version": "3.8.6", "description": "报名系统", "author": "软通", "license": "MIT", "scripts": {"dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "dependencies": {"@form-create/element-ui": "^2.5.33", "@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.25.3", "cross-env": "^7.0.3", "element-ui": "2.15.13", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "iview": "^3.5.4", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "jsonlint-mod": "^1.7.6", "mathjs": "^12.2.0", "moment": "^2.29.4", "nprogress": "0.2.0", "qrcodejs2": "^0.0.2", "screenfull": "5.0.2", "sortablejs": "1.10.2", "vue": "2.6.12", "vue-meta": "2.4.0", "vue-qr": "^4.0.9", "vue-router": "3.4.9"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.4.6", "@vue/cli-plugin-eslint": "^4.4.6", "@vue/cli-service": "^4.4.6", "babel-eslint": "10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "^6.0.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12", "webpack-bundle-analyzer": "^4.10.1"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}