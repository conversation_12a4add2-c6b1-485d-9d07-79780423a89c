<template>
  <div id="loader-wrapper">
    <div id="loader"></div>
    <div class="loader-section section-left"></div>
    <div class="loader-section section-right"></div>
    <div class="load_title">正在加载系统资源，请耐心等待</div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import { Notification, MessageBox, Message, Loading } from "element-ui";
export default {
  name: "loginFree",
  data() {
    return {
      loading: false,
    };
  },

  created() {
    this.getAccess_token();
  },
  methods: {
    //获取token
    getAccess_token() {
      let code = window.location.href.split("=")[1];
      this.$store
        .dispatch("Access_token", { code: code })
        .then((response) => {
           this.$router.push({ path: this.redirect || "/" }).catch(() => {});
        })
        .catch(() => {
         
        });
    },
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
</style>
