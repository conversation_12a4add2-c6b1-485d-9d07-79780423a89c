## 开发

```bash
# 安装依赖
node v18.14.0
npm  v9.3.1
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建生产环境
npm run build
```

## 项目主要文件介绍

# 开发环境配置

    header.js
    dev.env.js

# axios 封装

    request.js

# 答卷相关

static-diaowen

# 答卷设计

static-design.html

# 答题

static-answer-p.html（pc）
static-answer-m.html（wap）

# 答卷预览

static-preview.html
