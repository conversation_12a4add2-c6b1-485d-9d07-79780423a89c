<template>
  <div id="apply">
    <NavMenu />
    <div class="apply">
      <div>
        <el-collapse v-model="activeNames" @change="ColapseChange">
          <el-collapse-item title="活动信息" name="0">
            <el-form
              ref=""
              label-width="90px"
              style="
                width: 1000px;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-flow: wrap;
              "
              :inline="true"
              v-if="activeInfo"
            >
              <el-form-item label="活动名称：" prop="" style="width: 330px">
                {{ activeInfo.activityName }}
              </el-form-item>
              <el-form-item label="活动地点：" prop="" style="width: 330px">
                {{ activeInfo.activityPlace }}
              </el-form-item>
              <el-form-item label="活动时间：" prop="" style="width: 330px">
                {{ activeInfo.startDate + "至" + activeInfo.endDate }}
              </el-form-item>
              <el-form-item label="活动状态：" prop="" style="width: 330px">
                {{
                  activeInfo.activityStatus == 2
                    ? "进行中"
                    : activeInfo.activityStatus == 1
                    ? "未开始"
                    : "已结束"
                }}
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="展商申请" name="1">
            <div class="" style="width: 1200px">
              <el-steps
                :active="active"
                :space="400"
                :process-status="'wait'"
                finish-status="success"
                :align-center="true"
              >
                <el-step
                  style="cursor: pointer"
                  title="资质填写"
                  description=""
                  @click.native="goSteps1()"
                >
                </el-step>
                <el-step
                  title="资质审核"
                  style="cursor: pointer"
                  description=""
                  @click.native="goSteps2()"
                >
                </el-step>
                <el-step
                  title="展品填写"
                  style="cursor: pointer"
                  description=""
                  @click.native="goSteps3()"
                >
                  <!-- <template v-slot:description>
                    <Steps3
                      v-if="stepactive == 3"
                      :steps1="steps1"
                      :steps2="steps2"
                      @getData="getData"
                    ></Steps3>
                  </template> -->
                </el-step>
                <el-step
                  title="展品审核"
                  style="cursor: pointer"
                  description=""
                  @click.native="goSteps4()"
                >
                </el-step>
                <el-step
                  title="完成"
                  style="cursor: pointer"
                  description=""
                  @click.native="goSteps5()"
                >
                </el-step>
              </el-steps>
            </div>
            <div
              class=""
              style="width: 1000px; margin: 30px auto 0 auto; min-height: 300px"
            >
              <Steps1
                v-if="stepactive == 1"
                :steps1="steps1"
                @getData="getData"
                :activeInfo="activeInfo"
              ></Steps1>

              <div
                v-if="stepactive == 2"
                class="icon"
                style="margin-top: 150px"
              >
                <el-form
                  label-width="100px"
                  style="width: 500px; margin: 0 auto"
                >
                  <el-form-item
                    :label="
                      steps1.approveStatus == '审批驳回' ? '审核状态：' : ''
                    "
                    prop=""
                    style="font-size: 16px"
                  >
                    <span v-show="steps1.approveStatus == '审批通过'"
                      ><i class="el-icon-circle-check" />资质审核通过</span
                    >
                    <span v-show="steps1.approveStatus == '审批驳回'"
                      >审核未通过
                      <a
                        style="
                          font-size: 14px;
                          padding: 5px 0 0 10px;
                          cursor: pointer;
                        "
                        @click="goSteps1()"
                        >重新提交</a
                      ></span
                    >
                    <span v-show="steps1.approveStatus == '审批中'"
                      >请耐心等待，正在审核中</span
                    >
                  </el-form-item>
                  <el-form-item
                    v-if="steps1.approveStatus == '审批驳回'"
                    :label="
                      steps1.approveStatus == '审批驳回' ? '审核意见：' : ''
                    "
                    prop=""
                  >
                    <div v-html="failReason.replace(/\n/g, '<br>')"></div>
                  </el-form-item>
                </el-form>
              </div>
              <Steps3
                v-if="stepactive == 3"
                :steps1="steps1"
                :steps2="steps2"
                @getData="getData"
                :activeInfo="activeInfo"
              ></Steps3>
              <div
                v-if="stepactive == 4"
                class="icon"
                style="margin-top: 150px"
              >
                <el-form
                  label-width="100px"
                  style="width: 500px; margin: 0 auto"
                >
                  <el-form-item
                    :label="
                      steps2.approveStatus == '审批驳回' ||
                      steps2.approveStatus == '取消资格'
                        ? '审核状态：'
                        : ''
                    "
                    prop=""
                    style="font-size: 16px"
                  >
                    <span v-show="steps2.approveStatus == '审批通过'"
                      ><i class="el-icon-circle-check" />展品审核通过</span
                    >
                    <span v-show="steps2.approveStatus == '审批驳回'"
                      >展品审核未通过
                      <a
                        style="
                          font-size: 14px;
                          padding: 5px 0 0 10px;
                          cursor: pointer;
                        "
                        @click="goSteps3()"
                        >重新提交</a
                      ></span
                    >
                    <span
                      v-show="steps2.approveStatus == '取消资格'"
                      style="rgba(255, 0, 26)"
                      >您已被取消参展资格</span
                    >
                    <span
                      v-show="steps2.approveStatus == '审批中'"
                      style="color: rgba(252, 202, 0)"
                      >请耐心等待，展品正在审核中</span
                    >
                  </el-form-item>
                  <el-form-item
                    v-if="steps2.approveStatus == '审批驳回'"
                    :label="
                      steps2.approveStatus == '审批驳回' ? '审核意见：' : ''
                    "
                    prop=""
                  >
                    <div v-html="failReason.replace(/\n/g, '<br>')"></div>
                  </el-form-item>
                  <el-form-item
                    v-if="steps2.approveStatus == '取消资格'"
                    :label="steps2.approveStatus == '取消资格' ? '原因：' : ''"
                    prop=""
                  >
                    <div v-html="failReason.replace(/\n/g, '<br>')"></div>
                  </el-form-item>
                </el-form>
              </div>
              <div
                v-if="stepactive == 5"
                class="icon"
                style="margin-top: 150px"
              >
                <el-form
                  label-width="100px"
                  style="width: 500px; margin: 0 auto"
                >
                  <el-form-item
                    :label="
                      steps2.approveStatus == '审批驳回' ? '审核状态：' : ''
                    "
                    prop=""
                    style="font-size: 16px"
                  >
                    <span v-show="steps1.approveStatus == '审批通过'"
                      ><i class="el-icon-circle-check" />恭喜您,审核通过

                      <a
                        style="
                          font-size: 14px;
                          padding: 5px 0 0 10px;
                          cursor: pointer;
                        "
                        @click="tuiguang"
                      >
                        获取推广链接</a
                      ></span
                    >
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item
            title="推广引流"
            name="2"
            v-if="
              steps1.approveStatus == '审批通过' &&
              steps2.approveStatus == '审批通过'
            "
          >
            <Table :qrCode="qrCode"></Table>
          </el-collapse-item>
        </el-collapse>
        <el-dialog title="推广链接" :visible.sync="dialogVisible" width="600px">
          <el-form label-width="80px">
            <el-form-item label="推广渠道:">
              <span>{{ qrCode }}</span>
            </el-form-item>
            <el-form-item label="P C报名:">
              <span>{{ qrPCCodeUrl }}</span>
            </el-form-item>
            <el-form-item label="手机报名:">
              <div>
                <div>{{ qrCodeUrl }}</div>
                <vue-qr
                  class="qr-code"
                  :logoSrc="imageUrl"
                  :text="qrCodeUrl"
                  :size="200"
                  :whiteMargin="true"
                  :autoColor="true"
                  :correctLevel="3"
                />
              </div>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="dialogVisible = false"
              >取消</el-button
            >
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import NavMenu from "@/components/NavMenu/index.vue";
import Table from "./components/table.vue";
import Steps1 from "./components/steps_1.vue";
import Steps3 from "./components/steps_3.vue";
import request from "@/utils/request";
import { develop } from "@/utils/request";
import vueQr from "vue-qr";
import { getAccesToken, setAccesToken, removeAccesToken } from "@/utils/auth";
export default {
  name: "applyIndex",
  components: { NavMenu, Table, Steps1, Steps3, vueQr },
  data() {
    return {
      dialogVisible: false,
      activeNames: ["0", "1", "2"],
      active: 1,
      steps1: "",
      isShow: 10,
      steps2: "",
      stepactive: 0,
      interval: null,
      activityNo: null,
      qrCodeUrl: "",
      qrPCCodeUrl: "",
      imageUrl: require("../../assets/yuanzi.png"),
      qrCode: "",
      failReason: "",
      activeInfo: null,
    };
  },
  created() {},
  mounted() {
    if (getAccesToken()) {
      if (
        localStorage.getItem("activityNo") &&
        localStorage.getItem("activityNo") != ""
      ) {
        if (this.interval) {
          clearInterval(this.interval);
          this.interval = null;
        }
        setTimeout(() => {
          this.getActiveInfo();
        }, 500);
      } else {
        this.interval = setInterval(() => {
          this.activityNo = localStorage.getItem("activityNo");
        }, 100);
      }
    }
  },
  destroyed() {
    clearInterval(this.interval);
    this.interval = null;
  },
  methods: {
    getActiveInfo() {
      request({
        url: "/activityManage/getOne",
        data: {
          activityId: localStorage.getItem("activityNo"),
        },
        method: "post",
      })
        .then((res) => {
          if (res.code == 200 && res.data) {
            this.activeInfo = res.data;
            if (res.data.activityStatus == 2 || res.data.activityStatus == 3) {
              if (res.data.haveExhibitors == 1) {
                this.getData();
              } else {
                this.$message.error("该活动不支持展商,无法申请");
                return;
              }
            } else {
              if (res.data.activityStatus == 1) {
                this.$message.error("活动未开始,请稍后申请");
                return;
              }
            }
          } else {
            this.$message.error("获取活动信息异常");
          }
        })
        .catch(() => {});
    },
    ColapseChange() {
      this.activeNames = ["0", "1", "2"];
    },
    getQrCode() {
      request({
        url: "/api/display/channelList",
        data: {
          channelName: "展商-" + this.steps1.companyName,
          activityId: localStorage.getItem("activityNo"),
        },
        method: "post",
      })
        .then((res) => {
          if (res.data && res.data.length > 0) {
            this.qrCode = res.data[0].channelCode;
            let keyWords = develop() == "test" ? "Test" : "";
            this.qrCodeUrl =
              window.location.origin +
              "/registration_mobile" +
              keyWords +
              "/?code=" +
              this.qrCode +
              "&activityNo=" +
              localStorage.getItem("activityNo");
            this.qrPCCodeUrl =
              window.location.origin +
              "/registration" +
              keyWords +
              "/?code=" +
              this.qrCode +
              "&activityNo=" +
              localStorage.getItem("activityNo");
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    tuiguang() {
      this.dialogVisible = true;
    },
    getReason(busPK) {
      request({
        url: "/api/display/approval/opinions?bus_pk=" + busPK,
        method: "get",
      })
        .then((res) => {
          if (res.data && res.data.length > 0) {
            this.failReason = res.data[0].remarks;
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    getData() {
      const data = {
        applyUser: JSON.parse(localStorage.getItem("userInfo")).login,
        activityId: localStorage.getItem("activityNo"),
      };

      request({
        url: "/display/business/list",
        params: data,
        method: "get",
      }).then((res) => {
        if (res.data.length == 0) {
          // if (this.activeInfo.activityStatus == 3) {
          //   this.$message.error("活动已结束,无法申请");
          //   return;
          // } else {
          //   this.active = 1;
          //   this.stepactive = 1;
          // }
          this.active = 1;
          this.stepactive = 1;
        } else {
          if (res.data[0].areaSize) {
            res.data[0].areaSize = Number(res.data[0].areaSize);
          }
          this.steps1 = res.data[0];
          this.active = 2;
          this.stepactive = 2;
          if (this.steps1.approveStatus == "审批驳回") {
            this.getReason(this.steps1.busPk);
          }
        }

        if (res.data[0].approveStatus == "审批通过") {
          const data1 = {
            applyUser: JSON.parse(localStorage.getItem("userInfo")).login,
            activityId: localStorage.getItem("activityNo"),
            businessId: res.data[0].id,
          };
          request({
            url: "/display/product/list",
            params: data1,
            method: "get",
          }).then((res) => {
            if (res.data.length == 0) {
              this.active = 3;
              this.stepactive = 3;
            } else {
              this.steps2 = res.data[0];
              this.active = 4;
              this.stepactive = 4;
              if (
                this.steps2.approveStatus == "审批驳回" ||
                this.steps2.approveStatus == "取消资格"
              ) {
                this.getReason(this.steps2.busPk);
              }
            }
            if (res.data[0].approveStatus == "审批通过") {
              this.active = 5;
              this.stepactive = 5;
              this.getQrCode();
            }
          });
        }
      });
    },
    goSteps1() {
      this.stepactive = 1;
      if (this.steps1.approveStatus == "审批通过") {
        this.active = 1;
      }
    },
    goSteps2() {
      if (this.steps1.approveStatus == "审批通过") {
        this.stepactive = 2;
        this.active = 2;
      } else {
        if (this.active < 2) return;
        this.stepactive = 2;
      }
      //  if (this.active < 2) return;
      //   this.stepactive = 2;
    },
    goSteps3() {
      if (this.steps1.approveStatus == "审批通过") {
        this.stepactive = 3;
        this.active = 3;
      } else {
        if (this.active < 3) return;
        this.stepactive = 3;
      }
      //  if (this.active < 3) return;
      //   this.stepactive = 3;
    },
    goSteps4() {
      if (
        this.steps2.approveStatus ||
        this.steps1.approveStatus == "审批通过"
      ) {
        this.stepactive = 4;
        this.active = 4;
      } else {
        if (this.active < 4) return;
        this.stepactive = 4;
      }
      //  if (this.active < 4) return;
      //   this.stepactive = 4;
    },
    goSteps5() {
      if (
        this.steps2.approveStatus == "审批通过" &&
        this.steps1.approveStatus == "审批通过"
      ) {
        this.stepactive = 5;
        this.active = 5;
      } else {
        if (this.active < 5) return;
        this.stepactive = 5;
      }
      // if (this.active < 4) return;
      // this.stepactive = 5;
      // if (this.active < 5) return;
      // this.stepactive = 5;
      // this.active = 5;
    },
  },
};
</script>

<style lang="scss" scoped>
#apply {
  .apply {
    // background-color: #f8f9fa;
    width: 100%;
    height: 100%;

    > div {
      // padding: 0 30% 0 104px;
      // margin-top: 40px;
      margin: 0 auto;
      width: 1200px;
      ::v-deep .el-collapse {
        border-top: none;
        border-bottom: none;

        .el-collapse-item__header {
          font-size: 22px;
          border-bottom: none;
        }

        .el-collapse-item__wrap {
          // border-bottom: none;
        }

        .el-collapse-item__content {
          // display: flex;

          .left {
            width: 200px;
            margin-left: 50px;
          }

          .right {
            width: 100%;
            margin-left: 150px;

            .icon {
              width: 100%;
              // margin:10% 45%  0 45%;
              margin-top: 30%;
              margin-left: 30%;

              span {
                font-size: 24px;
                vertical-align: middle;
                display: flex;

                i {
                  font-size: 38px;
                  margin-right: 10px;
                }
              }

              span:nth-of-type(1) {
                color: rgba(104, 193, 98);

                i {
                  color: rgba(104, 193, 98);
                }
              }

              span:nth-of-type(2) {
                color: rgba(255, 0, 26);

                i {
                  color: rgba(255, 0, 26);
                }
              }

              span:nth-of-type(3) {
                color: rgba(252, 202, 0);

                i {
                  color: rgba(252, 202, 0);
                }
              }
            }
          }
        }

        .el-step__head.is-success {
          border-color: #409eff;
          color: #409eff;
        }

        .el-step__title.is-success {
          color: #409eff;
        }
        // .el-step__description {
        //   width: 1000px;
        //   padding-left: 100px;
        //   margin-top: 30px;
        //   margin-bottom: 30px;
        // }
        .el-step__description.is-success {
          // color: #0000009f;
        }
        .icon {
          width: 100%;
          // margin:10% 45%  0 45%;
          // margin-top: 30%;
          // margin-left: 30%;

          span {
            font-size: 24px;
            vertical-align: middle;
            display: flex;

            i {
              font-size: 38px;
              margin-right: 10px;
            }
          }

          span:nth-of-type(1) {
            color: rgba(104, 193, 98);

            i {
              color: rgba(104, 193, 98);
            }
          }

          span:nth-of-type(2) {
            color: rgba(255, 0, 26);

            i {
              color: rgba(255, 0, 26);
            }
          }

          span:nth-of-type(3) {
            color: rgba(252, 202, 0);

            i {
              color: rgba(252, 202, 0);
            }
          }
        }
      }
    }
  }
}
</style>
