<template>
  <div id="table">
    <el-table
      :data="
        tableData.slice(
          (queryParams.pageNum - 1) * queryParams.pageSize,
          queryParams.pageNum * queryParams.pageSize
        )
      "
      stripe
      style="width: 100%"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
    >
      <el-table-column prop="name" label="姓名" show-overflow-tooltip />
      <el-table-column prop="phone" label="手机号码" show-overflow-tooltip />
      <el-table-column prop="email" label="邮箱 " show-overflow-tooltip />
      <el-table-column prop="ticketName" label="票种" show-overflow-tooltip />
      <el-table-column prop="company" label="工作单位" show-overflow-tooltip />
      <el-table-column prop="job" label="职务" show-overflow-tooltip />
    </el-table>
     <el-pagination
      style="margin-top: 10px"
      align="right"
      v-show="tableData.length > 0"
      :total="tableData.length"
      :current-page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="10"
      layout="total, sizes, prev, pager,next"
      @size-change="handleSizeChangeSys"
      @current-change="handleCurrentChangeSys"
    />
  </div>
</template>
  
<script>
import request from "@/utils/request";
export default {
  name: "table",
  props: {
    qrCode: {
      type: String,
    },
  },
  watch: {
    qrCode: {
      handler(val, oldvalue) {
        if (val!== oldvalue) {
          this.getData();
        }
      },
    },
  },
  data() {
    return {
      tableData: [],
      queryParams:{
          pageNum:1,
          pageSize:10
      }
    };
  },

  mounted() {
    if (this.qrCode && this.qrCode != "") {
      this.getData();
    }
  },
  methods: {
      handleCurrentChangeSys(val) {
      this.queryParams.pageNum = val;
    },
    // 当当前页改变
    handleSizeChangeSys(val) {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = val;
    },
    getData() {
      request({
        url: "/api/display/reg/list?channelCode=" + this.qrCode,
        method: "get",
      })
        .then((res) => {
          if (res.data && res.data.length > 0) {
            this.tableData = res.data;
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
  },
};
</script>
  
<style lang="scss" scoped>
#table {
  padding-left: 120px;
}
</style>
  