<template>
  <div class="app-container" style="">
    <!-- <div style="position: absolute; right: 100px;top:-10px">
      <el-button type="primary" @click="callbackHome">返回</el-button>
    </div> -->

    <div
      v-if="
        !editOpen && !activeOpen && !signOpen && !channelOpen && !messageOpen
      "
    >
      <div class="activityMainMsg">
        <div style="float: left; color: #606266; font-weight: 700">
          <span> 活动名称：{{ this.activityMsg.activityName }}<br /> </span>
          <span>
            活动周期：{{
              this.activityMsg.startDate + " ~ " + this.activityMsg.endDate
            }}
          </span>
          <span style="padding-left: 50px">
            活动地点：{{ this.activityMsg.activityPlace }}
          </span>
          <span style="padding-left: 50px">
            是否自动审批：{{
              this.activityMsg.isAutoApproval == 1 ? "是" : "否"
            }}
          </span>
            <!-- <span style="padding-left: 50px">
            是否售票：{{
              this.activityMsg.isBuyTicket == 1 ? "是" : "否"
            }}
          </span> -->
          <span style="padding-left: 50px">
            活动状态：{{
              this.activityMsg.activityStatus == 1
                ? "未发布"
                : this.activityMsg.activityStatus == 2
                ? "已发布"
                : "已结束"
            }}
          </span>
        </div>
        <div style="float: right; margin-top: 15px">
          <el-button
            type="warning"
            icon="el-icon-plus"
            @click="pushActive(2)"
            v-if="this.activityMsg.activityStatus == 1"
            >发布</el-button
          >
          <el-button
            type="warning"
            icon="el-icon-video-pause"
             v-hasPermi="['activity:manage:save']"
            v-if="this.activityMsg.activityStatus != 3"
            @click="pushActive(3)"
            >结束</el-button
          >
        </div>
        <div style="clear: both"></div>
      </div>

      <div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="grid-content bg-green">
              <div class="titleStat">报名统计</div>
              <div class="remarkStat">
                <i class="el-icon-user" style="font-size: 40px" />
                总报名人数：{{ this.activityCount.regCount }}
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content bg-yellow">
              <div class="titleStat">人员来源统计</div>
              <div class="remarkStat">
                <div style="display: flex; justify-content: start">
                  <i class="el-icon-user" style="font-size: 40px" />
                  <div>
                    移动报名人数:{{ this.activityCount.appCount }};
                    PC报名人数:{{ this.activityCount.pcCount }}<br />
                    批量导入人数:{{ this.activityCount.exportCount }}
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content bg-red">
              <div class="titleStat">签到统计</div>
              <div class="remarkStat">
                <i class="el-icon-s-custom" style="font-size: 40px" />
                总签到人数：{{ signCount }}
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content bg-gray">
              <div class="titleStat">票款统计</div>
              <div class="remarkStat">
                <i class="el-icon-money" style="font-size: 40px" />{{
                  moneyCount
                }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row
          :gutter="20"
          style="
            background: url(../../../assets/images/yuanzi1.png) center center
              no-repeat;
          "
        >
          <el-col :span="6">
            <div class="grid-content" style="color: #000; padding: 10px 20px">
              <div class="titleIcon"><i class="el-icon-date" /></div>
              <div class="remarkIcon">活动前准备</div>
              <div class="activityMenu">
                <el-collapse accordion @change="handleChangeActive">
                  <el-collapse-item name="info">
                    <div slot="title">
                      <a
                        @click.stop="(e) => ActiveEdit(e)"
                        style="color: #1890ff"
                        >活动基本信息</a
                      >
                    </div>
                    <div>说明：活动基本信息查看&修改</div>
                  </el-collapse-item>
                  <el-collapse-item
                    v-hasPermi="['activity:manage:saveAdmin']"
                    name="shareadmin"
                  >
                    <div slot="title">
                      <a
                        @click.stop="(e) => shareAdminEdit(e)"
                        style="color: #1890ff"
                        >分配管理员</a
                      >
                    </div>
                    <div>说明：活动管理员配置</div>
                  </el-collapse-item>
                  <el-collapse-item
                    v-hasPermi="['activity:manage:saveSignAdmin']"
                    name="shareadmin"
                  >
                    <div slot="title">
                      <a
                        @click.stop="(e) => shareSignEdit(e)"
                        style="color: #1890ff"
                        >分配签到人员</a
                      >
                    </div>
                    <div>说明：活动签到人员配置</div>
                  </el-collapse-item>
                  <el-collapse-item name="formCreat">
                    <div slot="title">
                      <a
                        @click.stop="(e) => linkClick(e, '/activity/formCreat')"
                        style="color: #1890ff"
                        >报名表单管理</a
                      >
                    </div>
                    <div>说明：设置报名表单</div>
                  </el-collapse-item>
                  <el-collapse-item title="" name="privacy">
                    <div slot="title">
                      <a
                        @click.stop="(e) => linkClick(e, '/activity/privacy')"
                        style="color: #1890ff"
                        >隐私设置</a
                      >
                    </div>
                    <div>说明：活动隐私条款维护</div>
                  </el-collapse-item>
                  <el-collapse-item name="ticketType">
                    <div slot="title">
                      <a
                        @click.stop="
                          (e) => linkClick(e, '/activity/ticketType')
                        "
                        style="color: #1890ff"
                        >票种设置</a
                      >
                    </div>
                    <div>说明：票种配置</div>
                  </el-collapse-item>
                  <el-collapse-item title="" name="preferential">
                    <div slot="title">
                      <a
                        @click.stop="
                          (e) => linkClick(e, '/activity/preferential')
                        "
                        style="color: #1890ff"
                        >优惠券设置</a
                      >
                    </div>
                    <div>说明：优惠券设置</div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content" style="color: #000">
              <div class="titleIcon"><i class="el-icon-s-opportunity" /></div>
              <div class="remarkIcon">活动推广</div>
              <div class="activityMenu">
                <el-collapse accordion @change="handleChange">
                  <el-collapse-item name="publicize">
                    <template slot="title">
                      <a
                        @click.stop="(e) => linkClick(e, '/activity/publicize')"
                        style="color: #1890ff"
                        >活动宣传</a
                      >
                    </template>
                    <div>说明：活动宣传相关的宣传推广</div>
                  </el-collapse-item>
                  <el-collapse-item title="" name="yaoqing">
                    <template slot="title">
                      <a style="color: #1890ff">活动邀请</a>
                    </template>
                    <div style="text-align: -webkit-center">
                      <div style="display: flex">
                        <div style="width: 60px">PC报名:</div>
                        <div
                          style="
                            width: calc(100% - 60px);
                            word-break: break-all;
                            text-align: left;
                          "
                        >
                          {{ signPCUrl }}
                        </div>
                      </div>
                    </div>
                    <div style="text-align: -webkit-center">
                      <div style="display: flex">
                        <div style="width: 60px">手机报名:</div>
                        <div
                          style="
                            width: calc(100% - 60px);
                            word-break: break-all;
                            text-align: left;
                          "
                        >
                          {{ signUrl }}
                        </div>
                      </div>
                      <!-- <div id="qrcode" ref="qrcode"></div> -->
                      <vue-qr
                        class="qr-code"
                        :logoSrc="imageUrl"
                        :text="this.signUrl"
                        :size="200"
                        :whiteMargin="true"
                        :autoColor="true"
                        :correctLevel="3"
                      />
                    </div>
                  </el-collapse-item>
                  <el-collapse-item title="" name="tuiguang">
                    <template slot="title">
                      <a
                        style="color: #1890ff"
                        @click.stop="(e) => HandleChannel()"
                        >渠道推广</a
                      >
                    </template>
                    <div>说明：活动报名相关的推广</div>
                  </el-collapse-item>
                  <el-collapse-item title="" name="通知">
                    <template slot="title">
                      <a
                        style="color: #1890ff"
                        @click.stop="(e) => HandleMessage()"
                        >通知类日志</a
                      >
                    </template>
                    <div>说明：通知类相关日志</div>
                  </el-collapse-item>
                  <!-- <el-collapse-item title="提醒管理">
                    <div>提醒管理</div>
                  </el-collapse-item> -->
                </el-collapse>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content" style="color: #000">
              <div class="titleIcon"><i class="el-icon-s-claim" /></div>
              <div class="remarkIcon">活动过程</div>
              <div class="activityMenu">
                <el-collapse accordion>
                  <el-collapse-item>
                    <template slot="title">
                      <a
                        @click.stop="(e) => linkClick(e, '/activity/approval')"
                        style="color: #1890ff"
                      >
                        报名审批管理
                      </a>
                    </template>
                    <div>说明：报名人员审批以及查看</div>
                  </el-collapse-item>
                  <el-collapse-item>
                    <template slot="title">
                      <a
                        @click.stop="(e) => linkClick(e, '/activity/personnel')"
                        style="color: #1890ff"
                      >
                        报名人员管理
                      </a>
                    </template>
                    <div>说明：报名人员后台导入修改导出等操作</div>
                  </el-collapse-item>
                  <el-collapse-item title="">
                    <template slot="title">
                      <a
                        @click.stop="(e) => linkClick(e, '/activity/order')"
                        style="color: #1890ff"
                      >
                        订单管理
                      </a>
                    </template>
                    <div>说明：所有报名人员的订单信息查询</div>
                  </el-collapse-item>
                  <el-collapse-item title="">
                    <template slot="title">
                      <a
                        @click.stop="
                          (e) => linkClick(e, '/activity/signManage')
                        "
                        style="color: #1890ff"
                      >
                        签到管理
                      </a>
                    </template>
                    <div>说明：所有签到与未签到的人员信息查询</div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="grid-content" style="color: #000">
              <div class="titleIcon"><i class="el-icon-s-data" /></div>
              <div class="remarkIcon">统计分析</div>
              <div class="activityMenu">
                <el-collapse accordion>
                  <el-collapse-item>
                    <template slot="title">
                      <a
                        @click.stop="
                          (e) => linkClick(e, '/activity/statistical')
                        "
                        style="color: #1890ff"
                      >
                        统计与数据分析
                      </a>
                    </template>
                    <div>说明：人员来源,签到,票款,票种相关统计</div>
                  </el-collapse-item>
                  <!-- <el-collapse-item title="问卷调查">
                    <div>问卷调查</div>
                  </el-collapse-item> -->
                </el-collapse>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <editpost v-if="editOpen == true"></editpost>
    <shareAdmin v-if="activeOpen == true"></shareAdmin>
    <editSign v-if="signOpen == true"></editSign>
    <shareChannel v-if="channelOpen == true"></shareChannel>
    <shareMessage v-if="messageOpen == true"></shareMessage>
  </div>
</template>

<script>
import request from "@/utils/request";
import { constantRoutes } from "@/router";
import { mapGetters } from "vuex";
import editpost from "./editpost.vue";
import shareAdmin from "./shareAdmin.vue";
import editSign from "./editSign.vue";
import shareChannel from "./shareChannel.vue";
import shareMessage from "./shareMessage.vue";
import {
  listPost,
  getPost,
  delPost,
  addPost,
  updatePost,
} from "@/api/system/post";
import vueQr from "vue-qr";
import { develop } from "@/utils/baseKey.js";
import shareMessageVue from "./shareMessage.vue";
export default {
  name: "Index",
  data() {
    return {
      activityMsg: {},
      editOpen: false,
      activeOpen: false,
      activityCount: {
        regCount: 0,
        pcCount: 0,
        appCount: 0,
        exportCount: 0,
        signUrl: "",
      },
      queryParams: {},
      qrcode: undefined,
      signOpen: false,
      channelOpen: false,
      messageOpen: false,
      imageUrl: require("../../../assets/images/yuanzi.png"),
      signCount: 0,
      moneyCount: 0,
    };
  },
  components: {
    editpost,
    shareAdmin,
    editSign,
    shareChannel,
    vueQr,
    shareMessage,
  },
  created() {
    this.activityMsg = JSON.parse(sessionStorage.getItem("activity"));
    //  "/registration_mobileTest/?activityNo=" +
    let keyWords = develop() == "test" ? "Test" : "";
    this.signUrl =
      window.location.origin +
      "/registration_mobile" +
      keyWords +
      "/?activityNo=" +
      this.activityMsg.activityId;
    this.signPCUrl =
      window.location.origin +
      "/registration" +
      keyWords +
      "/?activityNo=" +
      this.activityMsg.activityId;
  },
  watch: {},
  mounted() {
    this.getPostType();
    this.regStaticsForHomePage();
  },
  computed: {
    ...mapGetters(["activeInfo"]),
  },
  methods: {
    regStaticsForHomePage() {
      request({
        url: "/stats/regStaticsForHomePage/" + this.activityMsg.activityId,
        method: "get",
      })
        .then((response) => {
          if (response.code == 200) {
            if (response.data && response.data.length > 0) {
              this.activityCount.regCount =
                response.data[0] && response.data[0].passCount
                  ? response.data[0].passCount
                  : 0;
              this.activityCount.pcCount =
                response.data[1] && response.data[1]["PC端报名"]
                  ? response.data[1]["PC端报名"]
                  : 0;
              this.activityCount.appCount =
                response.data[1] && response.data[1]["手机端报名"]
                  ? response.data[1]["手机端报名"]
                  : 0;
              this.activityCount.exportCount =
                response.data[1] && response.data[1]["导入报名"]
                  ? response.data[1]["导入报名"]
                  : 0;
              this.signCount =
                response.data[2] && response.data[2].signCount
                  ? response.data[2].signCount
                  : 0;
              this.moneyCount =
                response.data[3] && response.data[3].totalAmount
                  ? response.data[3].totalAmount
                  : 0;
            }
          } else {
            this.signCount = 0;
            this.activityCount.regCount = 0;
            this.activityCount.pcCount = 0;
            this.activityCount.appCount = 0;
            this.activityCount.exportCount = 0;
          }
        })
        .catch((err) => {
          this.signCount = 0;
          this.activityCount.regCount = 0;
          this.activityCount.pcCount = 0;
          this.activityCount.appCount = 0;
          this.activityCount.exportCount = 0;
          console.log(err);
        });
    },
    HandleMessage() {
      this.messageOpen = true;
    },
    HandleMessageCancel() {
      this.messageOpen = false;
    },
    HandleChannel() {
      this.channelOpen = true;
    },
    HandleChannelCancel() {
      this.channelOpen = false;
    },
    handleChangeActive(val) {},
    handleChange(val) {
      // if (val == "yaoqing") {
      //   if (!this.qrcode) {
      //     this.qrcodeFun();
      //   }
      // }
    },
    qrcodeFun() {
      this.$nextTick(() => {
        this.qrcode = new QRCode("qrcode", {
          errorCorrectionLevel: "H", //容错级别
          type: "image/png", //生成的二维码类型
          quality: 0.3, //二维码质量
          margin: 0, //二维码留白边距

          width: 150, // 二维码宽度，单位像素
          height: 150, // 二维码高度，单位像素
          text: this.signUrl,
        });
      });
    },
    regStatics(type) {
      request({
        url: "reg/regStatics/" + this.activityMsg.activityId + "/" + type,
        method: "get",
      })
        .then((response) => {
          if (response.code == 200) {
            if (type == 1) {
              this.activityCount.regCount = response.data[0].regCount;
            }
            if (type == 2) {
              response.data.map((item) => {
                if (item.regFrom == 1) {
                  this.activityCount.pcCount = item.count;
                }
                if (item.regFrom == 2) {
                  this.activityCount.appCount = item.count;
                }
                if (item.regFrom == 3) {
                  this.activityCount.exportCount = item.count;
                }
              });
            }
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    pushActive(status) {
      let str = "";
      if (status == 2) {
        str = "发布";
      }
      if (status == 3) {
        str = "结束";
      }
      this.$modal
        .confirm(
          "是否确认" + str + "" + this.activityMsg.activityName + '"的活动？'
        )
        .then(() => {
          request({
            url: "activityManage/updateActivity",
            method: "post",
            data: { id: this.activityMsg.id, activityStatus: status },
          })
            .then((response) => {
              if (response.code == 200) {
                this.activityMsg.activityStatus = status;
                sessionStorage.setItem(
                  "activity",
                  JSON.stringify(this.activityMsg)
                );
                this.$modal.msgSuccess("操作成功");
                // this.sysUserOpen = false;
                // this.getAdminList();
              }
            })
            .catch((err) => {
              console.log(err);
            });
          // return delPost(obj);
        });
    },
    linkClick(e, path) {
      e.stopPropagation(); // 阻止事件冒泡
      this.$router.push(path);
    },
    shareAdminEdit(e) {
      this.qrcode = undefined;
      e.stopPropagation(); // 阻止事件冒泡
      this.activeOpen = true;
    },
    shareSignEdit(e) {
      this.qrcode = undefined;
      e.stopPropagation(); // 阻止事件冒泡
      this.signOpen = true;
    },
    shareSignEditCancel() {
      this.signOpen = false;
    },
    shareAdminEditCancel() {
      this.activeOpen = false;
    },
    callbackHome() {
      this.$emit("callbackHome");
    },
    ActiveEdit(e) {
      this.qrcode = undefined;
      e.stopPropagation(); // 阻止事件冒泡
      this.editOpen = true;
      //   alert(1);
    },
    ActiveEditCancel() {
      this.editOpen = false;
    },
    setActiveImfo(obj) {
      this.activityMsg = obj;
    },
    // 菜单选择事件
    handleSelect(activity) {
      localStorage.setItem("activity", activity);
      this.showType = 2;
    },
    // 当前激活的路由
    activeRoutes(key) {
      var routes = [];
      if (this.childrenMenus && this.childrenMenus.length > 0) {
        this.childrenMenus.map((item) => {
          if (key == item.parentPath || (key == "index" && "" == item.path)) {
            routes.push(item);
          }
        });
      }
      if (routes.length > 0) {
        // window.location.href="http://atomgitactivity.com/atomgit/activity/post"
        this.$router.push({ path: routes[0].path });
        // window.location.reload()
        this.$store.commit("SET_SIDEBAR_ROUTERS", routes);
      } else {
        this.$store.dispatch("app/toggleSideBarHide", true);
      }
    },
    ishttp(url) {
      return url.indexOf("http://") !== -1 || url.indexOf("https://") !== -1;
    },
    getPostType() {
      request({
        url: "/system/dict/data/list?pageNum=1&pageSize=100&dictType=activity_status&status=0",
        method: "get",
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            this.postTypeOption = response.rows;
            this.queryParams.activityStatus = response.rows[0].dictValue;
            this.getList();
          } else {
            this.getList();
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getList() {
      this.loading = true;
      let param = JSON.parse(JSON.stringify(this.queryParams));
      param.activityStatus = Number(this.queryParams.activityStatus);
      request({
        url: "/activityManage/list",
        method: "post",
        data: param,
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            this.postList = response.rows;
            this.total = response.total;
            this.loading = false;
          } else {
            this.postList = [];
            this.total = 0;
            this.loading = false;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleClick() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.queryParams.searchParam = undefined;
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      this.form = {};
      this.open = true;
      this.title = "添加活动";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.startDate = this.form.stratEndTime[0];
          this.form.endDate = this.form.stratEndTime[1];
          if (this.form.activityId != undefined) {
            updatePost(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPost(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    submitFlowForm() {
      this.$refs["flowform"].validate((valid) => {
        if (valid) {
          request({
            url: "/activityManage/saveActivityFlow",
            method: "post",
            data: this.flowform,
          })
            .then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.flowOpen = false;
              this.getList();
            })
            .catch((err) => {
              console.log(err);
            });
        }
      });
    },
    // 取消按钮
    cancel() {
      alert(1);
      this.open = false;
      //   this.form = {};
      //   // this.reset();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      debugger;
      const postIds = row.postId;
      let obj = {};
      obj.id = row.id;
      obj.activityId = row.activityId;
      obj.isValidate = 0;
      this.$modal
        .confirm('是否确认删除活动编号为"' + row.activityId + '"的数据？')
        .then(function () {
          return delPost(obj);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss">
.app-container {
  font-size: 14px;
}
.activityMainMsg {
  line-height: 30px;
  font-size: 16px;
  margin: 10px 0px;
}
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 4px;
}
.bg-purple-dark {
  background: #99a9bf;
}
.bg-green {
  background: #67c23a;
}
.bg-yellow {
  background: #e6a23c;
}
.bg-red {
  background: #f56c6c;
}
.bg-gray {
  background: rgb(70, 185, 203);
}
.bg-purple-light {
  background: #e5e9f2;
}
.grid-content {
  border-radius: 4px;
  min-height: 36px;
  color: #ffffff;
  padding: 10px;
  height: 113px;
}
.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
.titleStat {
  font-size: 16px;
  font-weight: 500;
}
.remarkStat {
  //   line-height: 24px;
  font-size: 15px;
  margin-top: 15px;
}
.titleIcon {
  font-size: 80px;
  margin: auto;
  text-align: center;
  color: #1890ff;
}
.remarkIcon {
  font-weight: 700;
  font-size: 16px;
  margin: auto;
  text-align: center;
}
.activityMenu {
  width: 80%;
  margin-left: 10%;
  padding-top: 10px;
}
</style>


<style scoped lang="scss">
</style>

