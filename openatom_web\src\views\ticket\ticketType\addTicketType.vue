<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    :visible.sync="showDialog"
    :title="dialogTitle"
    width="600px"
    @close="cancelDialog"
  >
    <el-form
      ref="dialogData"
      :model="dialogData"
      :rules="rules"
      label-width="120px"
      class="dialogFormDataAlert"
      @submit.native.prevent
    >
      <el-form-item label="票种名称" prop="name">
        <el-input
          v-model.trim="dialogData.name"
          type="text"
          maxlength="15"
          show-word-limit
          placeholder="请输入票种名称"
        />
      </el-form-item>
      <el-form-item label="票种价格" prop="price">
        <el-input
          type="text"
          v-model.trim="dialogData.price"
          show-word-limit
          placeholder="请输入票种价格"
        />
        <!-- <el-input-number v-model="dialogData.price" controls-position="right"  :min="0" :max="99999.99" style="width:100%"></el-input-number> -->
      </el-form-item>
      <el-form-item label="是否使用活动价" prop="isActivityPrice">
        <el-select
          size="small"
          v-model.trim="dialogData.isActivityPrice"
          clearable
          placeholder="请选择是否使用活动价"
          style="width: 100%"
          @change="activityPriceChange"
        >
          <el-option
            v-for="item in isActivityPrice"
            :key="item.code"
            :label="item.cnName"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="活动价格"
        prop="activityPrice"
        v-if="dialogData.isActivityPrice == 1"
      >
        <el-input
          type="text"
          v-model.trim="dialogData.activityPrice"
          show-word-limit
          placeholder="请输入活动价格"
        />
      </el-form-item>

      <el-form-item label="数量" prop="stock">
        <el-input
          type="text"
          v-model.trim="dialogData.stock"
          maxlength="50"
          show-word-limit
          placeholder="请输入数量"
        />
      </el-form-item>
      <el-form-item label="是否上架" prop="isShow">
        <el-select
          size="small"
          v-model.trim="dialogData.isShow"
          clearable
          placeholder="请选择是否上架"
          style="width: 100%"
        >
          <el-option
            v-for="item in isShow"
            :key="item.code"
            :label="item.cnName"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否可售" prop="isSales">
        <el-select
          size="small"
          v-model.trim="dialogData.isSales"
          clearable
          placeholder="请选择是否可售"
          style="width: 100%"
        >
          <el-option
            v-for="item in isSales"
            :key="item.code"
            :label="item.cnName"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model.trim="dialogData.sort"
          :min="1"
          :max="50"
          label="请输入数量"
        />
      </el-form-item>
      <el-form-item label="票种权益" prop="info">
        <el-input
          v-model="dialogData.info"
          type="textarea"
          placeholder="请输入票种权益"
        />
      </el-form-item>
      <div class="formBtn" style="text-align: center">
        <el-button
          type="primary"
          @click.stop="submitDialog"
          :loading="btnloading"
          >提交</el-button
        >
        <el-button @click="cancelDialog">取消</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>
<script>
import ticketType, { updateTicketType } from "@/api/ticket/ticketType";
import { checkPrice, checkStock } from "@/utils/validate";
export default {
  name: "AddTicketType",
  components: {},
  props: {
    showDialog: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    dialogTitle: {
      type: String,
      default: () => {
        return "";
      },
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  watch: {
    rowData(newVal, _oldVal) {
      if (newVal) {
        this.dialogData = newVal;
      }
    },
    showDialog(newVal, _oldVal) {
      if (newVal) {
        this.isVisible = newVal;
      }
    },
  },
  data() {
    return {
      activityid: JSON.parse(sessionStorage.getItem("activity")).activityId,
      isVisible: false,
      isShow: [
        {
          value: 0,
          code: 0,
          cnName: "下架",
        },
        {
          value: 1,
          code: 1,
          cnName: "上架",
        },
      ],
      isSales: [
        {
          value: 0,
          code: 0,
          cnName: "不可售",
        },
        {
          value: 1,
          code: 1,
          cnName: "可售",
        },
      ],
      isActivityPrice: [
        {
          value: 0,
          code: 0,
          cnName: "否",
        },
        {
          value: 1,
          code: 1,
          cnName: "是",
        },
      ],
      dialogData: {
        name: "",
        price: null,
        stock: null,
        info: "",
        sort: 1,
        isShow: undefined,
        activityid: this.activityid,
        isSales: undefined,
        activityPrice: undefined,
        isActivityPrice: undefined,
      },
      rules: {
        name: [{ required: true, message: "请输入票种名称", trigger: "blur" }],
        price: [
          { required: true, message: "请输入票种价格", trigger: "blur" },
          { required: true, validator: checkPrice, trigger: "blur" },
        ],
        activityPrice: [
          { required: false, message: "请输入活动价格", trigger: "blur" },
          { required: false, validator: checkPrice, trigger: "blur" },
        ],
        stock: [
          { required: true, message: "请输入数量", trigger: "blur" },
          { required: true, validator: checkStock, trigger: "blur" },
        ],
        isShow: [
          { required: true, message: "请选择是否上架", trigger: "blur" },
        ],
        isSales: [
          { required: true, message: "请选择是否可售", trigger: "blur" },
        ],
        isActivityPrice: [
          { required: true, message: "请选择是否启用活动价", trigger: "blur" },
        ],
      },
      btnloading: false,
    };
  },
  methods: {
    activityPriceChange(val) {
      if (val && val == 1) {
        this.rules.activityPrice[0].required = true;
        this.rules.activityPrice[1].required = true;
      } else {
        this.rules.activityPrice[0].required = false;
        this.rules.activityPrice[1].required = false;
      }
    },
    submitDialog() {
      // alert(1)
      this.$refs.dialogData.validate(async (valid) => {
        let data = {
          id: this.dialogData.id,
          name: this.dialogData.name,
          price: this.dialogData.price,
          stock: this.dialogData.stock,
          info: this.dialogData.info,
          sort: this.dialogData.sort,
          isShow: this.dialogData.isShow,
          activityid: this.dialogData.activityid,
          isSales: this.dialogData.isSales,
          activityPrice: this.dialogData.activityPrice,
          isActivityPrice: this.dialogData.isActivityPrice,
        };
        if (data.isActivityPrice == 0) {
          data.activityPrice = undefined;
        }
        if (valid) {
          this.btnloading = true;
          if (this.dialogData.id) {
            ticketType
              .updateTicketType(data)
              .then((res) => {
                if (res.code === 200) {
                  this.$parent.init();
                  this.$message({
                    type: "success",
                    message: "修改成功",
                  });
                  this.cancelDialog();
                  this.btnloading = false;
                } else {
                  this.btnloading = false;
                }
              })
              .catch(() => {
                this.btnloading = false;
              });
          } else {
            ticketType
              .addTicketType(data)
              .then((res) => {
                if (res.code === 200) {
                  this.$parent.init();
                  this.$message({
                    type: "success",
                    message: "新增成功",
                  });
                  this.cancelDialog();
                  this.btnloading = false;
                } else {
                  this.btnloading = false;
                }
              })
              .catch(() => {
                this.btnloading = false;
              });
          }
        }
      });
    },
    cancelDialog() {
      setTimeout(() => {
        this.dialogData = {
          name: "",
          price: null,
          stock: null,
          info: "",
          sort: 1,
          isShow: 1,
          activityid: this.activityid,
          isSales: undefined,
          activityPrice: undefined,
          isActivityPrice: undefined,
        };
        this.$refs.dialogData.resetFields();
        this.isVisible = false;
        this.$emit("cancelDialogFun", false);
      }, 100);
    },
  },
};
</script>

<style scoped lang="scss">
</style>

