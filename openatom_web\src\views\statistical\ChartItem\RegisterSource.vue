<template>
  <div class="chart-content">
    <div class="top-chart"></div>
    <el-table size="mini" :data="yData">
      <el-table-column label="" prop="name"></el-table-column>
      <el-table-column label="移动端报名" prop="value"></el-table-column>

    </el-table>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { querySource } from '@/api/statistical/index'
export default {
  props: {},
  components: {},
  data () {
    return {
      chart: null,
      activityId: '',
      chartData: null,
      yData: [],
      xData: [],
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      tableData: [],
      option: {
        title: {
          text: '报名来源统计(报名通过)',
          left: 'top'
        },
        tooltip: {
          trigger: "item",
            formatter: "{b}: {c}人 ({d}%)",
        },
        series: [
          {
            name: "报名来源统计",
            type: "pie",
            radius: ["40%", "50%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 100,
              borderColor: "#fff",
              borderWidth: 2,
            },
           
            labelLine: {
              show: true,
            },
            data: [

            ],
            label: {
              show: true,
              formatter: function (params) {
                console.log(params)
                var name = params.name;
                var percent = params.value || 0;
                return name + "\n" + percent + "人";
              },
            },
          },
        ],
      }
    }
  },
  mounted () {
    this.initChart()
    
  },
  created () {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activityId = activeInfo.activityId;
  },
  methods: {
    async initChart () {
      let res = await querySource(this.activityId)

      this.chartData = res.data
      // this.tableData = [res.data]
      for (const key in res.data) {
        if (Object.hasOwnProperty.call(res.data, key)) {
          const element = res.data[key];
          this.yData.push({ value: element, name: key })

        }
      }
      this.drawChart()
    },
    drawChart () {
      this.chart = echarts.init(document.getElementsByClassName('top-chart')[1])
      this.option.series[0].data = this.yData
      this.chart.setOption(this.option)
    //    window.onresize = () => {
    //   this.chart.resize();
    // };
    }
  }
}
</script>
<style lang='scss'></style>
