<template>
  <div   v-loading="loading">
    <IFrame :src="iframeUrl"></IFrame>
  </div>
</template>
<script>
import { develop } from "@/utils/baseKey.js";
export default {
  data() {
    return {
      iframeUrl: "",
      loading:true
    };
  },
  created() {
    if (develop() == "test") {
      this.iframeUrl = "https://shentoutest-openatomcon.openatom.cn/dwsurvey/#/dw/survey";
    } else if (develop() == "prod") {
      this.iframeUrl = "https://openatomcon.openatom.cn/dwsurvey/#/dw/survey";
    } else if (develop() == "develop") {
        this.iframeUrl = "https://openatomcon.openatom.cn/dwsurvey/#/dw/survey";
      // this.iframeUrl = "http://*************:8091/dwsurvey/#/dw/survey";
    }
    setTimeout(()=>{
      this.loading=false
    },2000)
  },
  mounted: function () {},
};
</script>