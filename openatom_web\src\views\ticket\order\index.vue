<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="订单管理" name="1">
        <div v-if="activeName == 1">
          <el-form ref="form" :inline="true" :model="searchForm">
            <el-form-item label="订单编号">
              <el-input
                placeholder="请输入订单编号"
                v-model.trim="searchForm.orderNum"
              ></el-input>
            </el-form-item>
            <el-form-item label="订单状态">
              <el-select
                size="small"
                v-model.trim="searchForm.orderStatus"
                clearable
                placeholder="请选择订单状态"
              >
                <el-option
                  v-for="item in paymentStatus"
                  :key="item.value"
                  selected="selected"
                  :label="item.cnName"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="支付方式">
              <el-select
                size="small"
                v-model.trim="searchForm.paidMethod"
                clearable
                placeholder="请选择支付方式"
              >
                <el-option
                  v-for="item in paymentMode"
                  :key="item.value"
                  selected="selected"
                  :label="item.cnName"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="订单日期">
              <el-date-picker
                size="small"
                v-model="orderTimes"
                v-trimSpace
                type="daterange"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item style="margin-left: 10px">
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-search"
                @click="search(1)"
                >查询</el-button
              >
              <el-button size="mini" icon="el-icon-refresh" @click="resetSearch"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
          <el-table
            ref="orderTable"
            :data="tableData"
            style="width: 100%"
            v-loading="tableLoading"
            :element-loading-text="refoundText"
          >
            <el-table-column
              :resizable="false"
              align="center"
              label="序号"
              type="index"
              width="50"
            />
            <el-table-column
              :resizable="false"
              align="center"
              label="活动名称"
              width="150"
              :show-overflow-tooltip="true"
            >
              {{ this.activityName }}
            </el-table-column>
            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              prop="orderNum"
              label="订单号"
              width="150"
            />
            <!-- <el-table-column :show-overflow-tooltip="true" prop="buyCounts" label="购买数量" /> -->

            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              prop="orderNum"
              label="购票信息"
              width="400"
            >
              <template slot-scope="{ row }">
                <template v-for="(item, num) in row.ordersDetails">
                  <div
                    :key="num"
                    style="display: flex; justify-content: space-between"
                  >
                    <span
                      style="padding: 0 5px; width: 70%; white-space: normal"
                      >{{ item.product.name }}({{ item.product.price }}元)</span
                    >
                    <span
                      style="
                        padding-left: 5px;
                        width: 30%;
                        display: flex;
                        align-items: center;
                      "
                      >数量<span style="padding: 0 5px">*</span>
                      <span>{{ item.buyCounts }}</span>
                    </span>

                    <el-button
                      title="购买人员明细"
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="viewOrderInfo(item)"
                    ></el-button>
                  </div>
                </template>
              </template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              prop="createTime"
              label="订单时间"
              width="150"
            >
              <template slot-scope="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              width="80"
              prop="userId"
              label="购票人"
            />

            <!-- <el-table-column :resizable="false" :show-overflow-tooltip="true" prop="phone" label="购票手机" /> -->
            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              prop="orderStatus"
              label="订单状态"
              width="100"
            >
              <template v-slot="scope">
                <span v-for="item in paymentStatus" :key="item.value">{{
                  scope.row.orderStatus === item.value ? item.cnName : ""
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              prop="paidTime"
              label="支付时间"
              width="120"
            >
              <template slot-scope="{ row }">
                {{ formatDate(row.paidTime) }}
              </template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              prop="paidMethod"
              label="支付方式"
              width="100"
            >
              <template v-slot="scope">
                <span v-for="item in paymentMode" :key="item.value">{{
                  scope.row.paidMethod === item.value ? item.cnName : ""
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              prop="isPreferential"
              label="是否优惠"
              width="100"
            >
              <template v-slot="scope">
                <span>
                  {{ scope.row.isPreferential === false ? "否" : "是" }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              prop="couponCode"
              label="优惠码"
              width="120"
            />
            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              width="80"
              prop="orderAmount"
              label="应付金额"
            />
            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              width="80"
              prop="paidAmount"
              label="实付金额"
            />
            <el-table-column
              :resizable="false"
              :show-overflow-tooltip="true"
              width="80"
              prop="paidAmount"
              label="退款金额"
            >
              <template slot-scope="{ row }">
                {{ row.orderRefund ? row.orderRefund.refundAmount : "" }}
              </template>
            </el-table-column>

            <el-table-column
              :resizable="false"
              width="220px"
              label="操作"
              fixed="right"
            >
              <template v-slot="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  :disabled="
                    scope.row.orderStatus === '10' ||
                    scope.row.orderStatus === '30' ||
                    scope.row.orderStatus === '40'
                      ? false
                      : true
                  "
                  @click="handleDeleteOrder(scope.row)"
                  >删除</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  :disabled="
                    scope.row.orderStatus === '20' ||
                    scope.row.orderStatus === '70'
                      ? Number(scope.row.paidAmount) == 0
                        ? true
                        : false
                      : true
                  "
                  @click="handleRefund(scope.row)"
                  >退款</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-search"
                  @click="handleTrade(scope.row)"
                  >交易流水</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="searchForm.pageNum"
            :limit.sync="searchForm.pageSize"
            @pagination="getData"
          />
          <el-dialog
            title="购买人员明细"
            :visible.sync="showOrderInfo"
            width="50%"
          >
            <el-table
              :data="ticketList"
              style="width: 100%"
              :resizable="false"
              height="500"
            >
              <!-- <el-table-column :resizable="false" align="center" label="序号" type="index" width="50" /> -->
              <el-table-column
                :resizable="false"
                :show-overflow-tooltip="true"
                prop="name"
                label="姓名"
              />
              <el-table-column
                :resizable="false"
                :show-overflow-tooltip="true"
                prop="phone"
                label="购票手机"
              />
              <el-table-column
                :resizable="false"
                :show-overflow-tooltip="true"
                prop="email"
                label="邮箱"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="createTime"
                label="购买时间"
              />
            </el-table>
          </el-dialog>
        </div>
      </el-tab-pane>
      <el-tab-pane label="交易流水" name="2">
        <tradeFlow
          v-if="activeName == 2"
          :currOrderNum="currOrderNum"
        ></tradeFlow>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import tradeFlow from "./tradeFlow.vue";
import order from "@/api/ticket/order/index";
import { formatDate } from "@/utils/index";
import request from "@/utils/request";
const defaultSearchForm = {
  orderStatus: "",
  startTime: "",
  endTime: "",
  pageNum: 1,
  pageSize: 10,
};
export default {
  name: "Order",
  components: {},
  watch: {
    orderTimes(cur, _old) {
      if (cur) {
        this.searchForm.startTime = cur[0];
        this.searchForm.endTime = cur[1];
      } else {
        this.searchForm.startTime = "";
        this.searchForm.endTime = "";
      }
      return [this.searchForm.startTime, this.searchForm.endTime];
    },
  },
  components: { tradeFlow },
  data() {
    return {
      activeName: "1",
      currOrderNum: "",
      searchForm: { ...defaultSearchForm },
      showOrderInfo: false,
      formatDate,
      total: 0,
      ticketList: [],
      paymentStatus: [
        {
          value: "10",
          cnName: "待付款",
        },
        {
          value: "20",
          cnName: "已付款",
        },
        {
          value: "30",
          cnName: "已取消",
        },
        {
          value: "40",
          cnName: "交易关闭",
        },
        {
          value: "50",
          cnName: "退款中",
        },
        {
          value: "60",
          cnName: "退款成功",
        },
        {
          value: "70",
          cnName: "退款失败",
        },
      ],
      paymentMode: [
        {
          value: 1,
          cnName: "微信",
        },
        {
          value: 2,
          cnName: "支付宝",
        },
      ],
      orderTimes: [], // 订单日期
      tableData: [],
      tableHight: "600px",
      pageData: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      tableLoading: false,
      timer: null,
      refoundText: null,
    };
  },
  destroyed() {
    clearInterval(this.timer);
    this.timer = null;
  },
  mounted() {
    this.search(1);
  },
  methods: {
    handleTrade(row) {
      this.activeName = "2";
      this.currOrderNum = row.orderNum;
    },
    handleClick() {
      this.currOrderNum = "";
    },
    init(val) {
      this.getData();
    },
    // 查看订单详情
    async viewOrderInfo(row) {
      await this.getTicketInfo(row);
      this.showOrderInfo = true;
    },
    async getTicketInfo(row) {
      let res = await order.getOrderPersonList({ orderDetailsId: row.id });
      this.ticketList = res.data;
    },
    getData(order) {
      this.tableLoading = true;
      this.searchForm.activityId = JSON.parse(
        sessionStorage.getItem("activity")
      ).activityId;
      request({
        url: "/order/manage/list",
        method: "get",
        params: this.searchForm,
      })
        .then((response) => {
          if (response.code == 200) {
            this.tableLoading = false;
            this.tableData = response.rows;
            this.total = response.total;
            if (order) {
              if (response.rows.length > 0) {
                response.rows.map((item) => {
                  if (item.orderNum == order) {
                    this.tableLoading = true;
                    this.queryByOutRefundNo(item);
                  }
                });
              }
            }
          } else {
            this.tableLoading = false;
            this.tableData = [];
            this.total = 0;
          }
        })
        .catch((err) => {
          console.log(err);
          this.tableLoading = false;
          this.tableData = [];
          this.total = 0;
        });
    },
    search(val) {
      this.pageData.pageNum = val;
      this.init(val);
    },
    resetSearch() {
      this.searchForm = {
        ...defaultSearchForm,
      };
      this.orderTimes = [];
      this.searchForm.pageNum = 1;
      this.init(1);
    },
    queryByOutRefundNo(row) {
      let url = "";
      if (row.paidMethod == 2) {
        url =
          "/alirefund/queryByOutRefundNo?refundNo=" +
          row.orderRefund.refundNum +
          "&orderNum=" +
          row.orderNum;
      }
      if (row.paidMethod == 1) {
        url =
          "/wxrefund/queryByOutRefundNo?refundNo=" + row.orderRefund.refundNum;
      }
      request({
        url: url,
        method: "get",
      })
        .then((response) => {
          if (response.code === 200 && response.msg === "SUCCESS") {
            this.$confirm("已成功发起退款,请耐心等候", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              closeOnClickModal: false,
              type: "success",
            })
              .then(async () => {
                this.getData(undefined);
              })
              .catch(() => {
                return false;
              });
            // this.$message.success("退款成功");
            this.tableLoading = false;
            this.refoundText = null;
            if (this.timer) {
              clearInterval(this.timer);
              this.timer = null;
            }
          } else if (response.code === 200 && response.msg == "PROCESSING") {
            if (this.timer === null) {
              this.timer = setInterval(() => {
                this.queryByOutRefundNo(row);
              }, 2000);
            }
          } else {
            this.$message.error("退款失败");
          }
        })
        .catch((error) => {
          console.log(error);
          this.$message.error("退款异常");
        });
    },
    handleRefund(row) {
      this.$confirm(
        "确定要对编号为" + row.orderNum + "的订单发起退款？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          closeOnClickModal: false,
          type: "warning",
        }
      ).then(() => {
        if (row.paidMethod == 1) {
          this.wxRefund(row);
        } else if (row.paidMethod == 2) {
          this.aliRefund(row);
        }
      });
    },
    aliRefund(row) {
      this.tableLoading = true;
      this.refoundText = "退款中...";
      request({
        url: "/alirefund/refund",
        method: "post",
        data: { order: { orderNum: row.orderNum }, reason: "用户主动退款" },
      })
        .then((data) => {
          if (data.code === 200) {
            this.getData(row.orderNum);
          } else {
            this.tableLoading = false;
            this.refoundText = null;
            if (data.code === 5005) {
              this.$message.success(data.msg);
            } else {
              this.$message.error("退款异常");
            }
          }
        })
        .catch((err) => {
          console.log(err);
          this.tableLoading = false;
          this.refoundText = null;
          this.$message.error("退款异常");
        });
    },
    wxRefund(row) {
      this.tableLoading = true;
      this.refoundText = "退款中...";
      request({
        url: "/wxrefund/refund",
        method: "post",
        data: { order: { orderNum: row.orderNum }, reason: "用户主动退款" },
      })
        .then((data) => {
          if (data.code === 200) {
            this.getData(row.orderNum);
          } else {
            this.tableLoading = false;
            this.refoundText = null;
            if (data.code === 5005) {
              this.$message.success(data.msg);
            } else {
              this.$message.error("退款异常");
            }
          }
        })
        .catch((err) => {
          console.log(err);
          this.tableLoading = false;
          this.refoundText = null;
          this.$message.error("退款异常");
        });
    },
    handleDeleteOrder(data) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(async () => {
          let res = await order.delOrder(data.orderNum);
          if (res.code === 200) {
            this.$message.success("删除成功");
            this.getData();
          }
        })
        .catch(() => {
          return false;
        });
    },
    handleBackOrder(data) {
      this.$confirm("确定退款吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(() => {})
        .catch(() => {
          return false;
        });
    },
    handleCurrentChange(val) {
      this.search(val.page);
    },
  },
};
</script>

<style scoped lang="scss">
.tableBtnGroup {
  margin-bottom: 20px;
}
</style>

