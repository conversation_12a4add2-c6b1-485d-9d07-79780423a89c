<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="用户隐私协议" name="0" :disabled="flag1">
      </el-tab-pane>
      <el-tab-pane label="嘉宾授权协议" name="1" :disabled="flag2">
      </el-tab-pane>
    </el-tabs>
    <el-form
      ref="editorform"
      label-width="90px"
      :rules="editorformRules"
      :model="editorform"
    >
      <el-form-item label="活动名称:" prop="activityName">
        {{ this.editorform.activityName}}
      </el-form-item>
      <el-form-item label="标题:" prop="protocolName">
        <el-input
          v-model="editorform.protocolName"
          placeholder="请输入标题"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="内容:" prop="protocolContent">
        <div style="width: 80%">
          <quill-editor
            ref="editor"
            v-model="editorform.protocolContent"
            :options="editorOption"
            class="editor"
            @change="onEditorChange($event)"
          />
        </div>
      </el-form-item>
    </el-form>

    <el-row style="text-align: center; margin-top: 15px">
      <el-button :loading="Loading" @click="submitForm" type="primary"
        >提交</el-button
      >
    </el-row>
  </div>
</template>

<script>
import { quillEditor } from "vue-quill-editor";
import request from "@/utils/request";
export default {
  name: "privacy",
  components: {
    quillEditor,
  },
  data() {
    return {
      activeName: "0",
      flag1: false,
      flag2: true,
      Loading: false,
      editorform: {
        activityId: undefined,
        protocolName: undefined,
        protocolContent: undefined,
        activityName: undefined,
        protocolType: 1,
      },
      // 表单校验
      editorformRules: {
        protocolName: [
          { required: true, message: "标题不能为空", trigger: "blur" },
        ],
        protocolContent: [
          { required: true, message: "内容不能为空", trigger: "blur" },
        ],
      },
      editorOption: {
        modules: {
          toolbar: [
            ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
            ["blockquote", "code-block"], // 引用  代码块
            [{ header: 1 }, { header: 2 }], // 1、2 级标题
            [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
            [{ script: "sub" }, { script: "super" }], // 上标/下标
            [{ indent: "-1" }, { indent: "+1" }], // 缩进
            [{ direction: "rtl" }], // 文本方向
            [
              {
                //   "Normal"
                size: ["small",false , "large", "huge"],
              },
            ], // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6] }], // 标题
            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
            // [{ font: ['songti'] }], // 字体种类
            [{ align: [] }], // 对齐方式
            ["clean"], // 清除文本格式
            // ["image", "video"], // 链接、图片、视频
            // ["image"], // 链接、图片、视频
          ],
        },
        placeholder: "请输入正文",
      },

      titleConfig: [
        { Choice: ".ql-insertMetric", title: "跳转配置" },
        { Choice: ".ql-bold", title: "加粗" },
        { Choice: ".ql-italic", title: "斜体" },
        { Choice: ".ql-underline", title: "下划线" },
        { Choice: ".ql-header", title: "段落格式" },
        { Choice: ".ql-strike", title: "删除线" },
        { Choice: ".ql-blockquote", title: "块引用" },
        { Choice: ".ql-code", title: "插入代码" },
        { Choice: ".ql-code-block", title: "插入代码段" },
        { Choice: ".ql-font", title: "字体" },
        { Choice: ".ql-size", title: "字体大小" },
        { Choice: '.ql-list[value="ordered"]', title: "编号列表" },
        { Choice: '.ql-list[value="bullet"]', title: "项目列表" },
        { Choice: ".ql-direction", title: "文本方向" },
        { Choice: '.ql-header[value="1"]', title: "h1" },
        { Choice: '.ql-header[value="2"]', title: "h2" },
        { Choice: ".ql-align", title: "对齐方式" },
        { Choice: ".ql-color", title: "字体颜色" },
        { Choice: ".ql-background", title: "背景颜色" },
        { Choice: ".ql-image", title: "图像" },
        { Choice: ".ql-video", title: "视频" },
        { Choice: ".ql-link", title: "添加链接" },
        { Choice: ".ql-formula", title: "插入公式" },
        { Choice: ".ql-clean", title: "清除字体格式" },
        { Choice: '.ql-script[value="sub"]', title: "下标" },
        { Choice: '.ql-script[value="super"]', title: "上标" },
        { Choice: '.ql-indent[value="-1"]', title: "向左缩进" },
        { Choice: '.ql-indent[value="+1"]', title: "向右缩进" },
        { Choice: ".ql-header .ql-picker-label", title: "标题大小" },
        {
          Choice: '.ql-header .ql-picker-item[data-value="1"]',
          title: "标题一",
        },
        {
          Choice: '.ql-header .ql-picker-item[data-value="2"]',
          title: "标题二",
        },
        {
          Choice: '.ql-header .ql-picker-item[data-value="3"]',
          title: "标题三",
        },
        {
          Choice: '.ql-header .ql-picker-item[data-value="4"]',
          title: "标题四",
        },
        {
          Choice: '.ql-header .ql-picker-item[data-value="5"]',
          title: "标题五",
        },
        {
          Choice: '.ql-header .ql-picker-item[data-value="6"]',
          title: "标题六",
        },
        { Choice: ".ql-header .ql-picker-item:last-child", title: "标准" },
        {
          Choice: '.ql-size .ql-picker-item[data-value="small"]',
          title: "小号",
        },
        {
          Choice: '.ql-size .ql-picker-item[data-value="large"]',
          title: "大号",
        },
        {
          Choice: '.ql-size .ql-picker-item[data-value="huge"]',
          title: "超大号",
        },
        { Choice: ".ql-size .ql-picker-item:nth-child(2)", title: "标准" },
        { Choice: ".ql-align .ql-picker-item:first-child", title: "居左对齐" },
        {
          Choice: '.ql-align .ql-picker-item[data-value="center"]',
          title: "居中对齐",
        },
        {
          Choice: '.ql-align .ql-picker-item[data-value="right"]',
          title: "居右对齐",
        },
        {
          Choice: '.ql-align .ql-picker-item[data-value="justify"]',
          title: "两端对齐",
        },
      ],
    };
  },

  beforeCreate() {},
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.editorform.activityId = activeInfo.activityId;
    this.editorform.activityName = activeInfo.activityName;
    // this.editorform.protocolType = 1;
    setTimeout(() => {
      this.flag2 = false;
    }, 1000);
  },
  mounted() {
    for (let item of this.titleConfig) {
      let tip = document.querySelector(".quill-editor " + item.Choice);
      if (!tip) continue;
      tip.setAttribute("title", item.title);
    }
    this.getOne(1);
  },
  update() {},
  methods: {
    getOne(type) {
      request({
        url: "protocol/getOne/" + this.editorform.activityId + "/" + type,
        method: "get",
      })
        .then((response) => {
          if (response.data) {
            this.editorform.id = response.data.id;
            this.editorform.protocolName = response.data.protocolName;
            this.editorform.protocolContent = response.data.protocolContent;
          }else{
               this.editorform.id=undefined
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    submitForm() {
      this.$refs["editorform"].validate((valid) => {
        if (valid) {
          this.Loading=true
          let url = "";
          if (this.editorform.id && this.editorform.id != "") {
            url = "protocol/update";
          } else {
            url = "protocol/save";
          }
          request({
            url: url,
            method: "post",
            data: this.editorform,
          })
            .then((response) => {
                  this.$modal.msgSuccess("操作成功");
                  this.getOne(this.editorform.protocolType)
                   this.Loading=false
            })
            .catch((err) => {
              console.log(err);
               this.Loading=false
            });
        }
      });
    },
    onEditorChange(e) {
      // console.log(e);
    },
    handleClick(tab) {
      this.activeName = tab.index;
      this.editorform.protocolName = undefined;
      this.editorform.protocolContent = undefined;
      if (tab.index == 0) {
        this.flag2 = true;
        this.editorform.protocolType = 1;
        this.getOne(1);
        setTimeout(() => {
          this.flag2 = false;
        }, 1000);
        
      }
      if (tab.index == 1) {
        this.flag1 = true;
        this.editorform.protocolType = 2;
         this.getOne(2);
        setTimeout(() => {
          this.flag1 = false;
        }, 1000);
       
      }
    },
  },
};
</script>
<style scoped>
.el-tooltip__popper.is-dark {
  min-width: 10px !important;
  max-width: 300px !important;
}
.el-form-item {
  margin-bottom: 10;
}
.editor ::v-deep .ql-container {
  height: 500px;
}
</style>