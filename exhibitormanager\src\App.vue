<template>
  <div id="app" style="min-width: 120px">
    <router-view />
  </div>
</template>

<script>
import {
  getActityMsg,
  getatom,
  access_token,
  contactInfo,
} from "@/utils/apiManage";
import { getAccesToken, setAccesToken } from "@/utils/auth";
import moment from "moment";
import { REDIRECT_URL } from "@/utils/request";
moment.locale("zh-cn");
export default {
  name: "App",
  data() {
    return {
    
    };
  },
  mounted() {
  },
  methods: {
  }
};
</script>

<style>
#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}
body {
  background-color: #f3f8ff !important;
}
.calback {
  margin: 0px 0px 15px 0px;
  text-align: left;
}
a {
  color: #1281ff;
}
</style>
