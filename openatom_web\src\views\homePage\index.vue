<template>
  <div class="app-container">
    <div class="search-bar"></div>
    <div class="operate-bar">
      <el-button type="primary" size="mini" icon="el-icon-plus" plain @click="add">新增</el-button>
    </div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="seq" label="序号" width="60">
      </el-table-column>
      <el-table-column prop="moduleName" label="模块名称" width="180">
      </el-table-column>
      <el-table-column prop="mainTitle" label="主标题">
      </el-table-column>
      <el-table-column prop="subTitle" label="副标题">
      </el-table-column>
      <el-table-column prop="content" label="展示内容">
      </el-table-column>
      <el-table-column prop="address" align=center label="展示内容预览">
        <template slot-scope="{row}">
          <el-button type="primary">预览</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="操作">
        <template slot-scope="{row}">
          <el-button type="primary">修改</el-button>
          <el-button type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :title="dialogType === 'add' ? '新增配置' : '修改配置'" :visible.sync="visible" width="40%">
      <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px">
        <el-form-item label="所属模块" prop="module">
          <el-select v-model="form.module" placeholder="请选择所属模块"></el-select>
        </el-form-item>
        <el-form-item label="所属菜单" prop="menu">
          <el-select v-model="form.menu" placeholder="请选择活动区域">
            <el-option label="区域一" value="shanghai"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主标题">
          <el-input v-model="form.mainTitle" placeholder="请输入主标题"></el-input>
        </el-form-item>
        <el-form-item label="副标题">
          <el-input v-model="form.subTitle" placeholder="请输入副标题"></el-input>
        </el-form-item>
        <el-form-item label="图片">
          <el-upload class="avatar-uploader" action="https://jsonplaceholder.typicode.com/posts/" :show-file-list="false"
            :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
            <el-button type="primary" size="mini" icon="el-icon-upload">上传图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="内容">
          <Editor :min-height="'200'" @input="changeContent"></Editor>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="mini" @click="visible = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
const defaultForm = {
  content: ''
}
export default {
  name: "homepage",
  components: {

  },
  data () {
    return {
      visible: false,
      dialogType: 'add',
      imageUrl: null,
      rules: {
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },

        ],
      },
      form: { ...defaultForm },
      tableData: [{
        seq: 1,
        moduleName: '图片轮播',
        mainTitle: '123',
        subTitle: '456',
        content: '这是内容这是内容这是内容这是内容这是内容这是内容',
        img: 'https://www.ican365.net/homepageTest/static/img/content_4_1.58d4d227.png'
      }]
    };
  },

  beforeCreate () { },
  created () {

  },
  mounted () { },
  update () { },
  methods: {
    add () {
      this.visible = true
    },
    handleAvatarSuccess (res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload (file) {
      const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    changeContent (val) {
      console.log(val);
      this.form.content = val
    },
    confirm () {
      console.log(this.form);
      for (const key in this.form) {
        if (Object.hasOwnProperty.call(object, key)) {
          const element = object[key];

        }
      }
    },
  },
};
</script>
<style lang="scss">
.operate-bar {
  margin-bottom: 4px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
