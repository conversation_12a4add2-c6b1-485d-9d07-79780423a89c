<template>
  <div class="app-container">
    <Add
      v-if="isAdd == true"
      :steps2="steps2"
      @cancelAdd="cancelAdd"
      @getData="getTableData"
    ></Add>
    <div v-else>
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="公司名称" prop="businessCompanyName">
          <el-input
            v-model="queryParams.businessCompanyName"
            placeholder="请输人员姓名"
            clearable
          />
        </el-form-item>
        <el-form-item label="姓名" prop="contactName">
          <el-input
            v-model="queryParams.contactName"
            placeholder="请输人员姓名"
            clearable
          />
        </el-form-item>
        <el-form-item label="手机号" prop="contactPhone">
          <el-input
            v-model="queryParams.contactPhone"
            placeholder="请输人员手机号"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >新增</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        :data="
          tableData.slice(
            (queryParams.pageNum - 1) * queryParams.pageSize,
            queryParams.pageNum * queryParams.pageSize
          )
        "
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="" label="来源" width="100px">
          <template slot-scope="scope">
            {{ scope.row.busPk && scope.row.busPk != "" ? "用户" : "管理员" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="applyUser"
          label="申请人"
          width="100px"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="businessCompanyName"
          label="展商名称"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="businessDesc"
          label="展商简介"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="contactName"
          label="联系人"
          width="100px"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="contactPhone"
          label="电话号码"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="positionNumber"
          label="展位"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="approveStatus"
          label="审核状态"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="createTime"
          label="申请时间"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column label="操作" width="330px">
          <template slot-scope="scope">
            <template v-if="scope.row.approveStatus != '取消资格'">
              <el-button
                v-if="
                  !scope.row.positionNumber || scope.row.positionNumber == ''
                "
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                >分配展位</el-button
              >
              <el-button
                v-if="
                  scope.row.positionNumber && scope.row.positionNumber != ''
                "
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="cancelUpdate(scope.row)"
                >取消展位</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="zhanshangCancel(scope.row)"
                >取消资格</el-button
              >
              <el-button
                v-if="!scope.row.busPk || scope.row.busPk == ''"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="zhanshangUpdate(scope.row)"
                >修改</el-button
              >
            </template>

            <el-button
              size="mini"
              type="text"
              icon="el-icon-search"
              slot="reference"
              @click="handleShow(scope.row)"
              >查看</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              slot="reference"
              style="color: red"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        style="margin-top: 10px"
        align="right"
        v-show="tableData.length > 0"
        :total="tableData.length"
        :current-page.sync="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="10"
        layout="total, sizes, prev, pager,next"
        @size-change="handleSizeChangeSys"
        @current-change="handleCurrentChangeSys"
      />
    </div>

    <el-dialog
      :title="'分配展位'"
      :visible.sync="zhanweiOpen"
      width="600px"
      append-to-body
    >
      <el-form
        ref="zhanweiForm"
        :model="zhanweiForm"
        :rules="rules"
        label-width="150px"
      >
        <el-form-item label="展区展位" prop="positionNumber">
          <el-select v-model="zhanweiForm.positionNumber">
            <el-option
              v-for="(item, idx) in selectList"
              :key="idx"
              :value="item.positionNumber"
              :label="
                item.positionNumber + ' (' + item.positionAreaSize + '㎡)'
              "
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="'查看'"
      :visible.sync="showOpen"
      width="800px"
      append-to-body
      :destroy-on-close="true"
    >
      <div id="self" style="height: 300px; overflow: auto">
        <el-form label-width="100px">
          <el-form-item label="展商名称" prop="businessCompanyName">
            <span>{{ stepsForm.businessCompanyName }}</span>
          </el-form-item>
          <el-form-item label="展商介绍:" prop="businessDesc">
            <span>{{ stepsForm.businessDesc }}</span>
          </el-form-item>
          <el-form-item label="联系人:" prop="contactName">
            <span>{{ stepsForm.contactName }}</span>
          </el-form-item>
          <el-form-item label="联系电话:" prop="contactPhone">
            <span>{{ stepsForm.contactPhone }}</span>
          </el-form-item>
          <el-form-item label="展品:" prop="productDetails" class="content">
            <div
              v-for="(item, idx) in stepsForm.productDetails"
              :key="idx"
              style="display: flex; width: 150px; flex-wrap: wrap; margin: 5px"
            >
              <img :src="item.image_url" style="width: 150px; height: 150px" />
              <span
                :tltle="item.name"
                style="
                  text-overflow: ellipsis;
                  overflow: hidden;
                  display: block;
                  white-space: nowrap;
                "
                >{{ item.name }}</span
              >
            </div>
          </el-form-item>
          <el-form-item label="工作人员:">
            <el-table
              :data="stepsForm.workPerson"
              style="width: 98%"
              size="small"
            >
              <el-table-column label="姓名">
                <template slot-scope="scope">
                  <el-form-item :prop="'workPerson.' + scope.$index + '.name'">
                    <span>{{ scope.row.name }}</span>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="岗位">
                <template slot-scope="scope">
                  <el-form-item
                    :prop="'workPerson.' + scope.$index + '.position'"
                  >
                    <span>{{ scope.row.position }}</span>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="电话">
                <template slot-scope="scope">
                  <el-form-item :prop="'workPerson.' + scope.$index + '.phone'">
                    <span>{{ scope.row.phone }}</span>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="邮箱">
                <template slot-scope="scope">
                  <el-form-item :prop="'workPerson.' + scope.$index + '.email'">
                    <span>{{ scope.row.email }}</span>
                  </el-form-item>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showCancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="'取消资格'"
      :visible.sync="cancelOpen"
      width="600px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-form
        ref="cancelForm"
        :model="cancelForm"
        :rules="cancelFormRules"
        label-width="80px"
      >
        <el-form-item label="取消原因" prop="remarks">
          <el-input
            type="textarea"
            v-model="cancelForm.remarks"
            placeholder="请输入取消原因"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCancelForm">确 定</el-button>
        <el-button @click="closeForm">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import request from "@/utils/request";
import { getToken } from "@/utils/auth";
import Add from "./Add.vue";
export default {
  name: "exhibitorList",
  components: { Add },
  data() {
    return {
      tableData: [],
      activityId: JSON.parse(sessionStorage.getItem("activity")).activityId,
      loading: false,
      isSubmitLoading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: JSON.parse(sessionStorage.getItem("activity")).activityId,
        // approveStatus: "审批通过,取消资格",
      },
      zhanweiOpen: false,
      zhanweiForm: {
        positionNumber: undefined,
      },
      rules: {
        positionNumber: [
          {
            required: true,
            message: "请选择展位",
            trigger: ["blur", "change"],
          },
        ],
      },
      cancelFormRules: {
        remarks: [
          {
            required: true,
            message: "请输入取消原因",
            trigger: ["blur", "change"],
          },
        ],
      },
      selectList: [],
      isAdd: false,
      steps2: "",
      stepsForm: {},
      showOpen: false,
      cancelOpen: false,
      cancelForm: {},
    };
  },
  created() {
    this.getTableData();
  },
  methods: {
    closeForm() {
      this.cancelOpen = false;
      this.cancelForm = {};
    },
    submitCancelForm() {
      this.$refs["cancelForm"].validate((valid) => {
        if (valid) {
          request({
            url: "/display/product/manage/cancel",
            method: "post",
            data: this.cancelForm,
          })
            .then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("操作成功");
                this.closeForm();
                this.getTableData();
              } else {
                this.$message.error("操作失败");
              }
            })
            .catch((err) => {
              console.log(err);
            });
        }
      });
    },
    zhanshangCancel(row) {
      this.cancelOpen = true;
      this.cancelForm.bus_pk = row.busPk;
      this.cancelForm.id = row.id;
      this.cancelForm.userId = JSON.parse(
        localStorage.getItem("userInfo")
      ).login;
    },
    handleShow(row) {
      this.stepsForm = row;
      if (typeof row.workPerson == "string") {
        this.stepsForm.workPerson = JSON.parse(row.workPerson);
      }
      if (typeof row.workPerson == "object") {
        this.stepsForm.workPerson = row.workPerson;
      }
      if (typeof row.productDetails == "string") {
        this.stepsForm.productDetails = JSON.parse(row.productDetails);
      }
      if (typeof row.productDetails == "object") {
        this.stepsForm.productDetails = row.productDetails;
      }
      this.showOpen = true;
    },
    showCancel() {
      this.showOpen = false;
    },
    handleDelete(row) {
      this.$modal
        .confirm(
          '是否删除展商为"' +
            row.businessCompanyName +
            '"的数据项,数据删除无法恢复,请谨慎操作！'
        )
        .then(() => {
          request({
            url: "/display/product/manage/del/" + row.id,
            method: "delete",
          })
            .then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("操作成功");
                this.getTableData();
              } else {
                this.$message.error("操作失败");
              }
            })
            .catch((err) => {
              console.log(err);
            });
        })
        .catch(() => {});
    },
    zhanshangUpdate(row) {
      this.isAdd = true;
      this.steps2 = row;
    },
    cancelAdd() {
      this.steps2 = "";
      this.isAdd = false;
    },
    handleAdd() {
      this.isAdd = true;
      this.steps2 = "";
    },
    cancelUpdate(row) {
      this.$modal
        .confirm("是否取消" + row.businessCompanyName + "的展位？")
        .then(() => {
          request({
            url: "/display/product/manage/position",
            method: "post",
            data: {
              activityId: this.queryParams.activityId,
              id: row.id,
              positionNumber: row.positionNumber,
              displayPosition: {
                status: 0,
                positionNumber: row.positionNumber,
              },
            },
          })
            .then((response) => {
              if (response.code == 200) {
                this.getTableData();
              } else {
                this.$message.error("操作失败");
              }
            })
            .catch((err) => {
              console.log(err);
            });
        })
        .catch(() => {});
    },
    handleUpdate(row) {
      this.zhanweiOpen = true;
      this.zhanweiForm.id = row.id;
      this.getSelect();
    },
    submitForm() {
      this.$refs["zhanweiForm"].validate((valid) => {
        if (valid) {
          request({
            url: "/display/product/manage/position",
            method: "post",
            data: {
              ...this.zhanweiForm,
              activityId: JSON.parse(sessionStorage.getItem("activity"))
                .activityId,
              displayPosition: {
                positionNumber: this.zhanweiForm.positionNumber,
                status: 1,
              },
            },
          })
            .then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("操作成功");
                this.getTableData();
                this.cancel();
              } else {
                this.$message.error("操作失败");
              }
            })
            .catch((err) => {
              console.log(err);
            });
        }
      });
    },
    cancel() {
      this.zhanweiOpen = false;
      this.zhanweiForm = {
        positionNumber: undefined,
        id: undefined,
      };
    },
    getSelect() {
      const data = {
        activityId: this.queryParams.activityId,
        status: "0",
      };
      request({
        url: "/display/position/manage/list",
        method: "get",
        params: data,
      })
        .then((response) => {
          this.selectList = response.data;
          this.loading = false;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleCurrentChangeSys(val) {
      this.queryParams.pageNum = val;
    },
    // 当当前页改变
    handleSizeChangeSys(val) {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = val;
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getTableData();
    },
    getTableData() {
      this.loading = true;
      request({
        url: "/display/product/manage/list",
        method: "get",
        params: this.queryParams,
      })
        .then((response) => {
          this.loading = false;
          if (
            response.code === 200 &&
            response.data &&
            response.data.length > 0
          ) {
            this.tableData = response.data.filter((item) => {
              return (
                (item.approveStatus == "审批通过" ||
                  item.approveStatus == "取消资格") &&
                item.isValid == 1
              );
            });
          } else {
            this.tableData = [];
          }
          // this.loading = false;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    deleteRow(row) {},
  },
};
</script>
<style scoped rel="stylesheet/scss" lang="scss">
.exhibitorList {
  padding: 10px;
}
::v-deep .content .el-form-item__content {
  display: flex;
  flex-wrap: wrap;
}
</style>
  