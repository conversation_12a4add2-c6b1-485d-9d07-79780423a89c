## 开发

```bash
# 安装依赖
node v18.14.0
npm  v9.3.1
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash

# 构建生产环境
npm run build
```

## 项目主要文件介绍

# 项目图片

src-assets

# 公共组件

src-components

# 自定义指令

src-directive

# 布局 layout

src-layout

# 公共 js

src-plugins

# 路由配置

src-router

# 工具包

src-utils

# 开发环境配置

src-utils-baseKey.js

# axios 封装

src-utils-request.js

# 业务代码

src-view（具体参考路由配置）
