<template>
  <div class="app-container">
    <el-form ref="form" :inline="true" :model="form">
      <el-form-item label="姓名">
        <el-input size="small" v-model.trim="form.name" clearable placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="手机号码">
        <el-input size="small" v-model.trim="form.phone" clearable placeholder="请输入电话" />
      </el-form-item>
      <el-form-item label="签到时间段">
        <el-date-picker
          size="small"
          v-model="signInTimes"
          v-trimSpace
          type="daterange"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00','23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item style="margin-left:10px;">
        <el-button size="mini" type="primary" icon="el-icon-search" @click="search(1)">查询</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="signInTable"
      :data="tableData"
      :height="tableHight"
      style="width: 100%"
    >
      <el-table-column
        :resizable="false"
        align="center"
        label="序号"
        type="index"
        width="100%"
        fixed="left"
      />
      <el-table-column :resizable="false" :show-overflow-tooltip="true" prop="name" label="姓名" />
      <el-table-column :resizable="false" :show-overflow-tooltip="true" prop="phone" label="手机号" />
      <el-table-column :resizable="false" :show-overflow-tooltip="true" prop="email" label="邮箱" />
      <el-table-column :resizable="false" :show-overflow-tooltip="true" prop="isSignIn" label="是否签到">
        <template v-slot="scope">
          <span> {{ scope.row.isSignIn === false ? '否' : '是' }}</span>
        </template>
      </el-table-column>
      <el-table-column :resizable="false" :show-overflow-tooltip="true" prop="signInTime" label="签到时间" />
      <el-table-column :resizable="false" :show-overflow-tooltip="true" prop="info" label="备注" />
    </el-table>
    <pagination
      v-if="tableData && tableData.length > 0"
      :page.sync="pageData.pageNum"
      :total="pageData.total"
      :limit.sync="pageData.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      layout="prev, pager, next, jumper"
      @pagination="handleCurrentChange"
    />
  </div>
</template>
<script>
import signIn from '@/api/signIn/index'
export default {
  name: "SignIn",
  components: {
  },
  watch: {
    signInTimes ( cur, _old ) {
      if ( cur ) {
        this.form.startTime = cur[0]
        this.form.endTime = cur[1]
      } else {
        this.form.startTime = ''
        this.form.endTime = ''
      }
      return [this.form.startTime, this.form.endTime]
    }
  },
  data() {
    return {
      form: {
        name: '',
        phone: '',
        startTime: '',
        endTime: ''
      },
      signInTimes: [], //签到日期
      tableData: [],
      tableHight: '534px',
      pageData: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
    };
  },
  mounted () {
    this.search(1)
  },
  methods: {
    init(val) {
      this.tableData = signIn.getSignInList()
    },
    search(val) {
      this.pageData.pageNum = val
      this.init(val)
    },
    resetSearch() {
      this.form = {
        name: '',
        phone: '',
        startTime: '',
        endTime: ''
      }
      this.signInTimes = []
      this.pageData.pageNum = 1
      this.init(1)
    },
    handleCurrentChange(val) {
      this.search(val.page)
    },
  }
};
</script>

<style scoped lang="scss">
.tableBtnGroup {
  margin-bottom: 20px;
}
</style>

