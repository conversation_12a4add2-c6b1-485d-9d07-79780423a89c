import request from '@/utils/request'

// 获取票种设置列表
export function getTicketTypeList(data){
  return request({
    url: `/product/manage/list`,
    method: 'post',
    data
  })
}
// 新增票种
export function addTicketType(data){
  return request({
    url: `/product/manage/add`,
    method: 'post',
    data
  })
}
// 修改票种
export function updateTicketType(data){
  return request({
    url: `/product/manage/update`,
    method: 'post',
    data
  })
}
// 删除票种
export function removeTicketType(id){
  return request({
    url: `/product/manage/remove/${id}`,
    method: 'delete'
  })
}
export default { getTicketTypeList, addTicketType, updateTicketType, removeTicketType }
