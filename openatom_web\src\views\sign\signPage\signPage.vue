<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="设备签到" name="0">
        <autoSign v-if="activeName == 0" :nMPrintSocket="nMPrintSocket" :printSocketOpen="printSocketOpen" :jsonObj="jsonObj" :batchPrintJob="batchPrintJob"></autoSign>
      </el-tab-pane>
      <el-tab-pane label="手动签到" name="1">
        <sign v-if="activeName == 1" :nMPrintSocket="nMPrintSocket" :printSocketOpen="printSocketOpen" :jsonObj="jsonObj" :batchPrintJob="batchPrintJob"></sign>
      </el-tab-pane>
      <el-tab-pane label="待签到信息" name="2">
        <waitingSign v-if="activeName == 2"></waitingSign>
      </el-tab-pane>
      <el-tab-pane label="已签到信息" name="3">
        <hasSigned v-if="activeName == 3"></hasSigned>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import sign from './sign.vue'
import hasSigned from './hasSigned.vue'
import waitingSign from './waitingSign.vue'
import autoSign from "./autoSign.vue"
import Socket from "@/utils/print/Socket";
import NMPrintSocket from "@/utils/print/Print";

export default {
  name: "signPage",
  props: {},
  components: { waitingSign, hasSigned, sign, autoSign },
  data() {
    const jsonObj = {
      printerImageProcessingInfo: {
        printQuantity: 1,
      },
    };
    return {
      activeName: '0',
      nMPrintSocket: null,
      printSocketOpen: false,
      jsonObj: jsonObj,
      label_type: 1,
      print_mode: 1,
      density: 3,
    }
  },
  mounted() {
    // 创建socket实例
    const socketData = new Socket("ws://127.0.0.1:37989");

    socketData.open(
      (openBool) => {
        console.log(openBool, "openBool");
        this.printSocketOpen = openBool;
      },
      (msg) => {
        if (msg.resultAck.callback != undefined) {
          const callbackName = msg.resultAck.callback.name;
          const msgInfo = msg.resultAck.info;
          if (callbackName == "onCoverStatusChange") {
            //盒盖状态：0-闭合、1-打开
            console.log("盒盖状态", msgInfo.capStatus);
          } else if (callbackName == "onElectricityChange") {
            //"power" : 0-4, // 电池电量等级（共5档）
            console.log("电池电量等级", msgInfo.power);
          }
        }
      }
    );
    // 创建打印实例
    this.nMPrintSocket = new NMPrintSocket(socketData);
    console.log(this.nMPrintSocket, "this.nMPrintSocket");
  },
  methods: {
    handleClick() { },
     //更新打印机列表
     async getPrinters() {
      if (!this.printSocketOpen) {
        return console.log("打印服务未开启");
      }
      try {
        const allPrintersRes = await this.nMPrintSocket.getAllPrinters();
        console.log(allPrintersRes, "allPrintersRes");
        if (allPrintersRes.resultAck.errorCode === 0) {
          const allPrinters = JSON.parse(allPrintersRes.resultAck.info);
          this.usbPrinters = { ...allPrinters };
          this.usbSelectPrinter = Object.keys(this.usbPrinters)[0];
          console.log("printers", this.usbPrinters);
          this.selectOnLineUsbPrinter();
        } else {
          this.getPrinters();
        }
      } catch (err) {
        console.error(err);
      }
    },
    // 连接打印机
    async selectOnLineUsbPrinter() {
      if (!this.printSocketOpen) {
        return console.log("打印服务未开启");
      }
      console.log("开始连接打印机");
      try {
        const res = await this.nMPrintSocket.selectPrinter(
          this.usbSelectPrinter,
          parseInt(this.usbPrinters[this.usbSelectPrinter])
        );
        console.log("选择打印机", res);

        if (res.resultAck.errorCode === 0) {
          console.log("连接成功");
          this.onlineUsbBool = true;
          this.init();
        } else {
          this.onlineUsbBool = false;
          console.log("连接失败");
        }
      } catch (err) {
        console.error(err);
      }
    },
    //初始化SDK
    async init() {
      if (!this.printSocketOpen) return console.log("打印服务未开启");
      //初始化数据
      try {
        const res = await this.nMPrintSocket.initSdk({ fontDir: "" });
        if (res.resultAck.errorCode == 0) {
          console.log("初始化成功");
          this.initBool = true;
        } else {
          console.log("初始化失败");
          this.initBool = false;
        }
      } catch (err) {
        console.error(err);
      }
    },
    //批量打印列表数据
    async batchPrintJob(list) {
      const printQuantity =
        this.jsonObj.printerImageProcessingInfo.printQuantity;
      try {
        const startRes = await this?.nMPrintSocket.startJob(
          this.density,
          this.label_type,
          this.print_mode,
          list.length * printQuantity
        );
        if (startRes.resultAck.errorCode == 0) {
          // 提交打印任务
          await this.printTag(list, 0);
        }
      } catch (err) {
        console.error(err);
      }
    },
    // 绘制打印标签
    async printTag(list, x) {
      console.log(list, x);
      //设置画布尺寸
      try {
        const res = await this?.nMPrintSocket.InitDrawingBoard(
          list[x].InitDrawingBoardParam
        );
        if (res.resultAck.errorCode != 0) {
          return;
        }
        // 提交打印任务
        this.printItem(list, x, list[x].elements, 0);
      } catch (err) {
        console.error(err);
      }
    },
    async printItem(list, x, item, i) {
      try {
        if (i < item.length) {
          let arrParse;
          switch (item[i].type) {
            case "text":
              //绘制文本
              arrParse = await this?.nMPrintSocket.DrawLableText(item[i].json);
              if (arrParse.resultAck.errorCode != 0) {
                return;
              }
              i++;
              await this.printItem(list, x, item, i);
              break;
            case "qrCode":
              arrParse = await this?.nMPrintSocket.DrawLableQrCode(item[i].json);
              //绘制二维码
              if (arrParse.resultAck.errorCode !== 0) {
                return;
              }
              i++;
              await this.printItem(list, x, item, i);
              break;
            case "barCode":
              //绘制一维码
              arrParse = await this?.nMPrintSocket.DrawLableBarCode(
                item[i].json
              );
              if (arrParse.resultAck.errorCode !== 0) {
                return;
              }
              i++;
              await this.printItem(list, x, item, i);
              break;
            case "line":
              //绘制线条
              arrParse = await this?.nMPrintSocket.DrawLableLine(item[i].json);
              if (arrParse.resultAck.errorCode !== 0) {
                return;
              }
              i++;
              await this.printItem(list, x, item, i);
              break;
            case "graph":
              //绘制边框
              arrParse = await this?.nMPrintSocket.DrawLableGraph(item[i].json);
              if (arrParse.resultAck.errorCode != 0) {
                return;
              }

              i++;
              await this.printItem(list, x, item, i);
              break;
            case "image":
              //绘制边框
              arrParse = await this?.nMPrintSocket.DrawLableImage(item[i].json);
              if (arrParse.resultAck.errorCode != 0) {
                return;
              }
              i++;
              await this.printItem(list, x, item, i);
              break;
          }
        } else {
          //遍历完成，开始打印
          const commitRes = await this?.nMPrintSocket.commitJob(
            null,
            JSON.stringify(this.jsonObj)
          );
          //回调页码为数据总长度且回调打印份数数据等于当前页需要打印的份数数据时，结束打印任务
          if (
            commitRes.resultAck.printQuantity == list.length &&
            commitRes.resultAck.onPrintPageCompleted ==
            this.jsonObj.printerImageProcessingInfo.printQuantity
          ) {
            //结束打印任务
            const endRes = await this?.nMPrintSocket.endJob();
            if (endRes.resultAck.errorCode === 0) {
              console.log("打印完成");
            }
            return;
          }
          //回调为提交成功，同时数据未发送完成时，可继续提交数据
          if (commitRes.resultAck.errorCode === 0 && x < list.length - 1) {
            //数据提交成功，数据下标+1
            console.log("发送下一页打印数据： ");
            x++;
            await this.printTag(list, x);
          }
        }
      } catch (err) {
        console.error(err);
      }
    },
  },
  watch: {
    printSocketOpen() {
      if (this.printSocketOpen) {
        this.getPrinters();
      }
    },
  },
}
</script>
<style lang='scss'></style>
