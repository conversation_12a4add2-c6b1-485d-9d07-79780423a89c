<template>
  <div class="app-container">
    <el-button
      type="primary"
      plain
      icon="el-icon-plus"
      size="mini"
      @click="handleAdd"
      style="margin-bottom: 10px"
      >新增</el-button
    >

    <el-table :data="tableData" style="width: 100%" v-loading="isloading">
      <el-table-column prop="key" type="index" label="序号"></el-table-column>
      <el-table-column prop="name" label="姓名"></el-table-column>
      <el-table-column prop="phone" label="电话"></el-table-column>
      <el-table-column prop="email" label="邮箱"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="editRow(scope.row)"
            >修改</el-button
          >
          <el-button
            :disabled="isSubmitLoading"
            size="mini"
            type="text"
            icon="el-icon-delete"
            slot="reference"
            @click="deleteRow(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="dialogVisible === 1 ? '新增' : '编辑'"
      :visible="dialogVisible > 0"
      @close="colseDialog"
    >
      <el-form
        :model="formData"
        ref="addUpdateCommit"
        label-width="80px"
        :rules="rules"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="formData.name" placeholder="请输入姓名"></el-input>
        </el-form-item>

        <el-form-item label="电话" prop="phone">
          <el-input
            v-model="formData.phone"
            placeholder="请输入电话"
          ></el-input>
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="formData.email"
            placeholder="请输入邮箱"
          ></el-input>
        </el-form-item>

        <!-- <el-form-item>
          <el-button
            type="primary"
            @click="submitForm('addUpdateCommit')"
            :disabled="isSubmitLoading"
            >提交</el-button
          >
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm('addUpdateCommit')"
          :disabled="isSubmitLoading"
          >确 定</el-button
        >
        <el-button @click="colseDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import request from "@/utils/request";
import { getToken } from "@/utils/auth";
export default {
  name: "committee",
  data() {
    return {
      activityId: JSON.parse(sessionStorage.getItem("activity")).activityId,
      tableData: [],
      selectedRow: null,
      formData: {
        name: null,
        phone: null,
        email: null,
      },
      isloading: false,
      rules: {
        name: [
          { required: true, message: "请输入姓名", trigger: "blur" },
          { min: 2, message: "长度在必须大于一个字符", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "请输入电话", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请填写正确的手机号",
          },
        ],
        email: [
          { required: true, message: "请输入邮箱", trigger: "blur" },
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],
      },
      dialogVisible: 0,
      isSubmitLoading: false,
    };
  },
  created() {},
  mounted() {
    this.getTableData();
  },
  methods: {
    getTableData() {
      let params = { activityId: this.activityId };
      this.isloading = true;
      request({
        url: `/display/oc/manage/list`,
        method: "get",
        params: params,
      })
        .then((response) => {
          this.tableData = response.data;
          this.isloading = false;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleAdd() {
      this.formData = {
        name: null,
        phone: null,
        email: null,
      };
      this.dialogVisible = 1;
    },
    editRow(row) {
      // 编辑行
      let data = { ...row };
      this.selectedRow = data;
      this.formData = data;
      this.dialogVisible = 2;
    },

    deleteRow(row) {
      // 删除行
      this.$modal
        .confirm('是否删除姓名为"' + row.name + '"的数据项？')
        .then(() => {
          this.isSubmitLoading = true;
          request({
            url: "/display/oc/manage/del/" + row.id,
            method: "delete",
          })
            .then((response) => {
              this.isSubmitLoading = false;
              if (response.code === 200) {
                this.$message({
                  message: "删除成功！",
                  type: "success",
                });
                this.getTableData();
              }
            })
            .catch((err) => {
              console.log(err);
            });
        });
    },

    submitForm(formName) {
      // 提交表单
      this.$refs["addUpdateCommit"].validate((valid) => {
        if (valid) {
          let data = {
            activityId: this.activityId,
            name: this.formData.name,
            phone: this.formData.phone,
            email: this.formData.email,
          };
          let url = "/display/oc/manage/add";
          if (this.dialogVisible === 2) {
            //新增
            url = "/display/oc/manage/update";
            data.id = this.selectedRow.id;
          }
          this.isSubmitLoading = true;
          request({
            url: url,
            method: "post",
            data: data,
          })
            .then((response) => {
              this.isSubmitLoading = false;
              // this.loading = false;
              if (response.code === 200) {
                this.$message({
                  message: "保存成功！",
                  type: "success",
                });
                this.dialogVisible = 0;
                this.getTableData();
              }
            })
            .catch((err) => {
              console.log(err);
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    colseDialog() {
      this.dialogVisible = 0;
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss">
</style>
  