<template>
  <div>
    <NavMenu />
    <div v-if="activeBaseMsg && activeBaseMsg.activityStatus === 1" class="registrationNotAllowed"> 活动未开始 </div>
    <div v-else-if="activeBaseMsg && activeBaseMsg.activityStatus === 3" class="registrationNotAllowed"> 活动已结束 </div>
    <div class="registEnter" v-else>
      <div class="signMain">

        <div class="stepsMain" v-if="isBuyTicket === 1">
          <el-steps :active="showStep" align-center  style="padding:30px">
            <el-step title="选票" :description="showStep >1 ? '已完成' :'待选择'"></el-step>
            <el-step title="填写信息/Information" :description="showStep >2 ? '已完成' :'待填写'"></el-step>
            <el-step title="校验" :description="showStep >3 ? '已完成' :'待校验'"></el-step>
            <el-step title="审核" :description="showStep >4 ? '已完成' :'待审核'"></el-step>
            <el-step title="报名成功" :description="showStep >5 ? '已完成' :'待审核'"></el-step>
          </el-steps>
        </div>


        <div>
          <!-- 第一步 -->
          <div v-if="showStep===1">

            <div class="stepTitle">选择票种</div>

            <div class="ticketMain">
                <div v-for="(item, index) in ticketList" :key="index" :offset="index > 0 ? 2 : 0" style="margin:auto;text-align:center">
                  <div v-if="item.stock<=0 || item.isSales === 0" class="ticket_noOpen_bg" style="opacity: 0.5;float:left;width: 50%;">
                    <div style="margin: auto; text-align: center; width: 100%;padding-top:30px;padding-left:50px;">
                      <div class="ticketMsg_left">
                        <img src="@/assets/logo.png" class="image">
                        <div class="ticketName">{{ item.name }}</div>
                        <div style="width:100%">
                          <div class="ticketMsg_left" style="color:#A9B6C8;">
                            <i class="el-icon-warning-outline"></i>查看权益

                          </div>
                          <div style="padding-left:10px;float:right;font-size:22px">
                            {{  item.isSales === 0 ? "暂未开售" : "已售罄" }}
                          </div>
                          <div style="clear:both"></div>
                        </div>
                      </div>
                      <div class="ticketMsg_right">
                        <img src="@/assets/noSelect.png" class="image">
                      </div>
                      <div style="clear:both"></div>

                    </div>
                  </div>

                  <div v-else class="ticket_bg" style="float:left;width: 50%;">
                    <div style="margin: auto; text-align: center; width: 100%;padding-top:30px;padding-left:50px;">
                      <div class="ticketMsg_left">
                        <img src="@/assets/logo.png" class="image">
                        <div class="ticketName">{{ item.name }}</div>
                        <div style="width:100%">
                          <div class="equity_left" style="color:#A9B6C8">
                            <el-tooltip class="item" placement="top-start">
                              <div slot="content" style="max-width: 300px;" v-html="item.info"></div>
                              <div style="cursor: pointer;"><i class="el-icon-warning-outline"></i>查看权益</div>
                            </el-tooltip>
                          </div>
                          <!-- <div style="padding-left:10px;float:right" v-if="item.isActivityPrice === 1">
                            <span style="font-size: 12px; color:#FF8300">¥</span>
                            <span style="font-size: 22px; color:#FF8300">{{ item.activityPrice }}</span>
                            <span style="font-size: 11px;text-decoration:line-through;color:#303133">{{ "&nbsp;原价:"+item.price+"&nbsp;" }}</span>
                          </div>
                          <div style="padding-left:10px;float:right" v-else>
                            <span style="font-size: 12px; color:#FF8300">¥</span>
                            <span style="font-size: 22px; color:#FF8300">{{ item.price }}</span>
                          </div> -->
                          <div style="clear:both"></div>
                        </div>
                      </div>
                      <div class="ticketMsg_right" @click="selectedTicket(item.id)">
                        <img v-if="ticketType === item.id " src="@/assets/selected.png" class="image"/>
                        <img v-else src="@/assets/noSelect.png" class="image"/>
                      </div>
                      <div style="clear:both"></div>
                    </div>
                  </div>

                </div>

                <div style="clear: both;"></div>
            </div>

            <div style="padding: 10px;">
              <el-button type="primary" @click="clickNext">下一步</el-button>
            </div>

          </div>

          <!-- 第二步 -->
          <div v-if="showStep === 2">

            <div class="stepTitle">填写信息/Information</div>

            <div class="editUserMsg" style="min-height: 250px;"  v-loading="isformLoading">
              <el-form :model="regFormData" ref="regFormData" label-width="130px" style="width:800px;margin: auto;">

                <el-form-item
                  label="用户类型"
                  prop="userType"
                  v-if="this.baseFormParam.userType.open"
                  :rules="{required: this.baseFormParam.userType.open, message: '用户类型不能为空'} "
                >
                  <el-select
                    placeholder="请选择类型"
                    v-model="regFormData.userType"
                    class="el-input"
                    >
                      <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"/>
                    </el-select>
                </el-form-item>

                <el-form-item
                  label="姓名/Name"
                  prop="userName"
                  v-if="this.baseFormParam.name.open"
                  :rules="{required: this.baseFormParam.name.required, message: '姓名不能为空'} "
                >
                  <el-input placeholder="请输入姓名/Please enter your name." v-model="regFormData.userName" size="small"/>
                </el-form-item>

                <el-form-item
                  label="性别"
                  prop="userSex"
                  v-if="this.baseFormParam.sex.open"
                  :rules="{required: this.baseFormParam.sex.required, message: '性别不能为空'} "
                >
                  <el-radio-group v-model="regFormData.userSex" size="small">
                    <el-radio label="男" border style="width: 90px;margin-right: 10px;">男</el-radio>
                    <el-radio label="女" border style="width: 90px;">女</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item
                  label="手机号/Phone"
                  prop="userPhone"
                  v-if="this.baseFormParam.phone.open"
                  :rules="[
                    {required: this.baseFormParam.phone.required, message: '手机号不能为空'},
                  ]"
                >
                  <el-input v-model="regFormData.userPhone" placeholder="请输入手机号/Please enter your phone number." size="small"></el-input>
                </el-form-item>

                <el-form-item
                  label="国籍"
                  prop="userNational"
                  v-if="this.baseFormParam.nationality.open"
                  :rules="{required: this.baseFormParam.nationality.required, message: '国籍不能为空'} "
                >
                  <el-radio-group v-model="regFormData.userNational" size="small">
                    <el-radio label="中国" border style="width: 90px;margin-right: 10px;">中国</el-radio>
                    <el-radio label="外国籍" border style="width: 90px;">外国籍</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item
                  label="省份/市"
                  v-if="this.baseFormParam.provinceCity.open"
                  prop="userCity"
                  :rules="{required: this.baseFormParam.provinceCity.required, message: '国籍不能为空'} "
                >
                  <el-cascader
                    style="width: 100%;"
                    placeholder="请选择省份"
                    v-model="regFormData.userCity"
                    size="small"
                    :options="cityOption"/>
                </el-form-item>

                <el-form-item
                  label="证件类型"
                  prop="cardType"
                  v-if="this.baseFormParam.idCardType.open"
                  :rules="{required: this.baseFormParam.idCardType.required, message: '证件类型不能为空'} "
                >
                  <el-select placeholder="请选择证件类型" class="el-input" size="small" v-model="regFormData.cardType">
                    <el-option
                      v-for="item in cardOption"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item
                  v-if='regFormData.cardType == "1" && this.baseFormParam.idCardType.open'
                  label="身份证号"
                  prop="idCard"
                  :rules="[
                    {required: this.baseFormParam.idCardType.required, message: '身份证号不能为空'},
                    {validator:validatorCard,message:'身份证号不正确'}
                  ]"
                >
                  <el-input placeholder="请输入证件号码" size="small" v-model="regFormData.idCard"></el-input>
                </el-form-item>
                <el-form-item
                  v-else-if="this.baseFormParam.idCardType.open"
                  label="证件号码"
                  prop="idCard"
                  :rules="{required: this.baseFormParam.idCardType.required, message: '证件号码不能为空'} "
                >
                  <el-input placeholder="请输入证件号码" size="small" v-model="regFormData.idCard"></el-input>
                </el-form-item>

                <el-form-item
                  label="邮箱地址/Email"
                  prop="email"
                  v-if="this.baseFormParam.email.open"
                  :rules="[
                    {required: this.baseFormParam.email.required, message: '邮箱地址不能为空'},
                    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
                  ] "
                >
                  <el-input placeholder="请输入邮箱地址/Please enter a valid email address." size="small" v-model="regFormData.email"></el-input>
                </el-form-item>

                <el-form-item
                  label="单位名称"
                  prop="company"
                  v-if="this.baseFormParam.company.open"
                  :rules="{required: this.baseFormParam.company.required, message: '单位名称不能为空'}"
                >
                  <!-- <el-input placeholder="请输入单位名称" size="small" v-model="regFormData.company"></el-input> -->
                  <el-autocomplete
                    :style="{ width: '100%' }"
                    size="small"
                    v-model="regFormData.company"
                    :fetch-suggestions="searchCompany"
                    placeholder="请输入单位名称"
                    :trigger-on-focus="false"
                  ></el-autocomplete>
                </el-form-item>

                <el-form-item
                  label="职务"
                  prop="job"
                  v-if="this.baseFormParam.job.open"
                  :rules="{required: this.baseFormParam.job.required, message: '职务不能为空'} "
                >
                  <el-input placeholder="请输入职务" size="small" v-model="regFormData.job"></el-input>
                </el-form-item>

              </el-form>

              <div id="form-create">
                <form-create v-model="fApi" :rule="formInfo" :option="option" :value.sync="value"  style="width:800px;margin: auto;" ></form-create>
              </div>

              <div style="padding-top: 10px; width:800px;margin: auto;" v-if="protocolMsg && protocolMsg.length>0">
                <div>
                  <el-checkbox v-model="isChecked" />
                  <span style="font-size: 12px;color: #808080; cursor: pointer;" @click="isChecked = !isChecked">
                    勾选即代表您已充分理解并同意以下内容/By ticking the box, you are confirming that you have understood and agree to the following content.
                  </span>
                </div>
                <div style="padding-left: 18px;" v-for=" (protocol,index) in protocolMsg">
                  <a  style="color: #409eff; font-size: 12px;" @click="protocolClick(protocol)">{{protocol.protocolName}} </a>
                </div>
              </div>


              <div style="text-align: center;padding: 10px;">
                <el-button type="primary" v-if="isBuyTicket === 1" @click="backTicket">上一步</el-button>
                <el-button type="primary" :disabled="(protocolMsg && protocolMsg.length>0 && !isChecked) || isLoading" @click="submitForm('regFormData')">提交订单/Submit</el-button>
              </div>
            </div>
          </div>
        </div>
          <!-- 第三步 -->
          <div v-if="showStep === 3">
            <div class="stepTitle">校验</div>
            <OrderPay  @cancelPay = "cancelPay" :orderNum="orderNum"  @paySuccess="paySuccess" @callbackOrderExpire="callbackOrderExpire" />
          </div>

          <div v-if="showStep === 4">
            <div class="stepTitle">审核</div>
            <div  class="approveMsg">
              <div style="font-size: 22px;padding: 30px;">校验完成！请等待审核通过...</div>
              <div>
                <el-button type="primary" @click="toUserCenter">跳转个人中心</el-button>
              </div>
            </div>
          </div>

      </div>
    </div>

    <el-dialog
      :title="protocolName"
      :visible.sync="showProtocol"
      width="80%"
      top="30px"
      :before-close="handleClose"
      >
      <div v-html="htmlMsg"></div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showProtocol = false">我已充分理解并同意上述声明/协议(I have fully understood and agree to the above statement/agreement.)</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  formList,
  getUserType,
  getCardType,
  ticketList,
  orderCreate,
  protocolGetList,
  saveNoTicket,
  getLastInfo,
  searchCompanyInfo,
} from '@/utils/apiManage'
import citys from '@/utils/city.js'
import IdentityCodeValid from '@/utils/checkIdent';
import OrderPay from '@/components/OrderPay'
import NavMenu from '@/components/NavMenu/index.vue'

export default {
  name: 'userMsg',
  props: {
  },
  components: { OrderPay,NavMenu },
  data() {
    return {
      activeBaseMsg: localStorage.getItem("activity") ? JSON.parse(localStorage.getItem("activity")) : null,
      showStep:1,
      ticketList:[],
      showPopover:false,
      ticketType:null,
      regFormData:{},
      baseFormParam:{
        userType:{open:false,required:false},
        name:{open:false,required:false},
        phone:{open:false,required:false},
        email:{open:false,required:false},
        sex:{open:false,required:false},
        nationality:{open:false,required:false},
        provinceCity:{open:false,required:false},
        idCardType:{open:false,required:false},
        company:{open:false,required:false},
        job:{open:false,required:false},
      },
      cardOption: [],
      options: {},
      orderNum:null,
      htmlMsg:"",
      showProtocol:false,
      protocolName:"",

      fApi:{},

      formInfo: [],
      option: {
          form:{
            labelWidth: '130px',
          },
          submitBtn:{
            show:false
          },
          row:{
            gutter:0
          }

      },
      value: {},
      isChecked:false,
      protocolMsg:{},
      isLoading:false,

      activeMsg:null,
      userType: '',
      userName: '',
      userSex: null,
      userPhone: null,
      userNational: null,
      userCity: null,
      cardType: null,
      idCard: null,
      email: null,
      cityOption: citys,
      isformLoading:true,
      isBuyTicket:null,


    }
  },

  async mounted() {
    this.getFormList()
    this.getUserType()
    this.getCardType()
    this.protocolGet()
    let activity = JSON.parse(localStorage.getItem("activity"))
    this.isBuyTicket = activity.isBuyTicket
    if(this.isBuyTicket === 1){//购票模式
      this.getTicketList()
    }else{//无票模式
      this.showStep = 2
    }

    const lastInfo = await getLastInfo();

    lastInfo.data.userType && this.$set(this.regFormData, 'userType', lastInfo.data.userType);
    lastInfo.data.name && this.$set(this.regFormData, 'userName',  lastInfo.data.name);
    lastInfo.data.sex && this.$set(this.regFormData, 'userSex', lastInfo.data.userSex);
    lastInfo.data.phone && this.$set(this.regFormData, 'userPhone', lastInfo.data.phone);
    lastInfo.data.nationality && this.$set(this.regFormData, 'userNational', lastInfo.data.nationality);
    lastInfo.data.cardType && this.$set(this.regFormData, 'cardType', lastInfo.data.cardType);
    lastInfo.data.idCard && this.$set(this.regFormData, 'idCard', lastInfo.data.idCard);
    lastInfo.data.email && this.$set(this.regFormData, 'email', lastInfo.data.email);
    lastInfo.data.company && this.$set(this.regFormData, 'company', lastInfo.data.company);
    lastInfo.data.job && this.$set(this.regFormData, 'job', lastInfo.data.job);

    if (lastInfo.data.province && lastInfo.data.city) {
      this.$set(this.regFormData, 'userCity', [lastInfo.data.province, lastInfo.data.city]);
    }
  },
  methods: {
    callbackOrderExpire(){ //订单过期，返回订单列表页面
      localStorage.setItem("showOrder",true)
      this.$router.replace({ path: '/RegistView' })
    },
    cancelPay(){ //取消支付，返回订单列表页面
      this.showStep= 3
      localStorage.setItem("showOrder",true)
      this.$router.replace({ path: '/RegistView'})
    },
    paySuccess(){ //支付成功  设置进度未支付成功等待审批
      this.showStep = 4
    },
    toUserCenter(){
      localStorage.setItem("showOrder",true)
      this.$router.replace({ path: '/RegistView' })
    },
    handleClose() {
      this.showProtocol = false
    },
    getTicketList(){
      this.showLoading = true
      return ticketList(localStorage.getItem("activityNo")).then(res => {
        if(res && res.data){
          this.ticketList = res.data
          this.showLoading = false
        }else{
          this.showLoading = false
          this.$message({
            message:  JSON.stringify(res),
            type: 'warning',
            offset:60,
          });
        }
      })
    },

    canback() {
      localStorage.removeItem('redirectType')
      this.$router.replace({ path: '/' })
    },
    roleChange(e) {
      if (e === 4 || e === 5) {

      }
    },
    getFormList(){
      this.isformLoading= true
      return formList({activityId:localStorage.getItem("activityNo")}).then(res => {
        this.isformLoading= false
        if(res && res.data){
          this.activeMsg = res.data
          res.data.formInfo[0].map((item)=>{
            this.baseFormParam[item.defaultName].open = item.open
            this.baseFormParam[item.defaultName].required = item.required
          })
          this.formInfo = res.data.formInfo[1]

        }else{
          this.$message({
            message:  JSON.stringify(res),
            type: 'warning',
            offset:60,
          });
        }
      })
    },
    getCardType() {
      return getCardType().then(res => {
        this.cardOption = []
        res.data.map(item => {
          this.cardOption.push({
            value: item.dictValue,
            label: item.dictLabel
          })
        })
      })
    },
    getUserType() {
      return getUserType().then(res => {
        this.options = []
        res.data.map(item => {
          this.options.push({
            value: item.dictValue,
            label: item.dictLabel
          })
        })
      })
    },
    protocolGet(){
      return protocolGetList(localStorage.getItem("activityNo")).then(res => {
        if(res && res.data ){
          this.protocolMsg = res.data
        }else{
          this.$message({
            message:  JSON.stringify(res),
            type: 'warning',
            offset:60,
          });
        }

      })
    },
    submitForm(formName) {

      if((this.ticketList && this.ticketList.length>0 && this.ticketList[0].activityid === localStorage.getItem("activityNo")) || this.isBuyTicket === -1){

        this.$refs[formName].validate((valid) => {
          if (valid) {//验证通过
            this.fApi.submit(()=>{
              this.saveData()
            });
          } else {//验证不通过
            document.body.scrollTop=0;
            document.documentElement.scrollTop = 0;
            this.fApi.submit();
            return false;
          }
        });
      }else{
        this.$alert('活动和票种不匹配，请重新提交！','系统提示', {
          confirmButtonText: '确定',
          type: 'warning',
          callback: action => {
            location.reload()
          }
        });
      }
    },
    saveData () {
      let userInfo = JSON.parse(localStorage.getItem("userInfo"))

      if(this.isBuyTicket === 1){//购票模式
        let userMsg ={
          dynamicsJson:this.value,
          userType : this.regFormData.userType,
          name : this.regFormData.userName,
          sex : this.regFormData.userSex,
          phone : this.regFormData.userPhone,
          nationality : this.regFormData.userNational,
          province:this.regFormData.userCity && this.regFormData.userCity.length===2 ?this.regFormData.userCity[0]:null,
          city:this.regFormData.userCity && this.regFormData.userCity.length===2 ?this.regFormData.userCity[1]:null,
          cardType:this.regFormData.cardType,
          idCard:this.regFormData.idCard,
          email:this.regFormData.email,
          company:this.regFormData.company,
          job:this.regFormData.job,
          regFrom:"1",//1.pc 2.手机
          activityId:localStorage.getItem("activityNo"),
          valid:1,
          createBy:userInfo.login,
          channelCode: localStorage.getItem("channelCode") ? localStorage.getItem("channelCode"):null
        }

        let param ={
          orderAmount:null,
          sourceId:1,//1.pc 2.手机
          activityid:localStorage.getItem("activityNo"),
          ordersDetails:[{
            productId:null,
            buyCounts:1,
            regInfoList:[userMsg]
          }]
        }
        let ticktMsg = this.ticketList.filter((val)=>{
          return val.id === this.ticketType
        })
        param.orderAmount = ticktMsg[0].price
        param.ordersDetails[0].productId = ticktMsg[0].id

        this.isLoading = true
        return orderCreate(param).then(res => {
          this.isLoading = false
          if(res.code === 200 && res.data){
            this.orderNum = res.data.orderNum
            this.$message({
              message: '提交订单成功!',
              type: 'success',
              offset:60,
            });
            this.showStep = 3
          }
        })
      }else{//无票模式
        this.isLoading = true
        let userMsg ={
          activityId:localStorage.getItem("activityNo"),
          activityName:localStorage.getItem("activity").activityName,
          dynamicsJson:this.value,
          userType : this.regFormData.userType,
          name : this.regFormData.userName,
          sex : this.regFormData.userSex,
          phone : this.regFormData.userPhone,
          nationality : this.regFormData.userNational,
          province:this.regFormData.userCity && this.regFormData.userCity.length===2 ?this.regFormData.userCity[0]:null,
          city:this.regFormData.userCity && this.regFormData.userCity.length===2 ?this.regFormData.userCity[1]:null,
          cardType:this.regFormData.cardType,
          idCard:this.regFormData.idCard,
          email:this.regFormData.email,
          company:this.regFormData.company,
          job:this.regFormData.job,
          regFrom:"1",//1.pc 2.手机
          valid:1,
          createBy:userInfo.login,
          channelCode: localStorage.getItem("channelCode") ? localStorage.getItem("channelCode"):null
        }
        return saveNoTicket(userMsg).then(res => {
          this.isLoading = false
          if(res.code === 200){
            this.$message({
              message: '报名提交成功!',
              type: 'success',
              offset:60,
            });
            localStorage.setItem("showOrder",false)
            this.$router.replace({ path: '/RegistView'})
          }else{
            this.$message({
              message: JSON.parse(res),
              type: 'warning'
            });
          }
        }).catch(()=>{
          this.isLoading = false
        })
      }

    },
    goPayNow(){
      return orderGoPay({
        "orderNum": this.orderNum
      }).then(res => {

        if(res.code === 200 && res.data){
          this.orderMsg = res.data
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.fApi.resetFields();
    },
    //身份证号校验
    validatorCard (rule, value, callback) {
      let reg = IdentityCodeValid(value);
      if (value && reg == false) {
          callback('请输入正确的身份证号！'); // 身份证号校验未通过
      }
      callback();
    },
    clickNext(){
      if(this.ticketType){
        this.showStep = 2
      }else{
        this.$message({
          message: '请选票！',
          type: 'warning',
          offset:60,
        });
      }
    },
    //显示协议信息
    protocolClick(record){
      this.htmlMsg = record.protocolContent
      this.protocolName = record.protocolName
      this.showProtocol = true
    },

    selectedTicket(id){
      this.ticketType = id
    },
    backTicket(){
      this.showStep = 1
    },

    async searchCompany(queryString, cb) {
      const result = await searchCompanyInfo(queryString);

      if (result.code == 200) {
        cb(result.data);
      }
      else {
        cb([]);
      }
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.el-form-item__label:before {
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}

h3 {
  margin: 40px 0 0;
}

a {
  color: #42b983;
}

.formInfo {
  padding: 50px 300px 50px 300px;
  background-color: #fff;
  width: 600px;
  margin: 0 auto;
  border-radius: px;
  box-shadow: 2px 2px 22px rgba(119, 149, 202, 0.15);
}

.registEnter {
  width: 100%;
  background-color: #F3F8FF;
  margin:auto;
  padding: 0px;
  text-align: center;
  /* display: inline-table; */
}
.signMain{
  /* width: 70%; */
  margin: auto;
  padding: 30px 50px 50px 50px;
  min-width: 1000px;
  max-width: 1200px;
}

.navigation {
  width: 100%;
  height: 64px;
  margin-top: 16px;
  text-align: center;
}

.title {
  width: 100%;
  height: 94px;
  margin-top: 40px;
  text-align: center;
}

.info {
  width: 100%;
  height: 64px;
  margin-top: 70px;

  display: flex;
  margin-bottom: 48px;
}

.info img {
  margin-left: 360px;
}



.stepsMain{
  width: 100%;
  background: #ffffff;
  border-radius: 10px;
}
.stepTitle{
  margin: auto;
  text-align: left;
  padding: 20px 30px;
  font-size: 30px;
  font-weight: bold;
  color: #03081A;
}

.ticket_bg,.ticket_noOpen_bg{
  background-image: url("../assets/ticket_bg.png");
  background-size:100% 180px;
  width:100%;
  height:180px;
  margin:auto


}
.ticket_noOpen_bg{
}
.ticketMsg_left{
  padding:0px 5px;
  min-width: 210px;
  overflow: hidden;
}
.equity_left{
  padding:0px 5px;
  min-width: 100px;
  overflow: hidden;
  text-align:left;
  line-height:25px;
  float:left;
}
.ticketMsg_left,.ticketMsg_right{
  text-align:left;
  line-height:25px;
  float:left;
}
.ticketMsg_right{
  margin:auto;
  text-align:center;
  width:160px;
  padding-top:15px;
  text-align:left;
  line-height:25px;
  float:right;
}
.ticketName{
  font-size:20px;
  font-weight:bold;
  line-height:50px
}
.editUserMsg{
  background-color: #ffffff;
  border-radius: 10px;
  padding: 30px 100px;
  margin: auto;
  text-align: left;
  min-height: 350px;
}
.ticketMain,.editUserMsg,.stepsMain{
  /* min-height: 100px; */
}
.approveMsg{
  background: #ffffff;
  border-radius: 10px;
  padding: 30px;
}
.dialog-footer{
  margin: auto;
  text-align: center;
}
.registrationNotAllowed{
  margin: auto;
  text-align: center;
  line-height: 300px;
  font-size: 30px;
  font-weight: bold;
  background-color: #F3F8FF;
  min-height: 600px;
}
::v-deep .el-input,.el-input--medium,.el-select,.el-select--medium{
  width: 100% !important;
}
::v-deep .el-select,.el-select--medium{
  width: 100% !important;
}

</style>
