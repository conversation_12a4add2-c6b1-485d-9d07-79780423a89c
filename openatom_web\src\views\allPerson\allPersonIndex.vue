<template>
  <div class="app-container">
    <div class="tablePage">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="活动宣传" name="1" />
        <el-tab-pane label="内容配置" name="2" />
        <el-tab-pane label="宣传记录" name="3" />
      </el-tabs>

      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        :label-width="activeName === '3' ? '68px' : '100px'"
        @selection-change="handleSelectionChange"
        v-if="activeName != '2'"
      >
        <!-- <el-form-item label="活动名称" prop="searchParam" >
          <div
            style="
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              width: calc(100% - 100px);
            "
            :title="queryParams.activityName"
          >
            {{ queryParams.activityName }}
          </div>
 
        </el-form-item> -->
        <el-form-item
          label="关键字搜素"
          prop="searchParam"
          v-if="activeName === '1'"
        >
          <el-input
            v-model="queryParams.user"
            placeholder="请输入姓名/手机号/邮箱"
            clearable
          />
        </el-form-item>
        <el-form-item prop="dataSourceType" v-if="activeName === '1'">
          <span slot="label"
            >人员来源
            <el-tooltip class="item" effect="dark" placement="top-start">
              <template slot="content">
                <div>全部：系统历届报名人员和导入用户合集</div>
                <div>历届报名：历届所有报名参与活动人员</div>
                <div>导入用户：管理员导入的潜在目标用户</div>
              </template>
              <i class="el-icon-warning-outline" style="padding-right: 5px"></i>
            </el-tooltip>
          </span>

          <el-select v-model="queryParams.dataSourceType" placeholder="请选择">
            <el-option label="全部" value=""> </el-option>
            <el-option clearable label="历届报名" value="历届报名"> </el-option>
            <el-option clearable label="导入用户" value="导入用户"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="flag" v-if="activeName === '1'">
          <span slot="label"
            >覆盖范围
            <el-tooltip class="item" effect="dark" placement="top-start">
              <template slot="content">
                <div>全部-系统历届报名人员和导入用户合集（涉及重复发送）</div>
                <div>邮件未覆盖人员”-本次活动之前邮件宣传未覆盖到的人员</div>
                <div>短信未覆盖人员”-本次活动之前邮件宣传未覆盖到的人员</div>
              </template>
              <i class="el-icon-warning-outline" style="padding-right: 5px"></i>
            </el-tooltip>
          </span>

          <el-select v-model="queryParams.flag" placeholder="请选择">
            <el-option label="全部" value="0"> </el-option>
            <el-option label="邮件未覆盖" value="1"> </el-option>
            <el-option label="短信未覆盖" value="2"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="工作单位" prop="company" v-if="activeName === '1'">
          <el-input
            v-model="queryParams.company"
            placeholder="请输入工作单位"
            clearable
          />
        </el-form-item>
        <el-form-item label="职务" prop="job" v-if="activeName === '1'">
          <el-input
            v-model="queryParams.job"
            placeholder="请输入职务"
            clearable
          />
        </el-form-item>
        <el-form-item
          label="活动名称"
          prop="activityIds"
          v-if="activeName === '1'"
        >
          <el-select
            v-model="queryParams.activityIds"
            :multiple="false"
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="请输入活动名称搜素"
            :remote-method="remoteMethod"
            :loading="selectloading"
            @change="getTicketListByParam"
          >
            <el-option
              v-for="item in activeOptions"
              :key="item.activityId"
              :label="item.activityName"
              :value="item.activityId"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          label="报名开始日期"
          prop="startTime"
          v-if="activeName === '1'"
        >
          <el-date-picker
            size="small"
            v-model="queryParams.startTime"
            type="datetime"
            placeholder="选择开始日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="getTicketListByParam"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="报名结束日期" v-if="activeName === '1'">
          <el-date-picker
            size="small"
            v-model="queryParams.endTime"
            type="datetime"
            placeholder="选择结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="getTicketListByParam"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="ticketIds" v-if="activeName === '1'">
          <span slot="label"
            >票种信息
            <el-tooltip class="item" effect="dark" placement="top-start">
              <template slot="content">
                <div>默认查询半年的票种信息</div>
              </template>
              <i class="el-icon-warning-outline"></i> </el-tooltip
          ></span>

          <el-select
            v-model="queryParams.ticketIds"
            multiple
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in ticketList"
              clearable
              :key="index"
              :label="item.ticketName"
              :value="item.ticket_type"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="宣传类型"
          prop="searchParam"
          v-if="activeName === '3'"
        >
          <el-radio
            v-model="queryParams.sendType"
            label="1"
            border
            @input="refrashChangeData"
            >邮件</el-radio
          >
          <el-radio
            v-model="queryParams.sendType"
            label="2"
            border
            @input="refrashChangeData"
            >短信</el-radio
          >
        </el-form-item>
        <el-form-item
          label="活动名称"
          prop="activityIds"
          v-if="activeName === '3'"
        >
          <el-select
            v-model="queryParams.activityIds"
            :multiple="false"
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="请输入活动名称搜素"
            :remote-method="remoteMethod"
            :loading="selectloading"
            @change="refrashData(queryParams.activityIds)"
          >
            <el-option
              v-for="item in activeOptions"
              :key="item.activityId"
              :label="item.activityName"
              :value="item.activityId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <div style="border-top: 1px solid #cccccc" v-if="activeName === '3'">
          <!-- <el-form-item label="筛选" style="padding-top: 18px">
            <el-input
              v-model="queryParams.name"
              style="width: 200px"
              placeholder="请输入姓名"
              clearable
            />
            <el-input
              style="width: 200px"
              v-if="queryParams.sendType === '2'"
              v-model="queryParams.phone"
              placeholder="手机号"
              clearable
            />
            <el-input
              style="width: 200px"
              v-if="queryParams.sendType === '1'"
              v-model="queryParams.receiveMailAddress"
              placeholder="邮箱"
              clearable
            />
            <el-select
              v-model="queryParams.status"
              placeholder="发送状态"
              clearable
            >
              <el-option key="0" label="成功" value="success" />
              <el-option key="1" label="失败" value="fail" />
            </el-select>

            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
          </el-form-item> -->
          <el-form-item label="姓名" style="padding-top: 18px">
            <el-input
              v-model="queryParams.name"
              style="width: 200px"
              placeholder="请输入姓名"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="手机"
            style="padding-top: 18px"
            v-if="queryParams.sendType === '2'"
          >
            <el-input
              style="width: 200px"
              v-model="queryParams.phone"
              placeholder="手机号"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="邮箱"
            style="padding-top: 18px"
            v-if="queryParams.sendType === '1'"
          >
            <el-input
              style="width: 200px"
              v-model="queryParams.receiveMailAddress"
              placeholder="邮箱"
              clearable
            />
          </el-form-item>
          <el-form-item label="发送状态" style="padding-top: 18px">
            <el-select
              v-model="queryParams.status"
              placeholder="发送状态"
              clearable
            >
              <el-option key="0" label="成功" value="success" />
              <el-option key="1" label="失败" value="fail" />
            </el-select>
          </el-form-item>
          <el-form-item
            label="回执状态"
            style="padding-top: 18px"
            v-if="queryParams.sendType === '2'"
          >
            <el-select
              v-model="queryParams.statusOfIsp"
              placeholder="回执状态"
              clearable
            >
              <el-option key="0" label="成功" value="success" />
              <el-option key="1" label="失败" value="fail" />
            </el-select>
          </el-form-item>
          <el-form-item style="padding-top: 18px">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
          </el-form-item>
        </div>

        <el-form-item v-if="activeName === '1' || activeName === '2'">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button
            v-if="activeName === '1'"
            size="mini"
            plain
            type="primary"
            @click="userExport"
            style="display: initial; margin-right: 10px"
            >人员导出</el-button
          >
        </el-form-item>
      </el-form>

      <el-row
        style="padding-bottom: 10px; padding-top: 10px"
        v-if="activeName === '1'"
      >
        <el-upload
          class="upload-demo"
          action="https://jsonplaceholder.typicode.com/posts/"
          :file-list="fileList"
          :on-change="uploadFileChange"
          :show-file-list="false"
          :disabled="uploading"
          :beforeUpload="beforeUpload"
          style="display: initial; margin-right: 10px"
          list-type="text"
        >
          <el-button type="primary" size="mini" plain>批量导入</el-button>
          <!-- <div slot="tip" class="el-upload__tip">请选择具体活动后才能操作</div> -->
        </el-upload>
        <el-button
          size="mini"
          plain
          type="primary"
          :disabled="uploading"
          @click="downloadTemplate"
          style="display: initial; margin-right: 10px"
          >模板下载</el-button
        >

        <el-button
          type="primary"
          size="mini"
          plain
          :disabled="selectedEmailAndPhone.length > 0 ? false : true"
          @click="senEmailByOne()"
        >
          发送邮件
        </el-button>
        <el-button
          type="primary"
          size="mini"
          plain
          :disabled="selectedEmailAndPhone.length > 0 ? false : true"
          @click="senMessageByOne()"
        >
          发送短信
        </el-button>
        <el-button
          v-if="queryParams.dataSourceType == '导入用户'"
          :disabled="selectedEmailAndPhone.length > 0 ? false : true"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="deleteUser()"
          >删除</el-button
        >
      </el-row>

      <el-table
        v-if="activeName === '1'"
        v-loading="loading"
        :data="tableData"
        @selection-change="selectionChange"
      >
        <el-table-column
          key="selection"
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          label="序号"
          align="center"
          prop="key"
          key="key"
          :show-overflow-tooltip="true"
          width="50"
        />
        <!-- <el-table-column
          label="活动名称"
          align="center"
          prop=""
          key=""
          :show-overflow-tooltip="true"
        >
          <template>
            {{ queryParams.activityName }}
          </template>
        </el-table-column> -->
        <el-table-column
          label="人员姓名"
          align="center"
          prop="userName"
          key="userName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="票种"
          align="center"
          prop="ticketName"
          key="ticketName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="手机号"
          align="center"
          prop="mobile"
          key="mobile"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="邮箱"
          align="center"
          prop="mail"
          key="mail"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="人员来源"
          align="center"
          prop="dataSourceType"
          key="dataSourceType"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="公司(单位)"
          align="center"
          prop="company"
          key="company"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="职务"
          align="center"
          prop="job"
          key="job"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="报名时间"
          align="center"
          prop="createTime"
          key="createTime"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row }">
            {{
              row.dataSourceType.indexOf("导入") == -1 ? row.createTime : null
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="管理员导入时间"
          align="center"
          prop="createTime1"
          key="createTime1"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row }">
            {{
              row.dataSourceType.indexOf("导入") > -1 ? row.createTime : null
            }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" fixed="right" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              icon="el-icon-search"
              @click="historyInfo(scope.row)"
              >历史报名信息</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-table v-if="activeName === '8'" v-loading="loading" :data="tableData">
        <el-table-column
          label="序号"
          align="center"
          prop="key"
          key="key"
          width="100"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          label="活动编号"
          align="center"
          prop="activityId"
          key="activityId"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          label="活动名称"
          align="center"
          prop="activityName"
          key="activityName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="简介"
          align="center"
          prop="activityDescription"
          key="activityDescription"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.activityDescription }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          key="createTime"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          label="创建人"
          align="center"
          prop="createBy"
          key="createBy"
          :show-overflow-tooltip="true"
        />

        <!-- v-hasPermi="['system:post:edit']" -->
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="300"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="emailConfig(scope.row)"
              >邮件配置</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="messageConfig(scope.row)"
              >短信配置</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <el-table
        v-if="activeName === '3' && queryParams.sendType === '1'"
        v-loading="loading"
        :data="tableData1"
      >
        <el-table-column
          label="序号"
          align="center"
          prop="key"
          key="序号"
          :show-overflow-tooltip="true"
          width="100px"
        />

        <el-table-column
          label="活动"
          align="center"
          prop="activity_name"
          key="activity_name"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          label="人员姓名/账号"
          align="center"
          prop="name"
          key="name"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="邮箱"
          align="center"
          prop="receive_mail_address"
          key="receive_mail_address"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="发送状态"
          align="center"
          prop="status"
          key="status"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="发送时间"
          align="center"
          prop="send_mail_date"
          key="send_mail_date"
          :show-overflow-tooltip="true"
        />
        <!-- <el-table-column
          label="发送人"
          align="center"
          prop="create_by"
          :show-overflow-tooltip="true"
        /> -->
      </el-table>
      <el-table
        v-if="activeName === '3' && queryParams.sendType === '2'"
        v-loading="loading"
        :data="tableData"
      >
        <!-- <el-table-column
          label="序号"
          align="center"
          prop="key"
          key="center"
          :show-overflow-tooltip="true"
        /> -->
        <el-table-column
          label="活动名称"
          align="center"
          prop="activity_name"
          key="activity_name"
          :show-overflow-tooltip="true"
        >
          <!-- <template>
            {{ queryParams.activityName }}
          </template> -->
        </el-table-column>
        <el-table-column
          label="活动"
          align="center"
          prop="activity_id"
          key="activity_id"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          label="人员姓名/账号"
          align="center"
          prop="name"
          key="name"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="手机号码"
          align="center"
          prop="phone"
          key="phone"
          :show-overflow-tooltip="true"
        />
        <!-- <el-table-column
          label="短信内容"
          align="center"
          prop="activityId"
           key="activityId"
          :show-overflow-tooltip="true"
        /> -->
        <el-table-column
          label="发送状态"
          align="center"
          prop="state_code_of_hw"
          key="state_code_of_hw"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span>{{
              scope.row.state_code_of_hw &&
              scope.row.state_code_of_hw === "000000"
                ? "成功"
                : "失败"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="失败原因"
          align="center"
          prop="descChOfHw"
          key="descChOfHw"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row }">
            {{
              row.descChOfHw && row.descChOfHw.indexOf("成功") > -1
                ? "--"
                : row.descChOfHw
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="回执状态"
          align="center"
          prop="state_code_of_isp"
          key="state_code_of_isp"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span>{{
              scope.row.state_code_of_isp &&
              scope.row.state_code_of_isp === "DELIVRD"
                ? "成功"
                : !scope.row.state_code_of_isp
                ? ""
                : "失败"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="回执失败原因"
          align="center"
          prop="descChOfISP"
          key="descChOfISP"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row }">
            {{
              row.descChOfISP && row.descChOfISP.indexOf("成功") > -1
                ? "--"
                : row.descChOfISP
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="发送时间"
          align="center"
          prop="create_time"
          key="create_time"
          :show-overflow-tooltip="true"
        />
        <!-- <el-table-column
          label="发送人"
          align="center"
          prop="create_by"
          :show-overflow-tooltip="true"
        /> -->
      </el-table>
      <pagination
        v-if="activeName != '2' && total > 0"
        :total="total"
        :page-sizes="pageSizes"
        :page-size="10"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="refrashData"
      />
    </div>

    <!-- 邮件内容配置 -->
    <div v-if="activeName === '2'" v-bind:style="{}">
      <el-form ref="flowform" label-width="90px" :inline="true" size="small">
        <!-- <el-form-item label="活动编号:" prop="">
          <div>
            {{ this.activityMsg ? this.activityMsg.activityId : "" }}
          </div>
        </el-form-item> -->
        <!-- <el-form-item label="活动名称:" prop="">
          <div>
            {{ this.activityMsg ? this.activityMsg.activityName : "" }}
          </div>
        </el-form-item> -->
        <el-form-item label="模板类型:" prop="">
          <el-radio v-model="showType" :label="2" border @input="temSendConfig"
            >邮件模板</el-radio
          >
          <el-radio v-model="showType" :label="3" border @input="temSendConfig"
            >短信模板</el-radio
          >
        </el-form-item>
        <el-form-item label="活动名称" prop="activityIds">
          <el-select
            v-model="queryParams.activityIds"
            :multiple="false"
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="请输入活动名称搜素"
            :remote-method="remoteMethod"
            :loading="selectloading"
            @change="
              showType == 2
                ? emailTemplateList(queryParams.activityIds)
                : getMessageTemplate(queryParams.activityIds)
            "
          >
            <el-option
              v-for="item in activeOptions"
              :key="item.activityId"
              :label="item.activityName"
              :value="item.activityId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="活动周期:" prop="">
          <div>
            {{
              this.activityMsg
                ? this.activityMsg.startDate + " - " + this.activityMsg.endDate
                : ""
            }}
          </div>
        </el-form-item> -->
      </el-form>
      <div v-if="!showEditor && showType == 2">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          style="margin: 10px 0"
          >新增邮件模板</el-button
        >

        <el-table
          v-loading="emailTemploading"
          :data="
            emailTempData.slice(
              (tempCurrentPage - 1) * TemPageSize,
              tempCurrentPage * TemPageSize
            )
          "
        >
          <el-table-column
            type="index"
            label="序号"
            align="center"
            :show-overflow-tooltip="true"
            width="50"
          />
          <el-table-column
            label="活动名称"
            align="center"
            prop="activityName"
            key="activityName"
            :show-overflow-tooltip="true"
          >
            <!-- <template>
              {{ this.activityMsg ? this.activityMsg.activityName : "" }}
            </template> -->
          </el-table-column>
          <el-table-column
            label="宣传主题"
            align="center"
            prop="subject"
            key="subject"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="创建人"
            align="center"
            prop="createBy"
            key="createBy"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            key="createTime"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <!-- v-hasPermi="['system:post:edit']" -->
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                >修改</el-button
              >
              <!-- v-hasPermi="['system:post:remove']" -->
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          style="margin-top: 10px"
          v-if="emailTempData.length > 0"
          align="right"
          :current-page.sync="tempCurrentPage"
          :page-sizes="pageSizes"
          :page-size="10"
          layout="total, sizes, prev, pager,next"
          :total="emailTempData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>

      <div v-if="showEditor">
        <el-form
          ref="editorform"
          label-width="90px"
          :rules="editorformRules"
          :model="editorform"
        >
          <el-form-item label="邮件说明:" prop="">
            <div>
              {{ "上传图片累计不得超过5M" }}
            </div>
          </el-form-item>
          <el-form-item label="邮件标题:" prop="emailTitle">
            <el-input
              v-model="editorform.emailTitle"
              placeholder="请输入邮件标题"
              style="width: 300px"
            />
          </el-form-item>
          <el-form-item label="邮件内容:" prop="editContext">
            <quill-editor
              ref="editor"
              v-model="editorform.editContext"
              :options="editorOption"
              class="editor"
              @change="onEditorChange($event)"
            />
          </el-form-item>
        </el-form>

        <el-row style="text-align: center; margin-top: 15px">
          <el-button @click="emailCallbackList">返回</el-button>
          <el-button
            :loading="emailLoading"
            @click="emailConfigSave"
            type="primary"
            >提交</el-button
          >
        </el-row>
      </div>
    </div>

    <!-- 短信内容配置 -->
    <div
      v-if="activeName === '2'"
      v-bind:style="{
        display: showType === 3 ? 'block' : 'none',
      }"
    >
      <el-form ref="flowform" label-width="90px" :inline="false">
        <!-- <el-form-item label="活动编号:" prop="">
          <div>
            {{ this.activityMsg ? this.activityMsg.activityId : "" }}
          </div>
        </el-form-item> -->
        <!-- <el-form-item label="活动名称:" prop="">
          <div>
            {{ this.activityMsg ? this.activityMsg.activityName : "" }}
          </div>
        </el-form-item> -->
        <!-- <el-form-item label="活动周期:" prop="">
          <div>
            {{
              this.activityMsg
                ? this.activityMsg.startDate + " - " + this.activityMsg.endDate
                : ""
            }}
          </div>
        </el-form-item> -->
        <el-form-item
          label="短信说明:"
          prop=""
          v-if="showSmsEditor === 2 || showSmsEditor === 3"
        >
          <div>
            <!-- 短信服务由华为云提供，模板为预设内容，调整文字内容需要现在华为云测申请模板，由后台刷新进系统，（注：调整模板只允许修改文字内同不允许修改入参，否则会导致短信功能异常！） -->
            短信服务由华为云提供，此处模板配置为录入功能，调整内容需要先在华为云测申请模板，由后台刷新进系统，（注：宣传短信仅支持无入参短信模板）
          </div>
        </el-form-item>
        <el-form-item
          label="模板类型:"
          prop=""
          v-if="showSmsEditor === 2 || showSmsEditor === 3"
        >
          会前宣传
          <!-- <el-select v-model="editorSmsform.smsType" placeholder="请选择变量类型">
                <el-option key="1" label="审核通过" value="1"/>
                <el-option key="2" label="审核驳回" value="2"/>
                <el-option key="3" label="会前宣传" value="3"/>
                <el-option key="4" label="会前提醒" value="4"/>
            </el-select> -->
        </el-form-item>
      </el-form>
      <!-- <div>宣传短信内容设置</div>
      <div>
        活动编号：{{ this.activityMsg ? this.activityMsg.activityId : "" }}
      </div>
      <div>
        活动名称：{{ this.activityMsg ? this.activityMsg.activityName : "" }}
      </div>
      <div>
        活动周期：{{
          this.activityMsg
            ? this.activityMsg.startDate + " - " + this.activityMsg.endDate
            : ""
        }}
      </div>
      <div>
        说明：短信服务由华为云提供，模板为预设内容，调整文字内容需要现在华为云测申请模板，由后台刷新进系统，（注：调整模板只允许修改文字内同不允许修改入参，否则会导致短信功能异常！）
      </div> -->
      <div v-if="showSmsEditor === 1">
        <el-button
          @click="showAddSms"
          style="margin-bottom: 10px"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          >录入宣传短信模板</el-button
        >
        <el-table v-loading="loading" :data="messageTableData">
          <el-table-column
            label="序号"
            align="center"
            prop="key"
            width="50"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="活动名称"
            align="center"
            prop="activityName"
            key="activityName"
            :show-overflow-tooltip="true"
          >
            <!-- <template>
              {{ this.activityMsg ? this.activityMsg.activityName : "" }}
            </template> -->
          </el-table-column>
          <el-table-column
            label="模板名称"
            align="center"
            prop="templateName"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            label="模板ID"
            align="center"
            prop="templateId"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="签名"
            align="center"
            prop="signature"
            :show-overflow-tooltip="true"
          />
          <!-- <el-table-column
            label="模板内容"
            align="center"
            prop="sms_content"
            :show-overflow-tooltip="true"
          /> -->
          <!-- <el-table-column
            label="变量说明"
            align="center"
            prop="templateParams"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              {{ JSON.stringify(scope.row.templateParams) }}
            </template>
          </el-table-column> -->
          <el-table-column
            label="示例"
            align="center"
            prop="demoTemp"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            width="300"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="updateSmsConfig(scope.row)"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="deleteSmsConfig(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!-- <el-row style="text-align: center; margin-top: 15px">
          <el-button @click="emailCallback">返回</el-button>
        </el-row> -->
      </div>

      <div v-if="showSmsEditor === 2 || showSmsEditor === 3">
        <el-form
          ref="editorSmsform"
          label-width="90px"
          :rules="editorSmsformRules"
          :model="editorSmsform"
        >
          <el-form-item label="模板名称:" prop="templateName">
            <el-input
              v-model="editorSmsform.templateName"
              placeholder="请输入模板名称"
              style="width: 80%"
            />
          </el-form-item>

          <el-form-item label="模板ID:" prop="templateId">
            <el-input
              v-model="editorSmsform.templateId"
              placeholder="请输入模板ID"
              style="width: 80%"
            />
          </el-form-item>

          <el-form-item label="签名:" prop="signature">
            <el-input
              v-model="editorSmsform.signature"
              placeholder="请输入签名"
              style="width: 80%"
            />
          </el-form-item>
          <el-form-item label="通道号:" prop="sender">
            <el-input
              v-model="editorSmsform.sender"
              placeholder="请输入通道号"
              style="width: 80%"
            />
          </el-form-item>
          <el-form-item label="appKey:" prop="appKey">
            <el-input
              v-model="editorSmsform.appKey"
              placeholder="请输入appKey"
              style="width: 80%"
            />
          </el-form-item>
          <el-form-item label="appSecret:" prop="appSecret">
            <el-input
              v-model="editorSmsform.appSecret"
              placeholder="请输入appSecret"
              style="width: 80%"
            />
          </el-form-item>
          <el-form-item label="短信发送地址:" prop="sendUrl">
            <el-input
              v-model="editorSmsform.sendUrl"
              placeholder="请输入短信发送地址"
              style="width: 80%"
            />
          </el-form-item>
          <el-form-item label="模板内容:" prop="smsContent">
            <el-input
              v-model="editorSmsform.smsContent"
              placeholder="请输入模板内容"
              type="textarea"
              style="width: 80%"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </el-form-item>
          <!-- <el-form-item label="入参设置:" prop="param">
            <div className="paramConfig">
              <el-checkbox v-model="hasParam" />
              <div v-if="hasParam">
                <div
                  v-for="(item, index) in paramList"
                  :key="index"
                  style="margin-bottom: 10px"
                >
                  <el-input
                    v-model="item.paramName"
                    placeholder="变量英文名称"
                    style="width: 80%"
                  />
                  <el-select v-model="item.type" placeholder="请选择变量类型">
                    <el-option
                      v-for="item in paramTypeList"
                      :key="item.id"
                      :label="item.dicName"
                      :value="item.dicValue"
                    >
                    </el-option>
                  </el-select>
                  <el-input
                    v-model="item.description"
                    placeholder="变量中文名称"
                    style="width: 80%"
                  />
                  <i
                    class="el-icon-circle-plus-outline"
                    @click="addRow(index)"
                    style="font-size: 22px; margin: 0px 10px"
                  ></i>
                  <i
                    v-if="paramList.length > 1"
                    class="el-icon-delete"
                    @click="deleteRow(index)"
                    style="font-size: 22px"
                  ></i>
                </div>
              </div>
            </div>
          </el-form-item> -->
          <el-form-item label="示例:" prop="demoTemp">
            <el-input
              v-model="editorSmsform.demoTemp"
              placeholder="请输入示例"
              type="textarea"
              style="width: 80%"
              :autosize="{ minRows: 2, maxRows: 4 }"
            />
          </el-form-item>
          <el-row style="text-align: center; margin-top: 15px">
            <el-button @click="smsCallbackList">返回</el-button>
            <el-button
              :loading="emailLoading"
              @click="smsConfigSave"
              type="primary"
              >提交</el-button
            >
          </el-row>
        </el-form>
      </div>
    </div>
    <el-dialog
      title="发送邮件"
      :visible.sync="emailOpen"
      width="500px"
      append-to-body
      :destroy-on-close="true"
    >
      <div style="margin-bottom: 10px">
        <span>活动名称：</span>
        <el-select
          v-model="queryParams.activityIds"
          :multiple="false"
          filterable
          remote
          reserve-keyword
          clearable
          placeholder="请输入活动名称搜素"
          :remote-method="remoteMethod"
          :loading="selectloading"
          @change="emailTemplateList(queryParams.activityIds)"
        >
          <el-option
            v-for="item in activeOptions"
            :key="item.activityId"
            :label="item.activityName"
            :value="item.activityId"
          >
          </el-option>
        </el-select>
      </div>

      <div style="color: #606266; word-break: break-all; margin-bottom: 10px">
        本次操作设计发送邮件{{ this.selectedEmailAndPhone.length }}人
      </div>
      <div style="color: #606266; word-break: break-all; font-weight: 700">
        请选择邮件模板
      </div>
      <el-table
        v-loading="emailTemploading"
        :data="
          emailTempData.slice(
            (tempCurrentPage - 1) * TemPageSize,
            tempCurrentPage * TemPageSize
          )
        "
      >
        <el-table-column width="60px" key="radio">
          <template v-slot="scope">
            <el-radio
              :key="'radio' + scope.row.id"
              v-model="radioId"
              :label="scope.row.id"
              @change="handleRowChange(scope.row)"
              >{{ "" }}</el-radio
            >
          </template>
        </el-table-column>
        <el-table-column
          type="index"
          label="序号"
          align="center"
          :show-overflow-tooltip="true"
          width="50"
        />
        <el-table-column
          label="活动名称"
          align="center"
          prop="activityName"
          key="activityName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="宣传主题"
          align="center"
          prop="subject"
          key="subject"
          :show-overflow-tooltip="true"
        />
      </el-table>
      <el-pagination
        style="margin-top: 10px"
        v-if="emailTempData.length > 0"
        align="right"
        :current-page.sync="tempCurrentPage"
        :page-sizes="pageSizes"
        :page-size="10"
        layout="total, sizes, prev, pager,next"
        :total="emailTempData.length"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="btnloading"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="发送短信"
      :visible.sync="sysOpen"
      width="500px"
      append-to-body
      :destroy-on-close="true"
    >
      <div style="margin-bottom: 10px">
        <span>活动名称：</span>
        <el-select
          v-model="queryParams.activityIds"
          :multiple="false"
          filterable
          remote
          reserve-keyword
          clearable
          placeholder="请输入活动名称搜素"
          :remote-method="remoteMethod"
          :loading="selectloading"
          @change="getMessageTemplate(queryParams.activityIds)"
        >
          <el-option
            v-for="item in activeOptions"
            :key="item.activityId"
            :label="item.activityName"
            :value="item.activityId"
          >
          </el-option>
        </el-select>
      </div>
      <div style="color: #606266; word-break: break-all; margin-bottom: 10px">
        本次操作设计发送短信{{ this.selectedEmailAndPhone.length }}人
      </div>
      <div style="color: #606266; word-break: break-all; font-weight: 700">
        请选择短信模板
      </div>
      <el-table
        v-loading="sysTemploading"
        :data="
          messageTableData.slice(
            (sysCurrentPage - 1) * sysPageSize,
            sysCurrentPage * sysPageSize
          )
        "
      >
        <el-table-column width="60px" key="radio">
          <template v-slot="scope">
            <el-radio
              :key="'radio' + scope.row.id"
              v-model="radioId"
              :label="scope.row.id"
              @change="handleRowChange(scope.row)"
              >{{ "" }}</el-radio
            >
          </template>
        </el-table-column>
        <el-table-column
          type="index"
          label="序号"
          align="center"
          :show-overflow-tooltip="true"
          width="50"
        />
        <el-table-column
          label="活动名称"
          align="center"
          prop="activityName"
          key="activityName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="模板名称"
          align="center"
          prop="templateName"
          key="templateName"
          :show-overflow-tooltip="true"
        />
      </el-table>
      <el-pagination
        style="margin-top: 10px"
        v-if="messageTableData.length > 0"
        align="right"
        :current-page.sync="sysCurrentPage"
        :page-sizes="pageSizes"
        :page-size="10"
        layout="total, sizes, prev, pager,next"
        :total="messageTableData.length"
        @size-change="handleSizeChangeSys"
        @current-change="handleCurrentChangeSys"
      >
      </el-pagination>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormSys">确 定</el-button>
        <el-button @click="Syscancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="历史报名信息"
      :visible.sync="history"
      width="500px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-table v-loading="historyLoading" :data="historyList" height="300px">
        <el-table-column
          type="index"
          label="序号"
          align="center"
          :show-overflow-tooltip="true"
          width="50"
        />

        <el-table-column
          label="活动名称"
          align="center"
          prop="activity_name"
          key="activity_name"
          :show-overflow-tooltip="true"
        />
      </el-table>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="historyCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
// import {JSONGetParams} from "@/utils/index";
import moment from "moment";
import { quillEditor } from "vue-quill-editor";
moment.locale("zh-cn");
import { getTime } from "@/utils/index";
let configId = null;
export default {
  name: "publicize",
  data() {
    return {
      btnloading: false,
      historyList: [],
      historyLoading: [],
      history: false,
      sysOpen: false,
      sysTemploading: false,
      sysTempData: [],
      showType: 2,
      sysCurrentPage: 1,
      sysPageSize: 10,
      activeName: "1",
      showSmsEditor: 1, //1.短信列表，2.新增，3.修改
      hasParam: false,
      paramList: [
        {
          paramName: null,
          type: null,
          description: null,
          length: null,
        },
      ],
      paramTypeList: [
        {
          description:
            "长度限制：1-15个字符。\r\n可以传入手机号、座机号、95或400、800电话等。",
          dicLength: 15,
          dicName: "电话号码",
          dicValue: "PHONE",
          id: 1,
        },
        {
          description: "长度限制：1-20个字符。",
          dicLength: 20,
          dicName: "其他号码（如验证码、订单号、密码等）",
          dicValue: "CHARDIGIT",
          id: 2,
        },
        {
          description:
            "长度限制：1-20个字符。\r\n\r\n需要符合时间的表达方式，格式示例如下：\r\n\r\n日期：yyyyMMdd、yyyy-MM-dd、yyyy/MM/dd、yyyy年mm月dd日。\r\n时间：HH:mm:ss、HH:mm、HH点mm分、HH点mm。\r\n如果需要同时指定日期和时间，请在模板中填充两个变量，一个变量传入日期，另一个变量传入时间。\r\n\r\n短信内容示例：\r\n温馨提醒：2020-01-10 19:00",
          dicLength: 20,
          dicName: "时间",
          dicValue: "DATETIME",
          id: 3,
        },
        {
          description:
            "长度限制：1-20个字符。\r\n\r\n仅支持传入能够正常表达金额的数字、小数点或中文，例如壹、贰、叁、肆等。\r\n支持传入IP地址，例如：*********。\r\n说明：\r\n￥$等货币符号需要放在模板中，不支持变量传入。",
          dicLength: 20,
          dicName: "金额",
          dicValue: "MONEY",
          id: 4,
        },
        {
          description:
            "长度限制：1-20个字符。\r\n\r\n可以设置为公司/产品/地址/姓名/内容/帐号/会员名等。\r\n不允许出现QQ号/微信号（公众号）/手机号/网址/座机号等联系方式。如果确有需要，请将联系方式放入模板中。\r\n不允许在传入值中携带“.”、“。”、“ ' ”、“<”、“>”、“{”或“}”。否则，可能导致模板变量解析异常。\r\n不允许在传入值中携带“.”，即不支持传入IP地址，如变量取值为IP地址，请申请模",
          dicLength: 20,
          dicName: "其他（如名称、帐号、地址等）",
          dicValue: "TEXT",
          id: 5,
        },
      ],
      loading: false,
      activityLs: [],
      tableData: [],
      tableData1: [],
      total: 0,
      flag1: false,
      flag2: false,
      flag3: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: undefined,
        user: undefined,
        sendType: undefined,
        phone: undefined,
        status: undefined,
        dataSourceType: "",
        flag: "0",
        company: undefined,
        job: undefined,
        startTime: undefined,
        endTime: undefined,
        activityIds: undefined,
      },
      options: [],
      imageUrl: "",
      activityMsg: null,
      messageTableData: [],
      fileList: [],
      imgFile: null,
      uploading: false,
      multipleSelection: [],
      emailTitle: undefined,
      flagUploadImg: false,
      pageSizes: [10, 20, 50, 100],
      selectedEmailAndPhone: [],
      emailOpen: false,
      TemPageSize: 10,
      emailTemploading: false,
      emailTempData: [],
      tempCurrentPage: 1,
      radioId: undefined,
      editorform: {
        editContext: undefined,
        emailTitle: undefined,
      },
      editorSmsform: {},
      // 表单校验
      editorformRules: {
        emailTitle: [
          { required: true, message: "邮件标题不能为空", trigger: "blur" },
        ],
        editContext: [
          { required: true, message: "邮件内容不能为空", trigger: "blur" },
        ],
      },
      // 表单校验
      editorSmsformRules: {
        templateName: [
          { required: true, message: "模板名称不能为空", trigger: "blur" },
        ],
        templateId: [
          { required: true, message: "模板ID不能为空", trigger: "blur" },
        ],
        // smsType: [
        //   { required: true, message: "模板类型不能为空", trigger: "blur" },
        // ],
        signature: [
          { required: true, message: "签名不能为空", trigger: "blur" },
        ],
        sender: [
          { required: true, message: "通道号不能为空", trigger: "blur" },
        ],
        appKey: [
          { required: true, message: "appKey不能为空", trigger: "blur" },
        ],
        appSecret: [
          { required: true, message: "appSecret不能为空", trigger: "blur" },
        ],
        sendUrl: [
          { required: true, message: "短信发送地址不能为空", trigger: "blur" },
        ],
        smsContent: [
          { required: true, message: "模板内容不能为空", trigger: "blur" },
        ],
      },
      seteamilLoading: false,
      tempList: [],
      showEditor: false,
      emailLoading: false,
      editorOption: {
        modules: {
          toolbar: [
            ["bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
            ["blockquote", "code-block"], // 引用  代码块
            [{ header: 1 }, { header: 2 }], // 1、2 级标题
            [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
            [{ script: "sub" }, { script: "super" }], // 上标/下标
            [{ indent: "-1" }, { indent: "+1" }], // 缩进
            [{ direction: "rtl" }], // 文本方向
            [
              {
                size: ["small", false, "large", "huge"],
              },
            ], // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6] }], // 标题
            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
            // [{ font: ['songti'] }], // 字体种类
            [{ align: [] }], // 对齐方式
            ["clean"], // 清除文本格式
            // ["image", "video"], // 链接、图片、视频
            ["image"], // 链接、图片、视频
          ],
        },
        placeholder: "请输入正文",
      },

      titleConfig: [
        { Choice: ".ql-insertMetric", title: "跳转配置" },
        { Choice: ".ql-bold", title: "加粗" },
        { Choice: ".ql-italic", title: "斜体" },
        { Choice: ".ql-underline", title: "下划线" },
        { Choice: ".ql-header", title: "段落格式" },
        { Choice: ".ql-strike", title: "删除线" },
        { Choice: ".ql-blockquote", title: "块引用" },
        { Choice: ".ql-code", title: "插入代码" },
        { Choice: ".ql-code-block", title: "插入代码段" },
        { Choice: ".ql-font", title: "字体" },
        { Choice: ".ql-size", title: "字体大小" },
        { Choice: '.ql-list[value="ordered"]', title: "编号列表" },
        { Choice: '.ql-list[value="bullet"]', title: "项目列表" },
        { Choice: ".ql-direction", title: "文本方向" },
        { Choice: '.ql-header[value="1"]', title: "h1" },
        { Choice: '.ql-header[value="2"]', title: "h2" },
        { Choice: ".ql-align", title: "对齐方式" },
        { Choice: ".ql-color", title: "字体颜色" },
        { Choice: ".ql-background", title: "背景颜色" },
        { Choice: ".ql-image", title: "图像" },
        { Choice: ".ql-video", title: "视频" },
        { Choice: ".ql-link", title: "添加链接" },
        { Choice: ".ql-formula", title: "插入公式" },
        { Choice: ".ql-clean", title: "清除字体格式" },
        { Choice: '.ql-script[value="sub"]', title: "下标" },
        { Choice: '.ql-script[value="super"]', title: "上标" },
        { Choice: '.ql-indent[value="-1"]', title: "向左缩进" },
        { Choice: '.ql-indent[value="+1"]', title: "向右缩进" },
        { Choice: ".ql-header .ql-picker-label", title: "标题大小" },
        {
          Choice: '.ql-header .ql-picker-item[data-value="1"]',
          title: "标题一",
        },
        {
          Choice: '.ql-header .ql-picker-item[data-value="2"]',
          title: "标题二",
        },
        {
          Choice: '.ql-header .ql-picker-item[data-value="3"]',
          title: "标题三",
        },
        {
          Choice: '.ql-header .ql-picker-item[data-value="4"]',
          title: "标题四",
        },
        {
          Choice: '.ql-header .ql-picker-item[data-value="5"]',
          title: "标题五",
        },
        {
          Choice: '.ql-header .ql-picker-item[data-value="6"]',
          title: "标题六",
        },
        { Choice: ".ql-header .ql-picker-item:last-child", title: "标准" },
        {
          Choice: '.ql-size .ql-picker-item[data-value="small"]',
          title: "小号",
        },
        {
          Choice: '.ql-size .ql-picker-item[data-value="large"]',
          title: "大号",
        },
        {
          Choice: '.ql-size .ql-picker-item[data-value="huge"]',
          title: "超大号",
        },
        { Choice: ".ql-size .ql-picker-item:nth-child(2)", title: "标准" },
        { Choice: ".ql-align .ql-picker-item:first-child", title: "居左对齐" },
        {
          Choice: '.ql-align .ql-picker-item[data-value="center"]',
          title: "居中对齐",
        },
        {
          Choice: '.ql-align .ql-picker-item[data-value="right"]',
          title: "居右对齐",
        },
        {
          Choice: '.ql-align .ql-picker-item[data-value="justify"]',
          title: "两端对齐",
        },
      ],
      ticketList: [],
      selectloading: false,
      activeOptions: [],
      emailSysActiviId: undefined,
    };
  },
  watch: {
    "queryParams.activityId": {
      handler(val, oldval) {
        if (val != oldval) {
          //   this.queryParams.activityId = val;
          this.refrashData();
        }
      },
    },
    "queryParams.dataSourceType": {
      handler(val, oldval) {
        if (val != oldval) {
          this.refrashData();
        }
      },
    },
  },
  components: {
    quillEditor,
  },
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    (this.queryParams.startTime = moment()
      .subtract(6, "months")
      .format("yyyy-MM-DD")),
      (this.queryParams.endTime = moment().subtract().format("yyyy-MM-DD"));
  },
  mounted() {
    // this.activityList();
    this.getTicketListByParam();

    // this.getActiveList()
  },
  methods: {
    remoteMethod(query) {
      this.selectloading = true;
      if (query && query != "") {
        let obj = {
          pageNum: 1,
          pageSize: 10000,
          activityStatus: "2,3",
          searchParam: query,
        };
        let param = JSON.parse(JSON.stringify(obj));
        let formData = new window.FormData();
        Object.keys(param).map((item) => {
          if (param[item]) {
            formData.append(item, param[item]);
          }
        });
        request({
          url: "/activityManage/list",
          method: "post",
          data: formData,
        })
          .then((response) => {
            if (response.rows && response.rows.length > 0) {
              this.activeOptions = response.rows;
              this.selectloading = false;
            } else {
              this.activeOptions = [];
              this.selectloading = false;
            }
          })
          .catch((err) => {
            this.selectloading = false;
            console.log(err);
          });
      }
    },
    historyInfo(row) {
      this.history = true;
      this.getUserActivityList(row);
    },
    historyCancel() {
      this.history = false;
      this.historyList = [];
    },
    getUserActivityList(row) {
      this.historyLoading = true;
      request({
        url: "/activityPromotion/getUserActivityList",
        method: "post",
        data: {
          phone: row.mobile,
        },
      })
        .then((response) => {
          this.historyList = response.rows;
          this.historyLoading = false;
        })
        .catch((err) => {
          console.log(err);
          this.historyLoading = false;
        });
    },
    // ticketList
    getTicketListByParam() {
      request({
        url: "/activityPromotion/getTicketListByParam",
        method: "post",
        data: {
          startTime: this.queryParams.startTime,
          endTime: this.queryParams.endTime,
          activityIds: this.queryParams.activityIds,
        },
      })
        .then((response) => {
          this.queryParams.ticketIds = "";
          this.ticketList = response.rows;
          this.refrashData();
        })
        .catch((err) => {
          console.log(err);
        });
    },
    temSendConfig(val) {
      this.showType = val;
      this.showEditor = false;
      this.showSmsEditor = 1;
      this.queryParams.activityIds = null;
      if (val == 3) {
        this.getMessageTemplate();
      }
      if (val == 2) {
        this.emailTemplateList();
      }
    },
    onEditorChange(e) {
      // console.log(e);
    },
    handleClick(tab) {
      this.queryParams.activityIds = undefined;
      if (this.activeName === "1" || this.activeName === "2") {
        this.queryParams.sendType = null;
        if (this.activeName == 2) {
          this.flagUploadImg = false;
          let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
          this.emailTemplateList();
        }
      } else {
        if (this.activeName == 3) {
        }
        this.queryParams.sendType = "1";
      }
      this.selectedEmailAndPhone = [];
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.refrashData();
    },
    refrashChangeData() {
      this.queryParams.activityIds = undefined;
      this.refrashData();
    },
    refrashData() {
      if (this.activeName === "1") {
        this.activityPrlist("/activityPromotion/userList");
      } else if (this.activeName === "2") {
        this.activityPrlist("/activityManage/list");
      } else if (this.activeName === "3") {
        if (this.queryParams.sendType === "1") {
          //邮件
          this.activityPrlist("/sms/emailRecordList");
        } else {
          this.activityPrlist("/sms/smsRecordList");
        }
      }
    },
    activityPrlist(path) {
      let param = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        activityId: this.queryParams.activityIds,
        searchParam: null,
        dataSourceType: null,
        company: null,
        job: null,
      };
      if (this.activeName === "1") {
        //活动宣传
        param.flag = this.queryParams.flag;
        param.searchParam = this.queryParams.user;
        param.dataSourceType = this.queryParams.dataSourceType;
        param.company = this.queryParams.company;
        param.job = this.queryParams.job;
        param.ticketIds = this.queryParams.ticketIds.toString();
        param.startTime = this.queryParams.startTime;
        param.endTime = this.queryParams.endTime;
        // param.activityIds=this.queryParams.activityIds?this.queryParams.activityIds.join(","):undefined
        param.activityIds = this.queryParams.activityIds;
        this.getTableData(param, path);
      } else if (this.activeName === "3") {
        //宣传记录
        param.name = this.queryParams.user;
        if (this.queryParams.sendType === "1") {
          param.email = this.queryParams.phone;
        } else {
          param.phone = this.queryParams.phone;
        }
        param.status = this.queryParams.status;
        this.getTableDataByEmail(path);
      } else {
        this.getTableData(param, path);
      }
    },
    getTableData(param, path) {
      this.tableData = [];
      this.total = 0;
      this.loading = true;
      let formData = new window.FormData();
      Object.keys(param).map((item) => {
        if (param[item]) {
          formData.append(item, param[item]);
        }
      });
      request({
        url: path,
        method: "post",
        data: formData,
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            response.rows.map((item, index) => {
              return (item.key = index + 1);
            });
            this.tableData = response.rows;
            this.total = response.total;
            this.pageSizes =
              this.total < 100 ? [10, 20, 50, 100] : [10, 50, 100, 500, 1000];
            // this.total < 100 ? [10, 20, 50, 100] : [10, 50, 100, this.total];
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          console.log(err);
        });
    },
    getTableDataByEmail(path, param) {
      this.total = 0;
      if (this.queryParams.sendType == 1) {
        this.tableData1 = [];
      } else {
        this.tableData = [];
      }
      this.loading = true;
      // console.log(param)
      let formData = new window.FormData();
      Object.keys(this.queryParams).map((item) => {
        if (this.queryParams[item]) {
          formData.append(item, this.queryParams[item]);
        }
      });
      if (this.queryParams.sendType === "1") {
        formData.append("emailType", 3);
      } else {
        formData.append("smsType", 3);
      }
      request({
        url: path,
        method: "post",
        //data: this.queryParams,
        data: formData,
      })
        .then((response) => {
          if (response && response.rows && response.rows.length > 0) {
            response.rows.map((item, index) => {
              return (item.key = index + 1);
            });
            if (this.queryParams.sendType == 1) {
              this.tableData1 = response.rows;
            } else {
              this.tableData = response.rows;
            }

            this.total = response.total;
            this.$forceUpdate();
          } else {
            this.tableData1 = [];
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          console.log(err);
        });
    },
    activityList() {
      this.loading = true;
      request({
        url: "/activityManage/list",
        method: "post",
        data: {
          pageNum: 1,
          pageSize: 10000,
        },
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            response.rows.map((item, index) => {
              return (item.key = index + 1);
            });
            this.activityLs = response.rows;
            //  this.queryParams.activityId = response.rows[0].activityId;
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          console.log(err);
        });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.refrashData();
    },
    emailConfig(record) {
      this.showType = 2;
      this.flagUploadImg = false;
      this.activityMsg = JSON.parse(JSON.stringify(record));
      this.activityMsg.startDate = record.startDate;
      //  moment(record.startDate).format(
      //   "ll"
      // );
      this.activityMsg.endDate = record.endDate;
      this.emailTemplateList();
      // this.editorform=record
      // console.log(this.editorform)
      // moment(record.endDate).format("ll");
      // this.emailTitle = this.activityMsg.activityName
      // this.getEmailMsg();
    },
    handleAdd() {
      if (this.queryParams.activityIds && this.queryParams.activityIds != "") {
        let obj = { activityId: this.queryParams.activityIds };
        this.activityMsg = obj;
        this.showEditor = true;
        this.$nextTick(() => {
          for (let item of this.titleConfig) {
            let tip = document.querySelector(".quill-editor " + item.Choice);
            if (!tip) continue;
            tip.setAttribute("title", item.title);
          }
        });
      } else {
        this.activityMsg = null;
        this.$message.error("请选择活动后在进行操作");
      }
    },
    handleUpdate(row) {
      //   setTimeout(() => {
      //     this.showEditor = true;
      //     this.$nextTick(() => {
      //       for (let item of this.titleConfig) {
      //         let tip = document.querySelector(".quill-editor " + item.Choice);
      //         if (!tip) continue;
      //         tip.setAttribute("title", item.title);
      //       }
      //     });
      //   }, 1000);
      this.activityMsg = row;
      // this.editorform=row
      this.editorform.emailTitle = row.subject;
      // this.editorform.editContext = row.content;
      this.editorform.id = row.id;
      this.getPromotionInfo(row);
    },
    getPromotionInfo(row) {
      let param = {
        activityId: row.activityId,
        id: row.id,
      };
      request({
        url: "/activityPromotion/getPromotionInfo",
        method: "post",
        data: param,
        timeout: 60000,
      })
        .then((response) => {
          if (response && response.data && response.data.content) {
            this.showEditor = true;
            this.editorform.editContext = response.data.content;
          } else {
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    emailCallbackList() {
      this.showEditor = false;
      this.editorform.emailTitle = undefined;
      this.editorform.editContext = undefined;
      this.editorform.id = undefined;
    },
    smsCallbackList() {
      this.showSmsEditor = 1;
      this.activityMsg = null;
    },
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除主题为"' + row.subject + '"的邮件模板？')
        .then(() => {
          request({
            url: "/activityPromotion/saveInfo",
            method: "post",
            data: { id: row.id, isValidate: 0 },
          })
            .then((response) => {
              this.$modal.msgSuccess("删除成功");
              this.emailTemplateList(
                this.queryParams.activityIds
                  ? this.queryParams.activityIds
                  : null
              );
            })
            .catch((err) => {
              console.log(err);
            });
        });
    },
    getEmailMsg() {
      configId = null;
      let param = {
        activityId: this.activityMsg.activityId,
      };
      request({
        url: "/activityPromotion/getPromotionInfo",
        method: "post",
        data: param,
        timeout: 60000,
      })
        .then((response) => {
          if (response && response.data && response.data.content) {
            this.imageUrl = response.data.content;
            this.emailTitle = response.data.subject;
            configId = response.data.id;
            this.editorform.emailTitle = this.activityMsg.activityName;
          } else {
            this.imageUrl = null;
            this.emailTitle = this.activityMsg.activityName;
            this.editorform.emailTitle = this.activityMsg.activityName;
            configId = null;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    messageConfig(record) {
      // record.startDate = moment(record.startDate).format("ll");
      // record.endDate = moment(record.endDate).format("ll");
      this.showType = 3;
      this.activityMsg = record;
      this.getMessageTemplate();
    },
    getMessageTemplate(activityId) {
      request({
        url: "/sms_config/selectSmsConfig/",
        method: "post",
        data: {
          activityId: !activityId
            ? this.queryParams.activityId
              ? this.queryParams.activityId
              : null
            : activityId,
          smsType: 3,
          valid: 1,
        },
      })
        .then((response) => {
          if (response.data && response.data.length > 0) {
            response.data.map((item, index) => {
              return (item.key = index + 1);
            });

            this.messageTableData = response.data;
            this.radioId = undefined;
          } else {
            this.radioId = undefined;
            this.messageTableData = [];
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          console.log(err);
        });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.tableId);
      this.tableNames = selection.map((item) => item.tableName);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    selectionChange(selection) {
      this.selectedEmailAndPhone = selection;
    },
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) {
      this.flagUploadImg = true;
      this.imageUrl = URL.createObjectURL(file);

      const isJPG = file.type === "image/jpeg" || file.type === "image/png";
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG 格式!");
        this.imgFile = null;
      } else if (!isLt2M) {
        this.imgFile = null;
        this.$message.error("上传头像图片大小不能超过 2MB!");
      } else {
        this.imgFile = file;
      }
      // return isJPG && isLt2M;
      return false;
    },
    emailCallback() {
      this.showType = 1;
      this.activityMsg = null;
    },
    smsConfigSave() {
      this.$refs["editorSmsform"].validate((valid) => {
        if (valid) {
          this.emailLoading = true;
          this.editorSmsform.activityId = this.activityMsg.activityId;
          this.editorSmsform.smsType = 3;
          this.editorSmsform.templateParams = this.paramList;

          request({
            url:
              this.showSmsEditor === 2
                ? "/sms_config/saveSmsConfig"
                : "/sms_config/updateSmsConfig",
            method: "post",
            data: this.editorSmsform,
          })
            .then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.showSmsEditor = 1;
              this.activityMsg = null;
              this.getMessageTemplate(
                this.queryParams.activityIds
                  ? this.queryParams.activityIds
                  : null
              );
              this.emailLoading = false;
            })
            .catch((err) => {
              console.log(err);
              this.emailLoading = false;
            });
        }
      });
    },
    emailConfigSave() {
      this.$refs["editorform"].validate((valid) => {
        if (valid) {
          this.emailLoading = true;
          const requestSize = Object.keys(
            JSON.stringify(this.editorform.editContext)
          ).length; // 请求数据大小
          const limitSize = 5 * 1024 * 1024; // 限制存放数据5M
          if (requestSize >= limitSize) {
            this.$alert("上传图片大小不得超过5M", "系统提示", {
              confirmButtonText: "确定",
              type: "warning",
            });
            this.emailLoading = false;
          } else {
            this.editorform.activityId = this.activityMsg.activityId;
            this.editorform.subject = this.editorform.emailTitle;
            this.editorform.content = this.editorform.editContext;

            request({
              url: "/activityPromotion/saveInfo",
              method: "post",
              data: this.editorform,
            })
              .then((response) => {
                this.$modal.msgSuccess("操作成功");
                this.showEditor = false;
                // this.emailLoading = false;
                this.activityMsg = null;
                this.emailTemplateList(
                  this.queryParams.activityIds
                    ? this.queryParams.activityIds
                    : null
                );
                this.emailLoading = false;
              })
              .catch((err) => {
                if (err.indexOf("Too many properties to enumerate") > -1) {
                  this.$alert("上传图片大小不得超过5M", "系统提示", {
                    confirmButtonText: "确定",
                    type: "warning",
                  });
                }
                this.emailLoading = false;
                // this.emailLoading = false;
                console.log(err);
              });
          }
        }
      });
    },
    beforeUpload() {
      if (this.queryParams.activityIds && this.queryParams.activityIds != "") {
        return true;
      } else {
        this.$message.error("请选择具体活动后再进行操作");
        return false;
      }
    },
    uploadFileChange(file, fileList) {
      if (this.queryParams.activityIds && this.queryParams.activityIds != "") {
        this.uploading = true;
        let formData = new FormData();
        formData.append("file", file.raw);
        formData.append("activityId", this.queryParams.activityIds);
        request({
          url: "/activityPromotion/uploadUser",
          method: "post",
          data: formData,
        })
          .then((response) => {
            this.$message.success("操作成功");
            this.uploading = false;
            this.refrashData();
          })
          .catch((err) => {
            this.uploading = false;
            console.log(err);
          });
      } else {
        this.uploading = false;
      }
    },
    userExport() {
      let obj = { ...this.queryParams };
      delete obj.pageNum;
      delete obj.pageSize;
      this.download(
        "/activityPromotion/userexport",
        {
          ...obj,
        },
        `人员数据_${new Date().getTime()}.xlsx`
      );
    },
    downloadTemplate() {
      this.uploading = true;
      request({
        url: "/activityPromotion/downLoadUserTemp",
        method: "get",
        responseType: "blob",
      })
        .then((response) => {
          this.uploading = false;
          if (response.size) {
            const blob = response;
            const objectURL = URL.createObjectURL(
              new Blob([blob], { type: "text/xls" })
            ); // chrome不受文件你大小限制导出文件
            let a = document.createElement("a");
            a.download = `模板.xlsx`;
            a.href = objectURL;
            a.click();
            this.$message.success("操作成功");
          }
        })
        .catch((err) => {
          this.uploading = false;
          console.log(err);
        });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    sendMessage(type) {
      let param = {
        activityId: this.queryParams.activityIds,
        flag: type,
      };
      this.sendSms(param);
    },
    sendSms(param) {
      param.flag = Number(param.flag);
      request({
        url: "/activityPromotion/sendSms",
        method: "post",
        data: param,
      })
        .then((response) => {
          this.$message.success("操作成功");
          this.uploading = false;
          this.Syscancel();
          this.emailSysActiviId = undefined;
          // this.refrashData()
        })
        .catch((err) => {
          this.uploading = false;
          console.log(err);
        });
    },
    Syscancel() {
      this.sysOpen = false;
      this.messageTableData = [];
      this.radioId = undefined;
      this.queryParams.activityIds = undefined;
    },
    senMessageByOne(record) {
      this.sysOpen = true;
      this.getMessageTemplate();
    },
    emailTemplateList(activityId) {
      this.emailTemploading = true;
      request({
        url: "/activityPromotion/emailTemplateList",
        method: "post",
        data: {
          activityId: !activityId
            ? this.queryParams.activityId
              ? this.queryParams.activityId
              : null
            : activityId,
        },
      })
        .then((response) => {
          this.emailTemploading = false;
          this.emailTempData = response.rows;
          this.radioId = undefined;
          this.pageSizes =
            response.rows.length < 100
              ? [10, 20, 50, 100]
              : [10, 50, 100, 500, 1000];
        })
        .catch((err) => {
          this.emailTemploading = false;
          console.log(err);
        });
    },
    handleRowChange(data) {
      if (data) {
        this.radioId = data.id;
        this.emailSysActiviId = data.activityId;
      } else {
        this.radioId = undefined;
        this.emailSysActiviId = undefined;
      }
    },

    //
    handleCurrentChange(val) {
      this.tempPageNum = val;
    },
    // 当当前页改变
    handleSizeChange(val) {
      this.tempPageNum = 1;
      this.TemPageSize = val;
    },
    handleCurrentChangeSys(val) {
      this.tempPageNum = val;
    },
    // 当当前页改变
    handleSizeChangeSys(val) {
      this.tempPageNum = 1;
      this.sysPageSize = val;
    },
    senEmailByOne(record) {
      this.emailOpen = true;
      this.emailTemplateList();
    },
    submitFormSys() {
      if (this.radioId) {
        this.btnloading = true;
        let mobile = "";
        if (this.selectedEmailAndPhone.length > 0) {
          this.selectedEmailAndPhone.map((item) => {
            mobile += item.mobile + "$#$" + item.userName + ",";
          });
        }
        let param = {
          activityId: this.emailSysActiviId,
          phone: mobile,
          flag: 3,
          smsConfigId: this.radioId,
        };
        this.sendSms(param);
        this.btnloading = false;
      } else {
        this.$message.error("请选择模板");
      }
    },
    submitForm() {
      if (this.radioId) {
        this.btnloading = true;
        let email = "";
        if (this.selectedEmailAndPhone.length > 0) {
          this.selectedEmailAndPhone.map((item) => {
            email += item.mail + "$#$" + item.userName + ",";
          });
        }
        let param = {
          activityId: this.emailSysActiviId,
          receiveMailAddressString: email,
          flag: 3,
          id: this.radioId,
        };
        this.sendE(param);
        this.btnloading = false;
      } else {
        this.$message.error("请选择模板");
      }
    },
    cancel() {
      this.emailOpen = false;
      this.radioId = undefined;
      this.activityMsg = null;
      this.queryParams.activityIds = undefined;
    },
    sendEmail(type) {
      let param = {
        activityId: this.queryParams.activityId,
        flag: type,
      };
      this.sendE(param);
    },
    sendE(param) {
      param.flag = Number(param.flag);
      request({
        url: "/activityPromotion/sendEmail",
        method: "post",
        data: param,
      })
        .then((response) => {
          this.$message.success("操作成功");
          this.cancel();
          this.emailSysActiviId = undefined;
          //  this.uploading = false;
          // this.refrashData()
        })
        .catch((err) => {
          this.uploading = false;
          console.log(err);
        });
    },
    deleteUser() {
      this.$modal
        .confirm(
          "是否确认删除" + this.selectedEmailAndPhone.length + "个报名人员？"
        )
        .then(() => {
          let userNamePhone = "";
          this.selectedEmailAndPhone.map((item, index) => {
            userNamePhone += item.userName + "+" + item.mobile + ",";
            if (index == this.selectedEmailAndPhone.length - 1) {
              request({
                url: "/activityPromotion/deleteInfo",
                method: "post",
                data: { userNamePhone: userNamePhone },
              })
                .then((response) => {
                  if (response.code == 200) {
                    this.$modal.msgSuccess("操作成功");
                    this.refrashData();
                  }
                })
                .catch((err) => {
                  console.log(err);
                });
            }
          });
        });
    },
    showAddSms() {
      if (this.queryParams.activityIds && this.queryParams.activityIds != "") {
        let obj = { activityId: this.queryParams.activityIds };
        this.activityMsg = obj;
        this.showSmsEditor = 2;
        this.editorSmsform = {};
        this.paramList = [
          {
            paramName: null,
            type: null,
            description: null,
            length: null,
          },
        ];
        this.hasParam = false;
      } else {
        this.activityMsg = null;
        this.$message.error("请选择活动后在进行操作");
      }
    },
    updateSmsConfig(record) {
      this.showSmsEditor = 3;
      this.editorSmsform = {
        id: record.id,
        templateName: record.templateName,
        templateId: record.templateId,
        smsType: record.smsType,
        appKey: record.appKey,
        appSecret: record.appSecret,
        demoTemp: record.demoTemp,
        sendUrl: record.sendUrl,
        sender: record.sender,
        signature: record.signature,
        smsContent: record.smsContent,
      };
      this.paramList = JSON.parse(JSON.stringify(record.templateParams));
      this.hasParam = record.templateParams.length > 0 ? true : false;
      this.activityMsg = record;
    },
    deleteSmsConfig(record) {
      this.$modal
        .confirm(
          '是否确认删除模板名称"' + record.templateName + '"的短信模板？'
        )
        .then(() => {
          request({
            url: "/sms_config/deleteSmsConfig/" + record.id,
            method: "get",
          })
            .then((response) => {
              if (response.code == 200) {
                this.$modal.msgSuccess("操作成功");
                this.getMessageTemplate(
                  this.queryParams.activityIds
                    ? this.queryParams.activityIds
                    : null
                );
              }
            })
            .catch((err) => {
              console.log(err);
            });
        });
    },
    addRow(num) {
      let paramListTemp = [];
      this.paramList.map((item, index) => {
        paramListTemp.push(item);
        if (num === index) {
          paramListTemp.push({
            paramName: null,
            type: null,
            description: null,
            length: null,
          });
        }
      });
      this.paramList = paramListTemp;
    },
    deleteRow(num) {
      let tempParam = JSON.parse(JSON.stringify(this.paramList));
      // setParamList([])
      tempParam.splice(num, 1);
      // setParamList(tempParam)
      this.paramList = tempParam;
    },
  },
};
</script>
<style scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  min-width: 178px;
  min-height: 178px;
  /* width: 178px;
    height: 178px; */
  line-height: 178px;
  text-align: center;
}
.avatar {
  /* width: 178px;
    height: 178px; */
  min-width: 178px;
  min-height: 178px;
  display: block;
}
.el-tooltip__popper.is-dark {
  min-width: 10px !important;
  max-width: 300px !important;
}
.el-form-item {
  margin-bottom: 10;
}
.editor ::v-deep .ql-container {
  height: 500px;
}
</style>