import request from '@/utils/request'
import {getAccesToken} from '@/utils/auth'




export function access_token(data){
    return request({
        url: '/front/login/oauth/access_token',
        method: 'post',
        data: data
    })
}
export function getActityMsg(data){
    return request({
      url: '/activityManage/getOne',
      method: 'post',
      data: data
    })
  }
export function couponGetOne(data){
    return request({
      url: '/coupon/getOne',
      method: 'post',
      data: data
    })
  }


  export function formList(data){
    return request({
      url: '/api/activity/form/list',
      method: 'post',
      data: data
    })
  }
  export function ticketList(data){
    return request({
      url: '/product/list/'+data,
      method: 'get',
      data: data
    })
  }
  export function regGetOne(data){
    return request({
      url: '/reg/getOne/'+data,
      method: 'get',
      data: data
    })
  }
  export function protocolGetList(data){
    return request({
      url: '/protocol/getList/'+data,
      method: 'get',
      data: data
    })
  }
  export function activityRegInfo(data){
    return request({
      url: '/api/activity/reg/info/'+data,
      method: 'get'
    })
  }
  export function formSave(data){
    return request({
      url: '/api/activity/reg/save',
      method: 'post',
      data: data
    })
  }
  export function orderCancel(data){
    return request({
      url: '/order/cancel',
      method: 'post',
      data: data
    })
  }
  export function orderDelete(data){
    return request({
      url: '/order/del/'+data,
      method: 'delete',
    })
  }
  export function orderRegList(data){
    return request({
      url: '/api/activity/order/reg/list',
      method: 'post',
      data: data
    })
  }
  export function orderCreate(data){
    return request({
      url: '/order/create',
      method: 'post',
      data: data
    })
  }
  export function saveNoTicket(data){
    return request({
      url: '/api/activity/reg/saveNoTicket',
      method: 'post',
      data: data
    })
  }
  export function wapGoPay(data){
    return request({
      url: '/wxpay/pc/pay',
      method: 'post',
      data: data
    })
  }
  export function orderList(data){
    return request({
      url: '/api/activity/order/my',
      method: 'post',
      data: data
    })
  }
  export function orderGoPay(data){
    return request({
      url: '/order/goPay',
      method: 'post',
      data: data
    })
  }
  export function getUserType(){
    return request({
      url: '/api/activity/user/type',
      method: 'get',
    })
  }
  export function contactInfo(){
    return request({
      url: '/system/dict/data/contact',
      method: 'get',
    })
  }
  export function getCardType(){
    return request({
      url: '/api/activity/card/type',
      method: 'get',
    })
  }
  export function getRegistUserList(){
    return request({
      url: '/api/activity/reg/list',
      method: 'get',
    })
  }
  export function queryOrderByOutTradeNo(data){
    return request({
      url: '/wxpay/pc/queryOrderByOutTradeNo?outTradeNo='+data,
      method: 'get',
    })
  }

  export function getatom(){
    return request({
      url: 'https://api.atomgit.com/user/info',
      method: 'get',
      headers:{Authorization:"Bearer "+getAccesToken()}
    })
  }
  export function getActivityImage(activityId){
    return request({
      url: '/activityManage/getActivityImageFront/'+activityId+'/1',
      method:'get',
    })
  }
