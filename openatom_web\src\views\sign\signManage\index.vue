<template>
  <div class="app-container">
    <div class="search-bar">
      <el-form
        ref="form"
        :inline="true"
        :model="searchForm"
        :label-width="'100px'"
      >
        <el-form-item label="姓名：">
          <el-input
            size="small"
            v-model.trim="searchForm.name"
            clearable
            placeholder="请输入姓名"
           
          >
          </el-input>
        </el-form-item>
        <el-form-item label="手机号：">
          <el-input
            size="small"
            v-model.trim="searchForm.phone"
            clearable
            placeholder="请输入手机号"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="签到码：">
          <el-input
            size="small"
            v-model.trim="searchForm.signCode"
            clearable
            placeholder="请输入签到码"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="签到状态：">
          <el-select
            size="small"
            v-model.trim="searchForm.hasSign"
            clearable
            placeholder="请选择签到方式"
          >
            <el-option :label="'全部'" :value="undefined" />
            <el-option :label="'已签到'" :value="true" />
            <el-option :label="'未签到'" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="签到时间段：">
          <el-date-picker
           style="width:215px"
            size="small"
            v-model="signTimes"
            v-trimSpace
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="search(1)"
            >查询</el-button
          >
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleDownTemp"
            >导出</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="seq" label="序号" width="60">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        :resizable="false"
        align="center"
        label="活动名称"
        :show-overflow-tooltip="true"
      >
        {{ this.activityName }}
      </el-table-column>
      <el-table-column prop="name" label="姓名" width="180"> </el-table-column>
      <el-table-column prop="phone" label="手机号"> </el-table-column>
      <el-table-column prop="email" label="邮箱"> </el-table-column>
      <el-table-column
        width="80"
        :resizable="false"
        :show-overflow-tooltip="true"
        prop="signCode"
        label="签到码"
      >
      </el-table-column>
      <el-table-column prop="company" label="单位"> </el-table-column>
      <el-table-column prop="signStatus" label="是否签到">
        <template slot-scope="{ row }">
          <el-tag
            effect="dark"
            :type="row.signStatus === '已签到' ? 'success' : 'info'"
            >{{ row.signStatus }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column prop="signTime" label="签到时间"> </el-table-column>
      <el-table-column prop="createBy" label="签到人"> </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="searchForm.pageNum"
      :limit.sync="searchForm.pageSize"
      @pagination="getData"
    />
  </div>
</template>

<script>
import { querySignedAdminList } from "@/api/sign/signManage";
const defaultForm = {
  content: "",
};
const defaultSearchForm = {
  pageNum: 1,
  pageSize: 10,
  hasSign: undefined,
};
export default {
  name: "homepage",
  components: {},
  data() {
    return {
      searchForm: { ...defaultSearchForm },
      signTimes: [],
      activityId: "",
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      tableData: [],
      total: 0,
    };
  },

  beforeCreate() {},
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activityId = activeInfo.activityId;
  },
  mounted() {
    this.getData();
  },
  update() {},
  methods: {
    handleDownTemp() {
      let obj = { ...this.searchForm };
      delete obj.pageNum;
      delete obj.pageSize;
      this.download(
        "/sign/exportListForAdmin",
        {
          ...obj,
        },
        `签到人员数据_${new Date().getTime()}.xlsx`
      );
    },
    resetSearch() {
      this.searchForm = { ...defaultSearchForm };
      this.signTimes = [];
      this.getData();
    },
    search() {
      this.searchForm.pageNum = 1;
      if (this.signTimes.length > 0) {
        this.searchForm.signStartTime = this.signTimes[0];
        this.searchForm.signEndTime = this.signTimes[1];
      } else {
        delete this.searchForm.signStartTime;
        delete this.searchForm.signEndTime;
      }
      this.getData();
    },
    async getData() {
      this.searchForm.activityId = this.activityId;
      let res = await querySignedAdminList(this.searchForm);
      this.tableData = res.rows;
      this.total = res.total;
    },
  },
};
</script>
<style lang="scss"></style>
