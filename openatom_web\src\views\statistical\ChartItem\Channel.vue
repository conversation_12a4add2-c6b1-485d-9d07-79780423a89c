<template>
  <div class="chart-content" style="width: 99%; height: 600px">
    <div class="top-chart" style="height: 550px"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import { channelAmount } from "@/api/statistical/index";
export default {
  name: "Channel",
  props: {},
  components: {},
  data() {
    return {
      chart: null,
      activityId: "",
      chartData: null,
      yDataBar: [],
      yDataLineAct: [],
      yDataLineShould: [],
      xData: [],
      activityName: JSON.parse(sessionStorage.getItem("activity")).activityName,
      tableData: [],
   
    };
  },
  created() {
    let activeInfo = JSON.parse(sessionStorage.getItem("activity"));
    this.activityId = activeInfo.activityId;
  },
  destroyed() {},
  mounted() {
    this.initChart();
  },
  methods: {
    async initChart() {
      let res = await channelAmount(this.activityId);
      this.chartData = res.data;
      for (const key in res.data) {
        if (Object.hasOwnProperty.call(res.data, key)) {
          const element = res.data[key];
          this.xData.push(key);
          this.yDataBar.push(element);
        }
      }
      this.drawChart();
    },
    drawChart() {
        let option= {
        tooltip: {
          show: true,
          trigger: "axis",
          axisPointer: {
            type: "line",
          },
          // formatter: " {b}<br/>  {c} {c1} {c2}",
        },
        // grid: { left: "16%", right: "10%", bottom: "10%", top: "18%" },
        //  grid: { top:"10%" },
        title: {
          text: "推广统计(报名通过)",
          left: "top",
        },
       
        xAxis: {
          type: "category",
          data: [],
          axisLabel: {
            interval: 0,
            rotate: 20,
          },
        },
        yAxis: [
          {
            name: "单位：人",
            type: "value",
            axisLine: {
              show: true,
            },
          },
        ],
        series: [
          {
            name: "",
            yAxisIndex: 0,
            smooth: true,
            label: {
              show: true,
              position: "right",
              valueAnimation: true,
            },
            lineStyle: {
              normal: {
                color: "rgba(11, 255, 165, 1)",
                shadowBlur: 3,
                shadowColor: "rgba(11, 255, 165, 0.4)",
                shadowOffsetY: 8,
              },
            },
            data: [],
            type: "bar",
            barWidth: 20,
          },
        ],
      }
      this.chart = echarts.init(
        document.getElementsByClassName("top-chart")[4]
      );
     option.series[0].data = this.yDataBar;
      option.xAxis.data = this.xData;
      if(this.xData.length>20){
        option.dataZoom=  [
          {
            type: "slider",
            show: true,
            xAxisIndex: [0],
            // left: '9%',
            // bottom: -5,
            start: 0,
            end: 20, //初始化滚动条
          },
        ]
      }else{
        //  option.dataZoom[0].show=false
      }
      this.chart.setOption(option);
    
      //     window.onresize = () => {
      //   this.chart.resize();
      // };
    },
  },
};
</script>
<style lang='scss'></style>
