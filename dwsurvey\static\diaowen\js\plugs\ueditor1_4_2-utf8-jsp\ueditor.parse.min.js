(function(){(function(){UE=window.UE||{};var f=!!window.ActiveXObject,h={removeLastbs:function(a){return a.replace(/\/$/,"")},extend:function(a,d){for(var b=arguments,c=this.isBoolean(b[b.length-1])?b[b.length-1]:!1,e=this.isBoolean(b[b.length-1])?b.length-1:b.length,k=1;k<e;k++){var n=b[k],l;for(l in n)c&&a.hasOwnProperty(l)||(a[l]=n[l])}return a},isIE:f,cssRule:f?function(a,d,b){var c;b=b||document;c=b.indexList?b.indexList:b.indexList={};var e;if(c[a])e=b.styleSheets[c[a]];else{if(void 0===d)return"";
e=b.createStyleSheet("",b=b.styleSheets.length);c[a]=b}if(void 0===d)return e.cssText;e.cssText=e.cssText+"\n"+(d||"")}:function(a,d,b){b=b||document;var c=b.getElementsByTagName("head")[0],e;if(!(e=b.getElementById(a))){if(void 0===d)return"";e=b.createElement("style");e.id=a;c.appendChild(e)}if(void 0===d)return e.innerHTML;""!==d?e.innerHTML=e.innerHTML+"\n"+d:c.removeChild(e)},domReady:function(a){var d=window.document;"complete"===d.readyState?a():f?(function(){if(!d.isReady){try{d.documentElement.doScroll("left")}catch(b){setTimeout(arguments.callee,
0);return}a()}}(),window.attachEvent("onload",function(){a()})):(d.addEventListener("DOMContentLoaded",function(){d.removeEventListener("DOMContentLoaded",arguments.callee,!1);a()},!1),window.addEventListener("load",function(){a()},!1))},each:function(a,d,b){if(null!=a)if(a.length===+a.length)for(var c=0,e=a.length;c<e;c++){if(!1===d.call(b,a[c],c,a))return!1}else for(c in a)if(a.hasOwnProperty(c)&&!1===d.call(b,a[c],c,a))return!1},inArray:function(a,d){var b=-1;this.each(a,function(a,e){if(a===d)return b=
e,!1});return b},pushItem:function(a,d){-1==this.inArray(a,d)&&a.push(d)},trim:function(a){return a.replace(/(^[ \t\n\r]+)|([ \t\n\r]+$)/g,"")},indexOf:function(a,d,b){var c=-1;b=this.isNumber(b)?b:0;this.each(a,function(a,k){if(k>=b&&a===d)return c=k,!1});return c},hasClass:function(a,d){d=d.replace(/(^[ ]+)|([ ]+$)/g,"").replace(/[ ]{2,}/g," ").split(" ");for(var b=0,c,e=a.className;c=d[b++];)if(!RegExp("\\b"+c+"\\b","i").test(e))return!1;return b-1==d.length},addClass:function(a,d){if(a){d=this.trim(d).replace(/[ ]{2,}/g,
" ").split(" ");for(var b=0,c,e=a.className;c=d[b++];)RegExp("\\b"+c+"\\b").test(e)||(e+=" "+c);a.className=h.trim(e)}},removeClass:function(a,d){d=this.isArray(d)?d:this.trim(d).replace(/[ ]{2,}/g," ").split(" ");for(var b=0,c,e=a.className;c=d[b++];)e=e.replace(RegExp("\\b"+c+"\\b"),"");e=this.trim(e).replace(/[ ]{2,}/g," ");a.className=e;!e&&a.removeAttribute("className")},on:function(a,d,b){var c=this.isArray(d)?d:d.split(/\s+/),e=c.length;if(e)for(;e--;)if(d=c[e],a.addEventListener)a.addEventListener(d,
b,!1);else{b._d||(b._d={els:[]});var k=d+b.toString(),n=h.indexOf(b._d.els,a);b._d[k]&&-1!=n||(-1==n&&b._d.els.push(a),b._d[k]||(b._d[k]=function(a){return b.call(a.srcElement,a||window.event)}),a.attachEvent("on"+d,b._d[k]))}a=null},off:function(a,d,b){var c=this.isArray(d)?d:d.split(/\s+/),e=c.length;if(e)for(;e--;)if(d=c[e],a.removeEventListener)a.removeEventListener(d,b,!1);else{var k=d+b.toString();try{a.detachEvent("on"+d,b._d?b._d[k]:b)}catch(n){}b._d&&b._d[k]&&(d=h.indexOf(b._d.els,a),-1!=
d&&b._d.els.splice(d,1),0==b._d.els.length&&delete b._d[k])}},loadFile:function(){function a(a,c){try{for(var e=0,k;k=d[e++];)if(k.doc===a&&k.url==(c.src||c.href))return k}catch(n){return null}}var d=[];return function(b,c,e){var k=a(b,c);if(k)k.ready?e&&e():k.funs.push(e);else if(d.push({doc:b,url:c.src||c.href,funs:[e]}),!b.body){e=[];for(var n in c)"tag"!=n&&e.push(n+'="'+c[n]+'"');b.write("<"+c.tag+" "+e.join(" ")+" ></"+c.tag+">")}else if(!c.id||!b.getElementById(c.id)){var l=b.createElement(c.tag);
delete c.tag;for(n in c)l.setAttribute(n,c[n]);l.onload=l.onreadystatechange=function(){if(!this.readyState||/loaded|complete/.test(this.readyState)){k=a(b,c);if(0<k.funs.length){k.ready=1;for(var d;d=k.funs.pop();)d()}l.onload=l.onreadystatechange=null}};l.onerror=function(){throw Error("The load "+(c.href||c.src)+" fails,check the url");};b.getElementsByTagName("head")[0].appendChild(l)}}}()};h.each("String Function Array Number RegExp Object Boolean".split(" "),function(a){h["is"+a]=function(d){return Object.prototype.toString.apply(d)==
"[object "+a+"]"}});var g={};UE.parse={register:function(a,d){g[a]=d},load:function(a){h.each(g,function(d){d.call(a,h)})}};uParse=function(a,d){h.domReady(function(){var b;document.querySelectorAll?b=document.querySelectorAll(a):/^#/.test(a)?b=[document.getElementById(a.replace(/^#/,""))]:/^\./.test(a)?(b=[],h.each(document.getElementsByTagName("*"),function(d){d.className&&RegExp("\\b"+a.replace(/^\./,"")+"\\b","i").test(d.className)&&b.push(d)})):b=document.getElementsByTagName(a);h.each(b,function(b){UE.parse.load(h.extend({root:b,
selector:a},d))})})}})();UE.parse.register("insertcode",function(f){var h=this.root.getElementsByTagName("pre");if(h.length)if("undefined"==typeof XRegExp){var g,a;void 0!==this.rootPath?(g=f.removeLastbs(this.rootPath)+"/third-party/SyntaxHighlighter/shCore.js",a=f.removeLastbs(this.rootPath)+"/third-party/SyntaxHighlighter/shCoreDefault.css"):(g=this.highlightJsUrl,a=this.highlightCssUrl);f.loadFile(document,{id:"syntaxhighlighter_css",tag:"link",rel:"stylesheet",type:"text/css",href:a});f.loadFile(document,
{id:"syntaxhighlighter_js",src:g,tag:"script",type:"text/javascript",defer:"defer"},function(){f.each(h,function(a){a&&/brush/i.test(a.className)&&SyntaxHighlighter.highlight(a)})})}else f.each(h,function(a){a&&/brush/i.test(a.className)&&SyntaxHighlighter.highlight(a)})});UE.parse.register("table",function(f){var h=this,g=this.root,a=g.getElementsByTagName("table");if(a.length){a=this.selector;f.cssRule("table",a+" table.noBorderTable td,"+a+" table.noBorderTable th,"+a+" table.noBorderTable caption{border:1px dashed #ddd !important}"+
a+" table.sortEnabled tr.firstRow th,"+a+" table.sortEnabled tr.firstRow td{padding-right:20px; background-repeat: no-repeat;background-position: center right; background-image:url("+this.rootPath+"themes/default/images/sortable.png);}"+a+" table.sortEnabled tr.firstRow th:hover,"+a+" table.sortEnabled tr.firstRow td:hover{background-color: #EEE;}"+a+" table{margin-bottom:10px;border-collapse:collapse;display:table;}"+a+" td,"+a+" th{ background:white; padding: 5px 10px;border: 1px solid #DDD;}"+
a+" caption{border:1px dashed #DDD;border-bottom:0;padding:3px;text-align:center;}"+a+" th{border-top:1px solid #BBB;background:#F7F7F7;}"+a+" table tr.firstRow th{border-top:2px solid #BBB;background:#F7F7F7;}"+a+" tr.ue-table-interlace-color-single td{ background: #fcfcfc; }"+a+" tr.ue-table-interlace-color-double td{ background: #f7faff; }"+a+" td p{margin:0;padding:0;}",document);f.each(["td","th","caption"],function(a){a=g.getElementsByTagName(a);a.length&&f.each(a,function(a){a.firstChild||
(a.innerHTML="&nbsp;")})});a=g.getElementsByTagName("table");f.each(a,function(a){if(/\bsortEnabled\b/.test(a.className))f.on(a,"click",function(a){a=a.target||a.srcElement;var c=d(a,["td","th"]);a=d(a,"table");var c=f.indexOf(a.rows[0].cells,c),l=a.getAttribute("data-sort-type");if(-1!=c&&(b(a,c,h.tableSortCompareFn||l),!f.hasClass(a.rows[0],"firstRow"))){for(c=1;c<a.rows.length;c++)f.removeClass(a.rows[c],"firstRow");f.addClass(a.rows[0],"firstRow")}})});var d=function(a,c){var d,b=a;for(c=f.isArray(c)?
c:[c];b;){for(d=0;d<c.length;d++)if(b.tagName==c[d].toUpperCase())return b;b=b.parentNode}return null},b=function(a,d,b){for(var l=a.rows,q=[],g="TH"===l[0].cells[0].tagName,h=0,s=l.length;h<s;h++)q[h]=l[h];var r={reversecurrent:function(a,b){return 1},orderbyasc:function(a,b){return(a.innerText||a.textContent).localeCompare(b.innerText||b.textContent)},reversebyasc:function(a,b){return b.innerHTML.localeCompare(a.innerHTML)},orderbynum:function(a,b){var d=a[f.isIE?"innerText":"textContent"].match(/\d+/),
c=b[f.isIE?"innerText":"textContent"].match(/\d+/);d&&(d=+d[0]);c&&(c=+c[0]);return(d||0)-(c||0)},reversebynum:function(a,b){var d=a[f.isIE?"innerText":"textContent"].match(/\d+/),c=b[f.isIE?"innerText":"textContent"].match(/\d+/);d&&(d=+d[0]);c&&(c=+c[0]);return(c||0)-(d||0)}};a.setAttribute("data-sort-type",b&&"string"===typeof b&&r[b]?b:"");g&&q.splice(0,1);q=c(q,function(a,c){return b&&"function"===typeof b?b.call(this,a.cells[d],c.cells[d]):b&&"number"===typeof b?1:b&&"string"===typeof b&&r[b]?
r[b].call(this,a.cells[d],c.cells[d]):r.orderbyasc.call(this,a.cells[d],c.cells[d])});l=a.ownerDocument.createDocumentFragment();g=0;for(s=q.length;g<s;g++)l.appendChild(q[g]);a.getElementsByTagName("tbody")[0].appendChild(l)},c=function(a,b){b=b||function(a,b){return a.localeCompare(b)};for(var c=0,d=a.length;c<d;c++)for(var f=c,g=a.length;f<g;f++)if(0<b(a[c],a[f])){var h=a[c];a[c]=a[f];a[f]=h}return a}}});UE.parse.register("charts",function(f){function h(a){var b=[];a=a.getElementsByTagName("table");
for(var c=0,d;d=a[c];c++)null!==d.getAttribute("data-chart")&&b.push(g(d));return b.length?b:null}function g(a){for(var b=a.getAttribute("data-chart"),c={},d=[],f=0,e;e=a.rows[f];f++){for(var g=[],h=0,k;k=e.cells[h];h++){var m=k.innerText||k.textContent||"";g.push("TH"==k.tagName?m:m|0)}d.push(g)}b=b.split(";");for(f=0;e=b[f];f++)e=e.split(":"),c[e[0]]=e[1];return{table:a,meta:c,data:d}}function a(){window.jQuery?d():f.loadFile(document,{src:c+"/third-party/jquery-1.10.2.min.js",tag:"script",type:"text/javascript",
defer:"defer"},function(){d()})}function d(){window.Highcharts?b():f.loadFile(document,{src:c+"/third-party/highcharts/highcharts.js",tag:"script",type:"text/javascript",defer:"defer"},function(){b()})}function b(){f.loadFile(document,{src:c+"/dialogs/charts/chart.config.js",tag:"script",type:"text/javascript",defer:"defer"},function(){for(var a=null,b=null,c=null,d=0,f=k.length;d<f;d++){var a=k[d],b=[],e=[],c=[],g=a.data,h=a.meta;if("1"!=h.dataFormat){for(var p=0,e=g.length;p<e;p++)for(var m=0,t=
g[p].length;m<t;m++)c[m]||(c[m]=[]),c[m][p]=g[p][m];g=c}c={};if(h.chartType!=typeConfig.length-1){e=g[0].slice(1);p=1;for(m=void 0;m=g[p];p++)b.push({name:m[0],data:m.slice(1)});c.series=b;c.categories=e;c.title=h.title;c.subTitle=h.subTitle;c.xTitle=h.xTitle;c.yTitle=h.yTitle}else{m=[];p=1;for(e=g[0].length;p<e;p++)m.push([g[0][p],g[1][p]|0]);b[0]={type:"pie",name:h.tip,data:m};c.series=b;c.title=h.title}c.suffix=h.suffix;b=c;c=a.table;g=document.createElement("div");g.className="edui-chart-container";
c.parentNode.replaceChild(g,c);c=g;a=typeConfig[a.meta.chartType];$(c).highcharts($.extend({},a,{credits:{enabled:!1},exporting:{enabled:!1},title:{text:b.title,x:-20},subtitle:{text:b.subTitle,x:-20},xAxis:{title:{text:b.xTitle},categories:b.categories},yAxis:{title:{text:b.yTitle},plotLines:[{value:0,width:1,color:"#808080"}]},tooltip:{enabled:!0,valueSuffix:b.suffix},legend:{layout:"vertical",align:"right",verticalAlign:"middle",borderWidth:1},series:b.series}))}})}f.cssRule("chartsContainerHeight",
".edui-chart-container { height:"+(this.chartContainerHeight||300)+"px}");var c=this.rootPath,e=this.root,k=null;c&&(e=e?h(e):null,(k=e)&&a())});UE.parse.register("background",function(f){for(var h=this.root.getElementsByTagName("p"),g,a=0,d;d=h[a++];)(g=d.getAttribute("data-background"))&&d.parentNode.removeChild(d);g&&f.cssRule("ueditor_background",this.selector+"{"+g+"}",document)});UE.parse.register("list",function(f){function h(b){var d=this;f.each(b,function(b){if(b.className&&/custom_/i.test(b.className)){var e=
b.className.match(/custom_(\w+)/)[1];if("dash"==e||"dot"==e)f.pushItem(g,c+" li.list-"+a[e]+"{background-image:url("+d.liiconpath+a[e]+".gif)}"),f.pushItem(g,c+" ul.custom_"+e+"{list-style:none;} "+c+" ul.custom_"+e+" li{background-position:0 3px;background-repeat:no-repeat}");else{var h=1;f.each(b.childNodes,function(b){"LI"==b.tagName&&(f.pushItem(g,c+" li.list-"+a[e]+h+"{background-image:url("+d.liiconpath+"list-"+a[e]+h+".gif)}"),h++)});f.pushItem(g,c+" ol.custom_"+e+"{list-style:none;}"+c+" ol.custom_"+
e+" li{background-position:0 3px;background-repeat:no-repeat}")}switch(e){case "cn":f.pushItem(g,c+" li.list-"+e+"-paddingleft-1{padding-left:25px}");f.pushItem(g,c+" li.list-"+e+"-paddingleft-2{padding-left:40px}");f.pushItem(g,c+" li.list-"+e+"-paddingleft-3{padding-left:55px}");break;case "cn1":f.pushItem(g,c+" li.list-"+e+"-paddingleft-1{padding-left:30px}");f.pushItem(g,c+" li.list-"+e+"-paddingleft-2{padding-left:40px}");f.pushItem(g,c+" li.list-"+e+"-paddingleft-3{padding-left:55px}");break;
case "cn2":f.pushItem(g,c+" li.list-"+e+"-paddingleft-1{padding-left:40px}");f.pushItem(g,c+" li.list-"+e+"-paddingleft-2{padding-left:55px}");f.pushItem(g,c+" li.list-"+e+"-paddingleft-3{padding-left:68px}");break;case "num":case "num1":f.pushItem(g,c+" li.list-"+e+"-paddingleft-1{padding-left:25px}");break;case "num2":f.pushItem(g,c+" li.list-"+e+"-paddingleft-1{padding-left:35px}");f.pushItem(g,c+" li.list-"+e+"-paddingleft-2{padding-left:40px}");break;case "dash":f.pushItem(g,c+" li.list-"+e+
"-paddingleft{padding-left:35px}");break;case "dot":f.pushItem(g,c+" li.list-"+e+"-paddingleft{padding-left:20px}")}}})}var g=[],a={cn:"cn-1-",cn1:"cn-2-",cn2:"cn-3-",num:"num-1-",num1:"num-2-",num2:"num-3-",dash:"dash",dot:"dot"};f.extend(this,{liiconpath:"http://bs.baidu.com/listicon/",listDefaultPaddingLeft:"20"});var d=this.root,b=d.getElementsByTagName("ol"),d=d.getElementsByTagName("ul"),c=this.selector;b.length&&h.call(this,b);d.length&&h.call(this,d);if(b.length||d.length)g.push(c+" .list-paddingleft-1{padding-left:0}"),
g.push(c+" .list-paddingleft-2{padding-left:"+this.listDefaultPaddingLeft+"px}"),g.push(c+" .list-paddingleft-3{padding-left:"+2*this.listDefaultPaddingLeft+"px}"),f.cssRule("list",c+" ol,"+c+" ul{margin:0;padding:0;}li{clear:both;}"+g.join("\n"),document)});UE.parse.register("vedio",function(f){var h=this.root.getElementsByTagName("video"),g=this.root.getElementsByTagName("audio");document.createElement("video");document.createElement("audio");if(h.length||g.length){var h=f.removeLastbs(this.rootPath),
g=h+"/third-party/video-js/video.js",a=h+"/third-party/video-js/video-js.min.css",d=h+"/third-party/video-js/video-js.swf";window.videojs?videojs.autoSetup():(f.loadFile(document,{id:"video_css",tag:"link",rel:"stylesheet",type:"text/css",href:a}),f.loadFile(document,{id:"video_js",src:g,tag:"script",type:"text/javascript"},function(){videojs.options.flash.swf=d;videojs.autoSetup()}))}})})();
