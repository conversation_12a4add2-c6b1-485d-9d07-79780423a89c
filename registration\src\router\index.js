import Vue from 'vue'
import Router from 'vue-router'
import {packageName} from'@/utils/request.js'
import { develop } from "@/utils/baseKey.js";
const UserRegist = ()=>import('@/components/UserRegist')
const RegistView = ()=>import('@/components/RegistView')
Vue.use(Router)

  let routesData =  [
    {
      path: '/mobile/auth/login',
      name: 'RegistEnter',
      component: UserRegist
    },
    {
      path: '/',
      name: 'UserRegist',
      component: UserRegist
    },
    {
      path: '/UserRegist',
      name: 'UserRegist',
      component: UserRegist
    },
    {
      path: '/RegistView',
      name: 'RegistView',
      component: RegistView
    },
  ]



// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err)
}
let router = new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  // base:'/registrationTest/',
  base: develop() == "prew" ? "registrationTest" : "registration",
  routes: routesData
});
router.beforeEach((to, from, next) => {
  let userInfo=localStorage.getItem("userInfo")?JSON.parse(localStorage.getItem("userInfo")):null
  // if(userInfo){
    if (window._oafs) {
      window._oafs.setDefault({
        pageId: to.name,
        userId: userInfo&&userInfo.id ? userInfo.id : undefined,
        extra: {
          path: to.path,
        },
      });
      window._oafs.pv();
    }
  // }
  next();
});
export default router;
// export default new Router({
//   mode: 'history', // 去掉url中的#
//   scrollBehavior: () => ({ y: 0 }),
//   // base:'/registrationTest/',
//   base:'/registration/',
//   routes: routesData
// })