<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="票种设置" name="TICKETTYPE">
        <TicketType />
      </el-tab-pane>
      <!-- <el-tab-pane label="发票设置" name="RECEIPT">发票设置内容</el-tab-pane> -->
       <el-tab-pane label="入场卷背景图" name="pictour">
         <PictureImg />
       </el-tab-pane>
        <el-tab-pane label="手机端报名背景图" name="wapBacg">
         <WapImg />
       </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import TicketType from './ticketType.vue'
import PictureImg from "./picture.vue"
import WapImg from "./appImg.vue"
export default {
  name: "Ticket",
  components: {
    TicketType,
    PictureImg,
    WapImg
  },
  data() {
    return {
      activeName: 'TICKETTYPE',
    };
  },
  mounted () {
  },
  methods: {
    // handleClick(tab, event) {
    //   this.activeName = tab.name
    // },
  }
};
</script>

<style scoped lang="scss">

</style>

