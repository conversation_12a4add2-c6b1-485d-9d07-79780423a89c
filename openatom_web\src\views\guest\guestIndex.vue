<template>
  <div class="banner-all">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="60px"
    >
      <el-form-item label="姓名" prop="guest">
        <el-input
          v-model="queryParams.guest"
          placeholder="请输嘉宾姓名"
          clearable
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-plus" @click="goAdd()" size="mini"
          >新增</el-button
        >
          <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleDownTemp"
          >导出</el-button
        >
        <!-- <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleDown"
          v-hasPermi="['activity:reg:download']"
          >模板下载</el-button
        > -->
      </el-form-item>
    </el-form>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="index" label="序号" width="50">
        <template slot-scope="scope">
          <span>{{ scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="menuname" label="所属模块" width="100" /> -->
      <el-table-column
        prop="oldguestpictrue"
        label="头像"
        width="120"
      >
        <template slot-scope="scope">
          <el-image :src="scope.row.guestpictrue"  style="width: 100px; height: 100px">
             </el-image>
        
        </template>
      </el-table-column>
      <el-table-column
        prop="guest"
        label="姓名"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column
        prop="guestduty"
        :show-overflow-tooltip="true"
        label="职务"
      />
      <el-table-column
        prop="describes"
        :show-overflow-tooltip="true"
        label="介绍"
      />
      <el-table-column
        prop="createtime"
        :show-overflow-tooltip="true"
        label="创建时间"
      />
      <el-table-column
        prop="createuser"
        :show-overflow-tooltip="true"
        label="创建人"
      />
      <el-table-column
        prop="updatetime"
        :show-overflow-tooltip="true"
        label="修改时间"
      />
      <el-table-column
        prop="updateuser"
        :show-overflow-tooltip="true"
        label="最后修改人"
      />
      <el-table-column prop="id" label="操作" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="goModify(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="goDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getData"
    />

    <el-dialog :title="title" :visible.sync="dialogVisible" width="35%">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="姓名" prop="guest">
          <el-input v-model="ruleForm.guest" />
        </el-form-item>
        <el-form-item label="头像" prop="picture">
          <el-upload
            action="string"
            list-type="picture-card"
            :limit="1"
            ref="upload"
            :file-list="fileList"
            :auto-upload="false"
            :on-change="handleChange"
            :multiple="true"
            :class="uploadDisabled"
            :on-remove="handleRemove"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <span style="font-size: 12px; color: #ff6a00"
            >您上传的图片可能会根据页面显示进行一定比例的裁剪 <br />
            建议上传图片尺寸:240px*240px;大小小于500kb;格式.jpg、.png的<span
              style="color: red"
              >透明底</span
            >图片文件 <br />
            在线裁剪图片链接:https://www.gaitubao.com/
          </span>
        </el-form-item>
        <el-form-item label="职务" prop="guestduty">
          <el-input v-model="ruleForm.guestduty" />
        </el-form-item>
        <el-form-item label="介绍" prop="describes">
          <el-input type="textarea" v-model="ruleForm.describes" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetForm('ruleForm')">取 消</el-button>
        <el-button
          type="primary"
          @click="goSure('ruleForm')"
          :loading="btnLoading"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import request from "@/utils/request";
import { getToken } from "@/utils/auth";
export default {
  name: "banner",
  data() {
    return {
      fileList: [],
      isImage: false,
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      dialogVisible: false,
      title: "新增",
      ruleForm: {
        guest: "",
        picture: "",
        describes: "",
        guestduty: "",
      },

      uploadDisabled: "",
      rules: {
        guest: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        describes: [
          { required: false, message: "请输入介绍", trigger: "blur" },
        ],
        picture: [{ required: true, message: "请选择头像", trigger: "change" }],
      },
      files: "",
      attachmentId: [],
      tableData: [],
      total: 0,

      btnLoading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        guest:undefined
      },
    };
  },
  created() {
    this.getData();
  },
  methods: {
          handleDownTemp() {
      let obj = { ...this.queryParams };
      obj.dealIdCard = false;
      delete obj.pageNum;
      delete obj.pageSize;
      this.download(
        "/guest/export",
        {
          ...obj,
        },
        `嘉宾库人员数据_${new Date().getTime()}.xlsx`
      );
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getData();
    },
    handleChange(file, fileList) {
      let flag = true;
      const imgType =
        file.raw.type === "image/jpeg" || file.raw.type === "image/png";
      const isLt500k = file.size / 1024 / 1024 < 0.5;
      if (!imgType) {
        flag = false;
        this.fileList = [];
        this.$message.error("上传图片只能是 JPG和png 格式!");
        return false;
      }
      if (!isLt500k) {
        flag = true;
        this.$message.error("上传图片大小不能超过 500kB!");
        this.fileList = [];
        return false;
      }
      if (flag) {
        this.ruleForm.picture = file.name;
        this.files = file;
        this.attachmentId.push(file.uid);
        if (this.attachmentId.length > 0) {
          this.uploadDisabled = "disabled";
        }
      }
    },
    handleRemove() {
      this.uploadDisabled = "";
      this.ruleForm.picture = undefined;
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogVisible = false;
    },
    getData() {
      const formData = new FormData();
    //   const data = {
    //     pageNum: this.pageNum,
    //     pageSize: this.pageSize,
    //     // activityid: this.activityNum,
    //     // moduleid: "5",
    //     // back: "0",
    //   };
      Object.keys(this.queryParams).map((item) => {
        if (this.queryParams[item]) {
          formData.append(item, this.queryParams[item]);
        }
      });
      request({
        url: "/guest/list",
        data: formData,
        method: "post",
      }).then((data) => {
        this.total = data.total;
        this.tableData = data.rows;
      });
    },
    goAdd() {
      this.dialogVisible = true;
      this.title = "新增";
      this.fileList = [];
      this.uploadDisabled = "";
      this.ruleForm = {
        guest: "",
        picture: "",
        describes: "",
        guestduty: "",
      };
    },
    goModify(val) {
      // this.fileList = []
      this.dialogVisible = true;
      this.title = "修改";
      this.ruleForm = {
        guest: val.guest,
        picture: val.guestpictrue,
        // picture: val.picture,
        describes: val.describes,
        guestduty: val.guestduty,
        id: val.id,
      };
      // this.fileList.push({ url: val.picture })
      let obj = {
        name: val.oldguestpictrue,
        url: val.guestpictrue,
      };
      this.fileList = [{ ...obj }];
      this.uploadDisabled = "disabled";
    },
    goSure(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.getAxios();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getAxios() {
      this.btnLoading = true;
      if (this.title == "新增") {
        const formData = new FormData();
        formData.append("files", this.files ? this.files.raw : undefined);
        formData.append(
          "parameter",
          JSON.stringify({
            guest: this.ruleForm.guest,
            // picture: this.ruleForm.picture,
            guestduty:this.ruleForm.guestduty,
            describes: this.ruleForm.describes,
          })
        );
        request({
          url: "/guest/save",
          method: "post",
          data: formData,
        }).then((data) => {
          if (data.code == 200) {
            this.$message.success("新增成功");
            this.fileList = [];
            this.resetForm("ruleForm");
            this.files = undefined;
            this.dialogVisible = false;
            this.btnLoading = false;
            this.getData();
          } else {
            this.$message.success("新增失败");
            this.btnLoading = false;
          }
        });
      } else {
        const formData = new FormData();
        formData.append("files", this.files ? this.files.raw : undefined);
        formData.append(
          "parameter",
          JSON.stringify({
           guest: this.ruleForm.guest,
            guestduty:this.ruleForm.guestduty,
            describes: this.ruleForm.describes,
            id: this.ruleForm.id,
          })
        );
        request({
          url: "/guest/update",
          method: "post",
          data: formData,
        }).then((data) => {
          if (data.code == 200) {
            this.$message.success("修改成功");
            this.fileList = [];
            this.resetForm("ruleForm");
            this.files = undefined;
            this.dialogVisible = false;
            this.btnLoading = false;
            this.getData();
          } else {
            this.$message.success("修改失败");
            this.btnLoading = false;
          }
        });
      }
    },
    goDelete(val) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      })
        .then(() => {
          request({
            url: "/guest/delete/" + val.id,
            method: "get",
          }).then((data) => {
            if (data.code == 200) {
              this.$message.success("删除成功");
              this.getData();
            } else {
              this.$message.success("删除失败");
            }
          });
        })
        .catch(() => {
          return false;
        });
    },
  },
};
</script>
  
<style lang="scss" scoped>
.banner-all {
  padding: 40px;

  .btn-item {
    margin-bottom: 20px;
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}
</style>
<style rel="stylesheet/scss" lang="scss">
.disabled .el-upload--picture-card {
  display: none !important;
}
</style>
  