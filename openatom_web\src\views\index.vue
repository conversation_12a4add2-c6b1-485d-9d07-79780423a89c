<template>
  <div class="app-container">
    <el-row :gutter="10" v-if="showType === 1">
      <el-col :span="24">
        <el-card style="height: calc(100vh - 125px); overflow: auto">
          <el-tabs
            v-model="queryParams.activityStatus"
            @tab-click="handleClick"
          >
            <el-tab-pane label="未发布" name="1" :disabled="flag1">
            </el-tab-pane>
            <el-tab-pane label="已发布" name="2" :disabled="flag2">
            </el-tab-pane>
            <el-tab-pane label="已结束" name="3" :disabled="flag3">
            </el-tab-pane>
            <!-- <el-tab-pane
              v-for="(dict, num) in postTypeOption"
              :disabled="flag + dict.dictValue"
              :label="dict.dictLabel"
              :name="dict.dictValue"
              :key="dict.dictValue"
            /> -->
          </el-tabs>

          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            label-width="68px"
            @submit.native.prevent
          >
            <el-form-item label="关键字" prop="searchParam">
              <el-input
                v-model="queryParams.searchParam"
                style="width: 200px"
                placeholder="请输入关键字"
                clearable
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="getList"
                >搜索</el-button
              >
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['activity:manage:save']"
                >新增活动</el-button
              >
            </el-form-item>
          </el-form>

          <div
            style="margin: auto; text-align: left; padding-bottom: 10px"
            v-if="postList.length > 0"
          >
            <div class="activityItem" v-for="activity in this.postList">
              <div class="iConActivity">
                <span
                  style="color: #eb9e04; font-weight: bold; font-size: 20px"
                  >{{ activity.totalRegCnt }}</span
                ><br />已报名
              </div>
              <div class="msgActivity">
                <div>活动名称：{{ activity.activityName }}</div>
                <div>
                  <div style="width: 60%">
                    活动周期：{{
                      activity.startDate + " ~ " + activity.endDate
                    }}
                  </div>
                  <div style="float: right; width: 40%">
                    地点： {{ activity.activityPlace }}
                  </div>
                </div>
                <div class="btnTool">
                  <i
                    class="el-icon-s-tools"
                    @click="handleSelect('/activity', activity)"
                    v-hasPermi="['activity:manage:update']"
                    >管理</i
                  >

                  <i
                    v-if="activity.haveExhibitors == 1"
                    class="el-icon-s-tools"
                    @click="handleSelect('/exhibitor', activity)"
                    v-hasPermi="['activity:manage:update']"
                    >展商管理</i
                  >
                  <i
                    class="el-icon-edit"
                    style="color: #a4da89"
                    @click="handleUpdate(activity)"
                    v-if="activity.activityStatus == 1"
                    v-hasPermi="['activity:manage:update']"
                    >编辑</i
                  >
                  <!-- <i class="el-icon-document-copy" style="color:#6959CD">复制</i> -->
                  <i
                    class="el-icon-delete"
                    style="color: #f9a7a7"
                    @click="handleDelete(activity)"
                    v-hasPermi="['activity:manage:save']"
                    v-if="activity.activityStatus == 1"
                    >删除</i
                  >
                  <!-- v-hasPermi="['activity:sign:save']" -->
                  <i
                    class="el-icon-edit"
                    @click="handleSgin(activity)"
                    v-if="activity.activityStatus == 2 && activity.showSign"
                    >签到</i
                  >
                </div>
              </div>
              <div class="actionActivity">
                昨日新增<br />
                <span
                  style="color: #eb9e04; font-weight: bold; font-size: 30px"
                  >{{ activity.yesterdayRegCnt }}</span
                >
              </div>
              <div style="clear: both"></div>
            </div>
          </div>
          <div v-else>暂无数据</div>
          <pagination
            v-show="this.total > 0"
            :total="this.total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改岗位对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="600px"
      append-to-body
      :destroy-on-close="true"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-form-item
          v-if="form.activityId && form.activityId != ''"
          label="活动编号"
          prop="activityId"
        >
          <el-input
            :disabled="form.activityId && form.activityId != ''"
            v-model="form.activityId"
            placeholder="请输入活动编号"
          />
        </el-form-item>
        <el-form-item label="活动类型" prop="activityType">
          <el-select
            v-model="form.activityType"
            placeholder="请选择活动类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in typeList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动属性" prop="activityNameType">
          <el-select
            v-model="form.activityNameType"
            placeholder="请选择活动属性"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in activetypeList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动名称" prop="activityName">
          <el-input
            v-model="form.activityName"
            placeholder="请输入活动名称"
            maxlength="150"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="是否自动审批" prop="isAutoApproval">
          <!-- <el-input v-model="form.isAutoApproval" placeholder="请选择是否自动审批" /> -->
          <el-select
            v-model="form.isAutoApproval"
            placeholder="请选择是否自动审批"
            clearable
            style="width: 100%"
          >
            <el-option label="是" :value="1" />
            <el-option label="否" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item prop="isBuyTicket">
          <span slot="label"
            >是否售票
            <el-tooltip class="item" effect="dark" placement="top-start">
              <template slot="content">
                <div>是否售票选择后,无法修改,请谨慎操作</div>
              </template>
              <i class="el-icon-warning-outline"></i> </el-tooltip
          ></span>
          <el-select
            :disabled="form.activityId != undefined"
            v-model="form.isBuyTicket"
            placeholder="请选择是否售票"
            clearable
            style="width: 100%"
          >
            <el-option label="是" :value="1" />
            <el-option label="否" :value="-1" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否需要展商" prop="haveExhibitors">
          <!-- <el-input v-model="form.isAutoApproval" placeholder="请选择是否自动审批" /> -->
          <el-select
            v-model="form.haveExhibitors"
            placeholder="请选择是否需要展商"
            clearable
            style="width: 100%"
          >
            <el-option label="是" :value="1" />
            <el-option label="否" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="短信邮件发送" prop="sendType">
          <el-select
            v-model="form.sendType"
            placeholder="请选择短信邮件发送类型"
            clearable
            style="width: 100%"
          >
            <el-option label="正常发送(包含电子票链接)" :value="1" />
            <el-option label="正常发送(不包含电子票链接)" :value="2" />
              <el-option label="不发送" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="活动地点" prop="activityPlace">
          <el-input
            v-model="form.activityPlace"
            placeholder="请输入活动地点"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="活动简介" prop="activityDescription">
          <el-input
            v-model="form.activityDescription"
            type="textarea"
            :rows="5"
            placeholder="请输入活动简介"
          />
        </el-form-item>
        <el-form-item label="活动周期" prop="stratEndTime">
          <el-date-picker
            v-model="form.stratEndTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="btnloading"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from "@/utils/request";
import { constantRoutes } from "@/router";
import activityConfig from "@/views/activity/activityConfig";
import signPage from "../views/sign/signPage/signPage.vue";
import { checkActiveName } from "@/utils/validate";
import {
  listPost,
  getPost,
  delPost,
  addPost,
  updatePost,
} from "@/api/system/post";
export default {
  name: "Index",
  components: {
    signPage,
    activityConfig,
  },
  data() {
    return {
      activeName: "0",
      postList: [],
      postTypeOption: [],
      loading: false,
      showType: 1, //首页
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        searchParam: undefined,
        activityStatus: sessionStorage.getItem("activityStatus")
          ? sessionStorage.getItem("activityStatus")
          : "1",
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {
        activityName: undefined,
        activityDescription: undefined,
        activityPlace: undefined,
        activityType: undefined,
        stratEndTime: undefined,
        isAutoApproval: undefined,
        activityNameType: undefined,
        isBuyTicket: 1,
        haveExhibitors: undefined,
        sendType:undefined
      },
      // 表单校验
      rules: {
        activityName: [
          { required: true, message: "活动名称不能为空", trigger: "blur" },
          { required: true, validator: checkActiveName, trigger: "blur" },
        ],
        activityDescription: [
          { required: true, message: "活动简介不能为空", trigger: "blur" },
        ],
        stratEndTime: [
          { required: true, message: "活动周期不能为空", trigger: "blur" },
        ],
        activityType: [
          { required: true, message: "活动类型不能为空", trigger: "change" },
        ],
        isAutoApproval: [
          { required: true, message: "是否自动审批不能为空", trigger: "change" },
        ],
        activityPlace: [
          { required: true, message: "活动地点不能为空", trigger: "blur" },
          { required: true, validator: checkActiveName, trigger: "blur" },
        ],
        activityNameType: [
          { required: true, message: "活动属性不能为空", trigger: "change" },
        ],
        isBuyTicket: [
          { required: true, message: "是否购票不能为空", trigger: "change" },
        ],
        haveExhibitors: [
          { required: true, message: "是否需要展商不能为空", trigger: "change" },
        ],
        sendType:[
           { required: true, message: "短信邮件发送类型不能为空", trigger: "change" },
        ]
      },
      flowRules: {
        flowno: [
          { required: true, message: "请选择报名流程", trigger: "blur" },
        ],
      },
      flowOpen: false,
      flowform: { flowno: undefined },
      flowOptions: [],
      flowDisabled: false,
      activeOpen: false,
      activeform: {},
      activeList: [],
      Activeloading: false,
      activeQuery: {
        pageNum: 1,
        pageSize: 10,
      },
      activetotal: 0,
      sysUserOpen: false,
      sysUserListform: {},
      // queryParams: {
      //   pageNum: 1,
      //   pageSize: 10,
      //   userName: undefined,
      // },
      // 列信息
      columns: [
        { key: 1, label: `用户名称`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 6, label: `邮箱`, visible: true },
      ],
      userList: [],
      selectedUser: [],
      typeList: [],
      flag1: false,
      flag2: false,
      flag3: false,
      signPageOpen: false,
      activetypeList: [],
      signList: [],
      btnloading: false,
    };
  },
  created() {
    // this.getPostType();
    this.getList();
    this.getTypeList();
    this.getActivetypeList();
  },
  computed: {
    // 所有的路由信息
    routers() {
      return this.$store.state.permission.topbarRouters;
    },
    // 设置子路由
    childrenMenus() {
      var childrenMenus = [];
      this.routers.map((router) => {
        for (var item in router.children) {
          if (router.children[item].parentPath === undefined) {
            if (router.path === "/") {
              router.children[item].path = "/" + router.children[item].path;
            } else {
              if (!this.ishttp(router.children[item].path)) {
                router.children[item].path =
                  router.path + "/" + router.children[item].path;
              }
            }
            router.children[item].parentPath = router.path;
          }
          childrenMenus.push(router.children[item]);
        }
      });
      return constantRoutes.concat(childrenMenus);
    },
    // 默认激活的菜单
    activeMenu() {
      const path = this.$route.path;
      let activePath = path;
      if (
        path !== undefined &&
        path.lastIndexOf("/") > 0 &&
        hideList.indexOf(path) === -1
      ) {
        const tmpPath = path.substring(1, path.length);
        activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
        if (!this.$route.meta.link) {
          this.$store.dispatch("app/toggleSideBarHide", false);
        }
      } else if (!this.$route.children) {
        activePath = path;
        this.$store.dispatch("app/toggleSideBarHide", true);
      }
      this.activeRoutes(activePath);
      return activePath;
    },
  },
  watch: {
    signList: {
      handler(val, oldval) {
        if (val != oldval) {
          if (val && val.length > 0) {
            let postStr = [];
            let newLlist = [...this.postList];
            newLlist.map((item) => {
              postStr.push(item.activityId);
            });
            val.map((item, index) => {
              if (newLlist.length > 0) {
                newLlist.map((dic, num) => {
                  if (item.activityId == dic.activityId) {
                    dic.showSign = true;
                  } else {
                    if (postStr.indexOf(item.activityId) == -1) {
                      newLlist.push(item);
                    }
                  }
                });
                this.postList = newLlist;
              } else {
                this.postList = val;
              }
            });
            // this.postList;
          }
        }
      },
    },
  },
  methods: {
    getActivetypeList() {
      request({
        url: "/system/dict/data/list?pageNum=1&pageSize=100&dictType=activity_name_type&status=0",
        method: "get",
      })
        .then((response) => {
          this.activetypeList = response.rows;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleSgin(activity) {
      sessionStorage.setItem("activity", JSON.stringify(activity));
      this.$router.push("sign");
      // this.showType=2
    },
    getTypeList() {
      request({
        url: "/system/dict/data/list?pageNum=1&pageSize=100&dictType=activity_type&status=0",
        method: "get",
      })
        .then((response) => {
          this.typeList = response.rows;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    callbackHome() {
      this.showType = 1;
    },
    // 菜单选择事件
    handleSelect(key, activity) {
      sessionStorage.setItem("activity", JSON.stringify(activity));
      // this.showType = 2
      // this.$store.dispatch("setActive",activity)
      this.currentIndex = key;
      const route = this.routers.find((item) => item.path === key);
      if (this.ishttp(key)) {
        // http(s):// 路径新窗口打开
        window.open(key, "_blank");
      } else if (!route || !route.children) {
        // 没有子路由路径内部打开
        const routeMenu = this.childrenMenus.find((item) => item.path === key);
        if (routeMenu && routeMenu.query) {
          let query = JSON.parse(routeMenu.query);
          this.$router.push({ path: key, query: query });
        } else {
          this.$router.push({ path: key });
        }
        this.$store.dispatch("app/toggleSideBarHide", true);
      } else {
        // 显示左侧联动菜单
        this.activeRoutes(key);
        this.$store.dispatch("app/toggleSideBarHide", false);
      }
    },
    // 当前激活的路由
    activeRoutes(key) {
      var routes = [];
      if (this.childrenMenus && this.childrenMenus.length > 0) {
        this.childrenMenus.map((item) => {
          if (key == item.parentPath || (key == "index" && "" == item.path)) {
            routes.push(item);
          }
        });
      }
      if (routes.length > 0) {
        // window.location.href="http://atomgitactivity.com/atomgit/activity/post"
        this.$router.push({ path: routes[0].path });
        // window.location.reload()
        this.$store.commit("SET_SIDEBAR_ROUTERS", routes);
      } else {
        this.$store.dispatch("app/toggleSideBarHide", true);
      }
    },
    ishttp(url) {
      return url.indexOf("http://") !== -1 || url.indexOf("https://") !== -1;
    },
    getPostType() {
      request({
        url: "/system/dict/data/list?pageNum=1&pageSize=100&dictType=activity_status&status=0",
        method: "get",
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            this.postTypeOption = response.rows;
            this.queryParams.activityStatus = response.rows[0].dictValue;
            response.rows.map((item) => {
              item.flag = false;
            });
            this.getList();
          } else {
            this.getList();
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    activityListBySign() {
      let param = JSON.parse(JSON.stringify(this.queryParams));
      param.activityStatus = Number(this.queryParams.activityStatus);
      let formData = new window.FormData();
      Object.keys(param).map((item) => {
        if (param[item]) {
          formData.append(item, param[item]);
        }
      });
      request({
        url: "/activityManage/activityListBySign",
        method: "post",
        data: formData,
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            response.rows.map((item) => {
              item.showSign = true;
              if (item.startDate == item.endDate) {
                item.stratEndTime = [];
                item.stratEndTime.push(item.startDate);

                // item.stratEndTime = item.startDate;
              } else {
                item.stratEndTime = [];
                item.stratEndTime.push(item.startDate);
                item.stratEndTime.push(item.endDate);
                // item.stratEndTime = item.startDate + "至" + item.endDate;
              }
            });
          }
          this.signList = response.rows;
          // this.total = response.total;
          // this.loading = false;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getList(index) {
      this.loading = true;
      let param = JSON.parse(JSON.stringify(this.queryParams));
      param.activityStatus = Number(this.queryParams.activityStatus);
      // param.pageSize = 5;
      let formData = new window.FormData();
      Object.keys(param).map((item) => {
        if (param[item]) {
          // if (item != "pageNum" && item != "pageSize") {
          //   formData.append(item, param[item]);
          // }
          formData.append(item, param[item]);
        }
      });
      request({
        url: "/activityManage/list",
        method: "post",
        data: formData,
      })
        .then((response) => {
          if (response.rows && response.rows.length > 0) {
            response.rows.map((item) => {
              item.showSign = false;
              if (item.startDate == item.endDate) {
                item.stratEndTime = [];
                item.stratEndTime.push(item.startDate);

                // item.stratEndTime = item.startDate;
              } else {
                item.stratEndTime = [];
                item.stratEndTime.push(item.startDate);
                item.stratEndTime.push(item.endDate);
                // item.stratEndTime = item.startDate + "至" + item.endDate;
              }
            });
          }
          // if (index == 1) {
          //   this.activityListBySign();
          // }
          if (this.queryParams.activityStatus == 2) {
            this.activityListBySign();
          }
          this.postList = response.rows;
          this.total = response.total;
          this.loading = false;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleClick(tab) {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.queryParams.searchParam = undefined;
      this.queryParams.activityStatus = (Number(tab.index) + 1).toString();
      sessionStorage.setItem("activityStatus", this.queryParams.activityStatus);
      if (tab.index == 0) {
        this.flag2 = true;
        this.flag3 = true;
        setTimeout(() => {
          this.flag2 = false;
          this.flag3 = false;
        }, 1000);
      }
      if (tab.index == 1) {
        this.flag1 = true;
        this.flag3 = true;
        setTimeout(() => {
          this.flag1 = false;
          this.flag3 = false;
        }, 1000);
      }
      if (tab.index == 2) {
        this.flag1 = true;
        this.flag2 = true;
        setTimeout(() => {
          this.flag1 = false;
          this.flag2 = false;
        }, 1000);
      }
      this.getList(tab.index);
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      this.form = {};
      this.open = true;
      this.title = "添加活动";
    },
    handleUpdate(row) {
      this.form.id = row.id;
      this.form.activityId = row.activityId;
      this.form.activityName = row.activityName;
      this.form.activityDescription = row.activityDescription;
      this.form.activityPlace = row.activityPlace;
      this.form.activityType = row.activityType.toString();
      this.form.activityStatus = row.activityStatus;
      this.form.stratEndTime = [];
      this.form.stratEndTime.push(row.startDate);
      this.form.stratEndTime.push(row.startDate);
      this.form.isAutoApproval = row.isAutoApproval;
      this.form.activityNameType = row.activityNameType.toString();
      this.form.isBuyTicket = row.isBuyTicket;
      this.form.haveExhibitors = row.haveExhibitors;
      this.form.sendType=row.sendType
      this.open = true;
      this.title = "修改活动";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        // this.form.isBuyTicket = 1;
        if (valid) {
          this.btnloading = true;
          this.form.startDate = this.form.stratEndTime[0];
          this.form.endDate = this.form.stratEndTime[1];
          if (this.form.activityId != undefined) {
            updatePost(this.form)
              .then((response) => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
                this.btnloading = false;
              })
              .catch(() => {
                this.btnloading = false;
              });
          } else {
            addPost(this.form)
              .then((response) => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
                this.btnloading = false;
              })
              .catch(() => {
                this.btnloading = false;
              });
          }
        }
      });
    },
    submitFlowForm() {
      this.$refs["flowform"].validate((valid) => {
        if (valid) {
          request({
            url: "/activityManage/saveActivityFlow",
            method: "post",
            data: this.flowform,
          })
            .then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.flowOpen = false;
              this.getList();
            })
            .catch((err) => {
              console.log(err);
            });
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.form = {
        activityName: undefined,
        activityDescription: undefined,
        activityPlace: undefined,
        activityType: undefined,
        stratEndTime: undefined,
        isAutoApproval: undefined,
        activityNameType: undefined,
        isBuyTicket: 1,
        haveExhibitors: undefined,
      };
      // this.reset();
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const postIds = row.postId;
      let obj = {};
      obj.id = row.id;
      obj.activityId = row.activityId;
      obj.isValidate = 0;
      this.$modal
        .confirm('是否确认删除活动编号为"' + row.activityId + '"的数据？')
        .then(function () {
          return delPost(obj);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss">
.app-container {
  font-size: 14px;
}
::v-deep ._fc-designer .el-aside {
  padding: 0 !important;
  background: #fff !important;
}
aside {
  padding: 0;
  background: #fff;
}
.el-dialog__body {
  padding: 10px;
}
.el-tooltip__popper.is-dark {
  min-width: 10px !important;
  max-width: 300px !important;
}
.el-divider--horizontal {
  margin: 10px 0;
}
._fc-m-tools .is-round {
  display: none !important;
}
.activityItem {
  background-color: #eff7fe;
  margin: 0px 5px 20px 5px;
  border-radius: 10px;
  padding: 10px;
  // width: 1100px;
}
.iConActivity,
.msgActivity,
.actionActivity {
  float: left;
  margin: 10px 15px;
  padding: 0px 15px;
}
.iConActivity {
  height: 95px;
  width: 95px;
  border: 10px solid #5893fc;
  // border: 10px solid #eb9e04;
  border-radius: 47px;
  padding-top: 20px;
  line-height: 20px;
  margin: 10px 0px;
  text-align: center;
}
.msgActivity {
  width: 80%;
  line-height: 30px;
  border-left: 3px solid #ffffff;
  border-right: 3px solid #ffffff;
  vertical-align: middle;
}
.actionActivity {
  padding-top: 20px;
  line-height: 30px;
  text-align: center;
}
.btnTool > i {
  // color: #606266;
  margin: 0 20px;
  font-size: 1em;
  vertical-align: middle;
  color: #5893fc;
}
.btnTool > i {
  cursor: pointer;
}
</style>


<style scoped lang="scss">
</style>
