<template>
  <div class="" style="wdith: 100%">
    <div
      style="
        margin: auto;
        text-align: center;
        padding: 10px;
        font-size: 15px;
        font-weight: bold;
        width: 100%;
      "
    >
      入场票信息/Event Pass
    </div>
    <div style="padding: 0px 10px">
      <!-- <img src="@/assets/activityMain.png" class="image" style="width: 100%" /> -->
      <div
        style="margin: auto; text-align: left; line-height: 28px"
        v-if="activeMsg.activityName"
      >
        <div>
          <div class="codeTitle">活动名称/Event</div>
          <div style="text-align: left">{{ activeMsg.activityName }}</div>
        </div>

        <div>
          <div class="codeTitle">参会人/Attendee</div>
          <div style="text-align: left">{{ activeMsg.name }}</div>
        </div>
        <div>
          <div class="codeTitle">票种/Ticket Type</div>
          <div style="text-align: left">{{ activeMsg.ticketName }}</div>
        </div>
        <div>
          <div class="codeTitle">核销码/Verification Code</div>
          <div style="text-align: left">{{ activeMsg.signCode }}</div>
        </div>
        <div class="codeTitle">电子票/E-Ticket</div>
        <div style="margin: -25px; text-align: center">
          <img
            v-if="activeMsg && activeMsg.qrCode"
            :src="activeMsg.qrCode"
            class="image"
          />
          <!-- <div v-else>暂无数据</div> -->
        </div>
      </div>
      <div v-else style="margin: auto; text-align: left; line-height: 28px">
        <div>
          <div class="codeTitle">活动名称/Event</div>
          <div style="text-align: left">暂未查到相关活动</div>
        </div>

        <div>
          <div class="codeTitle">参会人/Attendee</div>
          <div style="text-align: left">暂未查到参会人</div>
        </div>
        <div>
          <div class="codeTitle">核销码/Verification Code</div>
          <div style="text-align: left">暂未查到核销码</div>
        </div>
        <div class="codeTitle">电子票/E-Ticket</div>
        <div>
          <div
            style="
              border: 1px solid #ccc;
              width: 200px;
              height: 200px;
              margin: 0 auto;
              line-height: 200px;
              text-align: center;
            "
          >
            暂未查询到相关电子票
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request from "@/utils/request";
export default {
  name: "regView",
  data() {
    return { activeMsg: {} };
  },

  beforeCreate() {},
  created() {
    let code = window.location.href.split("?");
    if (code.length > 0 && code[1] && code[1] != "") {
      this.getActiveMsg(code[1]);
    }
  },
  mounted() {},
  update() {},
  methods: {
    getActiveMsg(code) {
      request({
        url: "/reg/getSignInfo/" + code,
        method: "get",
      })
        .then((response) => {
          if (response.code == 200) {
            this.activeMsg = response.data;
          }
        })
        .catch((err) => {
          this.loading = false;
          console.log(err);
        });
    },
  },
};
</script>
<style scoped>
.codeTitle {
  font-size: 15px;
  font-weight: bold;
  color: #030b1a;
}
</style>