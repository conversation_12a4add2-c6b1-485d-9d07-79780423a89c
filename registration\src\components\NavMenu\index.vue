<template>
  <div class="NavMenu">
    <div class="log" style="margin:0 49px 0 104px">
      <img src="@/img/content_9_2.png" alt="">
    </div>
    <div class="el-menu-demo">
      <el-menu active-text-color='#25BEFF' :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
        <el-menu-item index="1">我要报名/Register</el-menu-item>
        <el-menu-item index="2">个人中心/User Center</el-menu-item>
      </el-menu>
    </div>
    <div class="user" v-if="userInfo">
      <div style="display:flex;margin-right:8px" v-if=" userInfo.avatar_url">
        <el-avatar size="large" :src="userInfo.avatar_url"/>
      </div>
      <div style="padding-right:10px;">{{ userInfo.login }}</div>

    </div>
  </div>
</template>

<script>
import moment from 'moment'
moment.locale("zh-cn");
export default {
  name: "NavMenu",
  data() {
    return {
       activeIndex: '1',
       userInfo:JSON.parse(localStorage.getItem("userInfo"))
    };
  },
  mounted(){
    if(window.location.href.indexOf("/RegistView")>-1){
      this.activeIndex= '2'
    }else{
      this.activeIndex= '1'
    }
  },
  methods: {
    handleSelect(key, keyPath) {
      if(key === "1"){
        localStorage.setItem("showOrder",false)
        localStorage.setItem("redirectType",0)
        this.$router.replace({ path: '/UserRegist'})
      }else if(key === "2"){
        localStorage.setItem("showOrder",false)
        localStorage.setItem("redirectType",0)
        this.$router.replace({ path: '/RegistView'})
      }
    }
  }
};
</script>

<style>
.NavMenu{
  display: flex;
  align-items: center;
  position: relative;
  background-color: #fff;
}
.el-menu-item{
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0px;
  text-align: center;
}
.el-submenu__title{
  font-family: PingFang SC;
  font-size: 16px!important;
  font-weight: 400;
  letter-spacing: 0px;
  text-align: center;
}
.user{
  position: absolute;
  right: 60px;
  display: flex;
  color: #909399;
  align-items: center;
}
</style>
