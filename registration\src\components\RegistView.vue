<template>
  <div>
    <NavMenu />
    <div>
      <div class="registEnter">
        <div class="personalCenter" v-if="showType === 1">
          <div style="margin: auto; text-align: left">
            <el-radio-group v-model="active" @change="refrashData">
              <el-radio-button label="1">我的报名/My Registration</el-radio-button>
              <el-radio-button label="2">我的订单/My Order</el-radio-button>
            </el-radio-group>
          </div>

          <div v-if="active === '1'">
            <el-row :gutter="20" v-if="userData.length > 0">
              <el-col
                :span="6"
                v-for="(userMsg, index) in userData"
                :key="index"
              >
                <div class="viewTicket">
                  <div :class="stateShow(userMsg.approveStatus)">
                    <div>
                      <el-avatar
                        :size="50"
                        class="userLogo"
                        style="float: left"
                        :src="userLogo"
                      ></el-avatar>
                      <div
                        style="
                          font-weight: bold;
                          width: 50%;
                          font-size: 13px;
                          float: left;
                          line-height: 25px;
                          padding-top: 30px;
                          padding-left: 10px;
                          overflow: hidden;
                          white-space: nowrap;
                          text-overflow: ellipsis;
                        "
                      >
                        {{ userMsg.name }}
                        <div
                          style="
                            overflow: hidden;
                            width: 110px;
                            font-size: 12px;
                            font-weight: 200;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                          "
                        >
                          {{
                            userMsg && userMsg.userTypeName
                              ? userMsg.userTypeName
                              : ""
                          }}
                        </div>
                      </div>
                      <div style="clear: both"></div>
                    </div>
                  </div>

                  <div
                    style="padding: 5px 20px; line-height: 30px; height: 160px"
                  >
                    <div
                      style="
                        clear: both;
                        margin-top: 10px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      "
                    >
                      {{
                        userMsg && userMsg.activityName
                          ? userMsg.activityName
                          : ""
                      }}
                    </div>

                    <div>
                      <el-image
                        style="
                          width: 30px;
                          height: 50px;
                          float: left;
                          padding: 3px;
                        "
                        :src="iconMsg"
                        fit="contain"
                      ></el-image>
                      <div
                        style="
                          float: left;
                          line-height: 50px;
                          padding-left: 10px;
                        "
                      >
                        报名信息/Registration information
                      </div>
                      <div style="clear: both"></div>
                    </div>

                    <div v-if="userMsg.approveStatus === '审批通过'">
                      <el-image
                        style="
                          width: 30px;
                          height: 50px;
                          float: left;
                          padding: 3px;
                        "
                        :src="iconTicket"
                        fit="contain"
                      ></el-image>
                      <div
                        style="
                          float: left;
                          line-height: 50px;
                          padding-left: 10px;
                        "
                      >
                        入场票/Event pass
                      </div>
                      <div style="clear: both"></div>
                    </div>
                  </div>

                  <div
                    :class="btnBgColor(userMsg.approveStatus)"
                    v-if="userMsg.approveStatus === '退款'"
                    @click="userShowMsg(userMsg)"
                    style="cursor: pointer"
                  >
                    查看详情/View details
                  </div>
                  <div
                    :class="btnBgColor(userMsg.approveStatus)"
                    v-else-if="userMsg.approveStatus === '审批通过'"
                    @click="userShowMsg(userMsg)"
                    style="cursor: pointer"
                  >
                    查看详情/View details
                  </div>
                  <div
                    :class="btnBgColor(userMsg.approveStatus)"
                    v-else-if="userMsg.approveStatus === '审批驳回'"
                    @click="userShowMsg(userMsg)"
                    style="cursor: pointer"
                  >
                    查看详情/View details
                  </div>
                  <div
                    :class="btnBgColor(userMsg.approveStatus)"
                    v-else-if="userMsg.approveStatus === '审批中'"
                  >
                    正在审核中/Under Review
                  </div>
                  <div
                    :class="btnBgColor(userMsg.approveStatus)"
                    v-else
                    @click="userShowMsg(userMsg)"
                    style="cursor: pointer"
                  >
                    查看详情/View details
                  </div>
                </div>
              </el-col>
            </el-row>
            <div
              v-else
              class="noData"
              v-loading="isLoading"
              element-loading-text="数据加载中..."
            ></div>
          </div>

          <div v-if="active === '2'">
            <div v-if="orderMsgList.length > 0">
              <div class="orderItem" v-for="order in orderMsgList">
                <div>
                  <div class="ticketMsg">
                    <span style="font-size: 11px">&nbsp;&nbsp;订单编号:</span>
                    <span style="font-weight: bold; font-size: 11px">{{
                      order.orderNum
                    }}</span>
                    <span
                      style="
                        float: right;
                        color: rgb(153, 155, 155);
                        font-size: 12px;
                      "
                      v-if="order.paidTime"
                      ><span style="margin: 0px 5px"> 支付时间</span
                      >{{ order.paidTime }}</span
                    >
                  </div>
                  <div style="padding: 5px; line-height: 25px">
                    <div style="padding: 5px; height: 85px">
                      <div
                        class="productIcon"
                        style="padding: 10px 0px"
                        @click="showOrder(order)"
                      >
                        <img src="@/assets/logo.svg" class="image" />
                        <div style="font-size: 14px; font-weight: bold">
                          {{ order.ordersDetails[0].product.name }}
                        </div>
                      </div>
                      <div
                        class="productAttribute"
                        v-if="order.paidAmount && order.orderStatus === '10'"
                        style="
                          border-left: 1px dashed #e3ecf8;
                          padding-left: 5px;
                        "
                        @click="showOrder(order)"
                      >
                        <div class="moneyShow" style="line-height: 80px">
                          <span style="font-size: 14px">应付¥</span>
                          <span style="font-size: 20px; font-weight: bold">{{
                            order.paidAmount
                          }}</span>
                        </div>
                      </div>
                      <div
                        class="productAttribute"
                        v-else
                        style="
                          border-left: 1px dashed #e3ecf8;
                          padding-left: 5px;
                        "
                        @click="showOrder(order)"
                      >
                        <div class="moneyShow" style="line-height: 40px">
                          <span
                            style="font-size: 14px"
                            v-if="order.orderStatus === '20'"
                            >实付¥</span
                          >
                          <span style="font-size: 20px; font-weight: bold">{{
                            order.paidAmount
                          }}</span>
                        </div>
                        <div
                          class="moneyShow"
                          style="
                            color: rgb(124, 123, 123);
                            text-decoration: line-through;
                            line-height: 40px;
                          "
                          v-if="order.paidAmount && order.orderStatus === '10'"
                        ></div>
                        <div
                          class="moneyShow"
                          style="
                            color: rgb(124, 123, 123);
                            text-decoration: line-through;
                          "
                          v-else-if="order.orderStatus === '20'"
                        >
                          <span style="font-size: 12px">原价¥</span>
                          <span style="font-size: 12px">{{
                            order.ordersDetails[0].product.price
                          }}</span>
                        </div>
                        <div
                          class="moneyShow"
                          style="color: rgb(124, 123, 123); line-height: 80px"
                          v-else-if="
                            order.orderStatus === '30' ||
                            order.orderStatus === '40'
                          "
                        >
                          <span style="font-size: 12px">原价¥</span>
                          <span style="font-size: 12px">{{
                            order.ordersDetails[0].product.price
                          }}</span>
                        </div>
                        <div
                          class="moneyShow"
                          style="color: rgb(124, 123, 123); line-height: 40px"
                          v-else
                        >
                          <span style="font-size: 12px">原价¥</span>
                          <span style="font-size: 12px">{{
                            order.ordersDetails[0].product.price
                          }}</span>
                        </div>
                      </div>
                      <div
                        class="productAttribute"
                        v-if="order"
                        style="
                          border-left: 1px dashed #e3ecf8;
                          padding-left: 5px;
                          line-height: 80px;
                          text-align: center;
                        "
                        @click="showOrder(order)"
                      >
                        <span
                          v-if="order.orderStatus === '10'"
                          style="color: red"
                          >等待付款
                          <PayTime
                            :data="order"
                            @payCallback="refrashOrderData"
                            style="display: inline"
                          />
                        </span>
                        <span v-if="order.orderStatus === '20'">已付款</span>
                        <span v-if="order.orderStatus === '30'">已取消</span>
                        <span v-if="order.orderStatus === '40'">交易关闭</span>
                        <span v-if="order.orderStatus === '50'">退款中</span>
                        <span v-if="order.orderStatus === '60'">退款完成</span>
                        <span v-if="order.orderStatus === '70'">退款失败</span>
                      </div>
                      <div
                        class="productAttribute"
                        v-if="order"
                        style="
                          border-left: 1px dashed #e3ecf8;
                          padding-left: 5px;
                          line-height: 80px;
                          text-align: center;
                        "
                        @click="showOrder(order)"
                      >
                        <div>数量*{{ order.ordersDetails[0].buyCounts }}</div>
                      </div>
                      <div
                        class="productAttribute"
                        v-if="order"
                        style="
                          border-left: 1px dashed #e3ecf8;
                          padding-left: 5px;
                          padding-top: 15px;
                          line-height: 25px;
                          text-align: center;
                          min-width: 200px;
                        "
                        @click="showOrder(order)"
                      >
                        <div>
                          <div style="color: rgb(153, 155, 155)">下单时间</div>
                          {{ order.createTime }}
                        </div>
                      </div>
                      <div
                        class="productAttribute"
                        v-if="order.sourceId"
                        style="
                          border-left: 1px dashed #e3ecf8;
                          padding-left: 5px;
                          padding-top: 15px;
                          line-height: 25px;
                          text-align: center;
                          min-width: 200px;
                        "
                        @click="showOrder(order)"
                      >
                        <div>
                          <div style="color: rgb(153, 155, 155)">订单来源</div>
                          {{ order.sourceId === 1 ? "PC端" : "手机端" }}
                        </div>
                      </div>
                    </div>

                    <div style="clear: both"></div>
                    <div style="margin: auto; text-align: right">
                      <el-popconfirm
                        title="确定取消该订单吗？"
                        @confirm="cancelOrder(order)"
                      >
                        <el-button
                          v-if="order.orderStatus === '10'"
                          size="mini"
                          round
                          slot="reference"
                          >取消订单</el-button
                        >
                      </el-popconfirm>
                      <el-button
                        size="mini"
                        type="primary"
                        plain
                        round
                        @click="showOrder(order)"
                        style="margin-left: 10px"
                        >查看订单</el-button
                      >
                      <el-button
                        v-if="order.paidAmount && order.orderStatus === '10'"
                        size="mini"
                        round
                        @click="submitOrder(order)"
                        type="success"
                        plain
                        >去付款</el-button
                      >
                      <el-button
                        v-else-if="order.orderStatus === '10'"
                        size="mini"
                        round
                        @click="submitOrder(order)"
                        type="danger"
                        plain
                        >去结算</el-button
                      >
                      <el-popconfirm
                        title="确定删除该订单吗？"
                        @confirm="deleteOrder(order)"
                      >
                        <el-button
                          v-if="
                            order.orderStatus === '30' ||
                            order.orderStatus === '40'
                          "
                          plain
                          size="mini"
                          round
                          >删除订单</el-button
                        >
                      </el-popconfirm>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div
              v-else
              class="noData"
              v-loading="isLoading"
              element-loading-text="数据加载中..."
            ></div>
          </div>
        </div>

        <div v-if="showType === 2" class="personalCenter">
          <div class="stepsMain">
            <el-steps :active="showStep" align-center style="padding: 30px">
              <el-step
                title="选票"
                :description="showStep > 1 ? '已完成' : '待选择'"
              ></el-step>
              <el-step
                title="填写信息"
                :description="showStep > 2 ? '已完成' : '待填写'"
              ></el-step>
              <el-step
                title="支付"
                :description="showStep > 3 ? '已完成' : '待支付'"
              ></el-step>
              <el-step
                title="审核"
                :description="showStep > 4 ? '已完成' : '待审核'"
              ></el-step>
              <el-step
                title="报名成功"
                :description="showStep > 5 ? '已完成' : '待审核'"
              ></el-step>
            </el-steps>
          </div>

          <div v-if="showStep === 3">
            <div class="stepTitle">支付</div>
            <OrderPay
              @cancelPay="cancelPay"
              :orderNum="selectedOrder"
              @paySuccess="paySuccess"
              @callbackOrderExpire="callbackOrderExpire"
            />
          </div>
          <div v-if="showStep === 4">
            <div class="stepTitle">审核</div>
            <div class="approveMsg">
              <div style="font-size: 22px; padding: 30px">
                支付完成！请等待审核通过...
              </div>
              <div>
                <el-button type="primary" @click="toUserCenter"
                  >跳转个人中心</el-button
                >
              </div>
            </div>
          </div>
        </div>

        <el-dialog :visible.sync="showOrderCode" top="20px">
          <el-tabs v-model="activeName">
            <el-tab-pane label="报名资料/Registration Information" name="1" if="regInfo">
              <el-descriptions
                :column="2"
                size="medium"
                border
                style="padding: 10px; min-height: 300px"
              >
                <el-descriptions-item
                  v-for="(val, key, index) in regInfo"
                  v-if="val"
                  :key="index"
                  :label="key"
                  >{{ val }}</el-descriptions-item
                >
              </el-descriptions>
            </el-tab-pane>

            <el-tab-pane label="入场券/Event Pass" name="2" v-if="codeMsg">
              <div class="admissionTicket">
                <div>
                  <el-image
                    :src="imgUrl"
                    fit="cover"
                    style="
                      border-radius: 10px;
                      overflow: hidden;
                      height: 300px;
                      width: 700px;
                    "
                  />

                  <div
                    style="margin: auto; text-align: left; line-height: 28px"
                  >
                    <!-- <div class="codeTitle">{{ activeMsg.activityName }}</div> -->
                         <div class="codeTitle">{{ codeMsg.activityName }}</div>
                    <div>
                      <!-- {{
                        activeMsg
                          ? activeMsg.startDate + " ~ " + activeMsg.endDate
                          : ""
                      }} -->
                        {{
                        codeMsg
                          ? codeMsg.activityStartDate + " ~ " + codeMsg.activityEndDate
                          : ""
                      }}
                    </div>
                    <div class="codeTitle">地点/Location</div>
                       <div>{{ codeMsg.activityPlace }}</div>
                    <!-- <div>{{ activeMsg.activityPlace }}</div> -->
                    <div class="codeTitle">签到码/Ticket Code</div>
                    <div>
                      {{
                        codeMsg && codeMsg.signCode ? codeMsg.signCode : null
                      }}
                    </div>
                    <div class="codeTitle">电子票/E-Ticket</div>
                    <div
                      style="
                        margin: auto;
                        text-align: center;
                        padding-top: 10px;
                      "
                    >
                      <img
                        v-if="codeMsg && codeMsg.qrCode"
                        :src="codeMsg.qrCode"
                        class="image"
                        style="
                          padding-top: 0px;
                          border-radius: 5px;
                          padding-bottom: 20px;
                        "
                      />
                      <div v-else>暂无数据</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-dialog>

        <el-dialog :visible.sync="showOrderDetail" top="20px" title="订单信息">
          <div style="min-height: 300px">
            <el-descriptions
              :column="2"
              size="medium"
              border
              style="padding: 10px"
              v-if="orderUserData"
            >
              <el-descriptions-item key="0" label="活动编号">{{
                orderUserData.activityId
              }}</el-descriptions-item>
              <el-descriptions-item key="1" label="活动名称">{{
                orderUserData.activityName
              }}</el-descriptions-item>
              <el-descriptions-item
                v-if="orderUserData.name"
                key="2"
                label="姓名"
                >{{ orderUserData.name }}</el-descriptions-item
              >
              <el-descriptions-item
                v-if="orderUserData.phone"
                key="3"
                label="手机号"
                >{{ orderUserData.phone }}</el-descriptions-item
              >
              <el-descriptions-item
                v-if="orderUserData.email"
                key="4"
                label="邮箱"
                >{{ orderUserData.email }}</el-descriptions-item
              >
              <el-descriptions-item
                v-if="orderUserData.userTypeName"
                key="5"
                label="用户类型"
                >{{ orderUserData.userTypeName }}</el-descriptions-item
              >
              <el-descriptions-item
                v-if="orderUserData.createTime"
                key="6"
                label="创建时间"
                >{{ orderUserData.createTime }}</el-descriptions-item
              >
            </el-descriptions>
            <div v-else v-loading="true" style="min-height: 300px"></div>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getRegistUserList,
  getUserType,
  orderList,
  orderRegList,
  orderDelete,
  regGetOne,
  orderCancel,
  activityRegInfo,
  getActivityImage,
} from "@/utils/apiManage";
import moment from "moment";
import OrderPay from "@/components/OrderPay";
import PayTime from "@/components/PayTimer";
import userLogo from "@/assets/user.svg";
import iconTicket from "@/assets/icon_ticket.svg";
import iconMsg from "@/assets/icon_msg.svg";
import mainkv from "@/assets/mainkv.jpg";
import defauleImgUrl from "@/assets/mainkv.jpg";
import NavMenu from "@/components/NavMenu/index.vue";
export default {
  name: "RegistView",
  props: {},
  components: {
    OrderPay,
    PayTime,
    NavMenu,
  },
  data() {
    return {
      userLogo: userLogo,
      iconTicket: iconTicket,
      iconMsg: iconMsg,
      mainkv,
      showStep: 3,
      showType: 1,
      regInfo: null,

      activeName: "1",

      userData: [],
      active: "1",
      orderType: "",
      showOrderDetail: false,
      showOrderUser: [],
      orderMsgList: [],
      orderUserData: null,
      codeMsg: null,
      showOrderCode: false,
      activeMsg: JSON.parse(localStorage.getItem("activity")),
      showPayPage: false,
      showPay: false,
      selectedOrder: null,
      isLoading: false,
      imgUrl: "",
    };
  },
  destroyed() {
    localStorage.removeItem("showOrder");
  },
  mounted() {
    if (
      localStorage.getItem("showOrder") === true ||
      localStorage.getItem("showOrder") === "true"
    ) {
      this.active = "2";
    } else {
      this.active = "1";
    }
    this.refrashData();
  },
  created() {},
  methods: {
    getActivityImageFront(activityNo) {
      return getActivityImage(activityNo).then((res) => {
        if (res.code == 200 && res.data) {
          this.imgUrl =
            res.data.imageUrl && res.data.imageUrl != ""
              ? res.data.imageUrl
              : defauleImgUrl;
        } else {
          this.imgUrl = defauleImgUrl;
        }
      });
    },
    callbackOrderExpire() {
      this.showType = 1;
      this.refrashData();
    },
    toUserCenter() {
      this.showType = 1;
      this.refrashData();
    },
    paySuccess() {
      this.showStep = 4;
    },
    cancelPay() {
      this.showType = 1;
      this.showStep = 3;
      localStorage.setItem("showOrder", true);
      this.refrashData();
    },
    showOrder(record) {
      this.orderUserData = null;
      this.showOrderDetail = true;
      return orderRegList({ orderDetailsId: record.ordersDetails[0].id }).then(
        (res) => {
          if (res && res.data) {
            this.orderUserData = res.data[0];
          } else {
            this.$message({
              message: JSON.stringify(res),
              type: "warning",
              offset: 60,
            });
          }
        }
      );
    },
    userShowMsg(userMsg) {
      this.codeMsg = null;
      this.activeName = "1";
      this.showOrderCode = true;

      if (userMsg.approveStatus === "审批通过") {
        this.getActivityImageFront(userMsg.activityId);
        regGetOne(userMsg.id).then((res) => {
          if (res && res.data) {
            this.codeMsg = res.data;
            this.codeMsg.activityName = userMsg.activityName;
            this.codeMsg.activityPlace = userMsg.activityPlace;
            this.codeMsg.activityStartDate = userMsg.activityStartDate;
            this.codeMsg.activityEndDate = userMsg.activityEndDate;
          } else {
            this.$message({
              message: JSON.stringify(res),
              type: "warning",
              offset: 60,
            });
          }
        });
      }
      this.regInfo = null;
      activityRegInfo(userMsg.id).then((res) => {
        if (res && res.data) {
          this.regInfo = res.data;
        } else {
          this.$message({
            message: JSON.stringify(res),
            type: "warning",
            offset: 60,
          });
        }
      });
    },
    canback() {
      localStorage.removeItem("redirectType");
      this.$router.replace({ path: "/" });
    },
    getViewList() {
      this.isLoading = true;
      this.userData = [];
      return getRegistUserList()
        .then((res) => {
          this.isLoading = false;
          if (res && res.data) {
            this.userData = res.data;
          } else {
            this.$message({
              message: JSON.stringify(res),
              type: "warning",
              offset: 60,
            });
          }
        })
        .catch((res) => {
          this.$message({
            message: JSON.stringify(res),
            type: "warning",
            offset: 60,
          });
        });
    },

    getUserTypeName(value) {
      if (this.options && this.options.length > 0) {
        let items = this.options.filter((val) => {
          return val.value === value;
        });
        if (items && items.length > 0) {
          return items[0].label;
        } else {
          return "";
        }
      } else {
        return "";
      }
    },
    getUserType() {
      return getUserType().then((res) => {
        this.getViewList();
        if (res && res.data && res.data.length > 0) {
          this.options = [];
          res.data.map((item) => {
            this.options.push({
              value: item.dictValue,
              label: item.dictLabel,
            });
          });
        } else {
          this.$message({
            message: JSON.stringify(res),
            type: "warning",
            offset: 60,
          });
        }
      });
    },
    //获取订单列表
    getOrderList() {
      this.isLoading = true;
      this.orderMsgList = [];
      return orderList({ orderStatus: this.orderType })
        .then((res) => {
          this.isLoading = false;
          if (res && res.data && res.data.length >= 0) {
            res.data.map((item, index) => {
              if (item.orderStatus === "10") {
                let start = moment();
                let end = moment(moment(item.createTime).add(15, "minutes"));
                let count = end.diff(start, "milliseconds");
                if (count > 0) {
                  return (item.countDownTime = count);
                } else {
                  return (item.orderStatus = "40");
                }
              }
            });
            res.data.map((item) => {
              item.createTime = item.createTime
                ? moment(item.createTime).format("YYYY-MM-DD HH:mm:ss")
                : null;
              item.paidTime = item.paidTime
                ? moment(item.paidTime).format("YYYY-MM-DD HH:mm:ss")
                : null;
              return item;
            });
            this.orderMsgList = res.data;
          } else {
            this.$message({
              message: JSON.stringify(res),
              type: "warning",
              offset: 60,
            });
          }
        })
        .catch((res) => {
          this.$message({
            message: JSON.stringify(res),
            type: "warning",
            offset: 60,
          });
        });
    },
    refrashOrderData() {
      this.refrashData();
    },
    //刷新数据
    refrashData() {
      this.showPay = false;
      if (this.active === "2") {
        this.getOrderList();
      } else {
        this.getViewList();
      }
    },
    //取消订单
    cancelOrder(order) {
      order.createTime = null;
      return orderCancel(order).then((res) => {
        if (res) {
          this.$message({
            message: "订单取消成功！",
            type: "success",
            offset: 60,
          });
          this.refrashData();
        } else {
          this.$message({
            message: JSON.stringify(res),
            type: "warning",
            offset: 60,
          });
        }
      });
    },
    //删除订单
    deleteOrder(order) {
      return orderDelete(order.orderNum).then((res) => {
        if (res) {
          this.$message({
            message: "删除订单成功！",
            type: "success",
            offset: 60,
          });
        } else {
          this.$message({
            message: JSON.stringify(res),
            type: "warning",
            offset: 60,
          });
        }
        this.refrashData();
      });
    },
    orderTypeChange() {
      this.refrashData();
    },
    submitOrder(order) {
      this.selectedOrder = order.orderNum;
      this.showType = 2; //显示支付页面
      this.showStep = 3; //支付步骤
    },

    stateShow(approveStatus) {
      if (approveStatus === "审批中") {
        return "registUserName state_pendding";
      } else if (approveStatus === "审批驳回") {
        return "registUserName state_fail";
      } else if (approveStatus === "审批通过") {
        return "registUserName state_success";
      } else if (approveStatus === "退款") {
        return "registUserName state_refund";
      } else {
        return "registUserName";
      }
    },
    btnBgColor(approveStatus) {
      if (approveStatus === "审批中") {
        return "bigBtn btnBgBlue";
      } else if (approveStatus === "审批驳回") {
        return "bigBtn btnBgRed";
      } else if (approveStatus === "审批通过") {
        return "bigBtn btnBgGreen";
      } else if (approveStatus === "退款") {
        return "bigBtn btnBgGrap";
      } else {
        return "bigBtn";
      }
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
body {
  padding-right: 0px !important;
  overflow: hidden;
}
.el-form-item__label:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}
.el-form-item__label {
  padding-right: 10px !important;
}
h3 {
  margin: 40px 0 0;
}
a {
  color: #42b983;
}
.registUserName {
  padding: 5px;
  height: 100px;
}
.state_success {
  background: url("../assets/state_success.svg") no-repeat;
  background-size: 100px 100px;
}
.state_fail {
  background: url("../assets/state_fail.svg") no-repeat;
  background-size: 100px 100px;
}
.state_pendding {
  background: url("../assets/state_pendding.svg") no-repeat;
  background-size: 100px 100px;
}
.state_refund {
  background: url("../assets/state_refund.svg") no-repeat;
  background-size: 100px 100px;
}
.noData {
  border-radius: 10px;
  text-align: center;
  font-size: 24px;
  height: 300px;
  overflow: hidden;
}
.productIcon {
  float: left;
}
.productAttribute {
  float: right;
  margin: auto;
  text-align: right;
  padding: 5px;
  min-width: 100px;
}
.payMoneyMsg {
  padding: 10px;
  margin: auto;
  text-align: right;
  font-size: 16px;
}
.codeTitle {
  font-size: 15px;
  font-weight: bold;
  color: #030b1a;
}
.moneyShow {
  color: red;
  padding: 0px;
}
.registEnter {
  width: 100%;
  background-color: #f3f8ff;
  margin: auto;
  padding: 0px;
  text-align: center;
}
.personalCenter {
  margin: auto;
  padding: 30px 50px 50px 50px;
  min-width: 1000px;
  max-width: 1200px;
  min-height: 500px;
}
.viewTicket {
  margin: 10px 0px;
  text-align: left;
  background-color: #ffffff;
  border-radius: 10px;
  overflow: hidden;
  min-width: 240px;
  font-size: 12px;
}
.userLogo {
  margin-top: 30px;
  margin-left: 40px;
  padding: 5px;
}
.bigBtn {
  color: #ffffff;
  margin: auto;
  text-align: center;
  /* border-radius: 10px; */
  line-height: 40px;
  font-size: 16px;
}
.btnBgBlue {
  background-color: #2c80ff;
}
.btnBgGrap {
  background-color: #cccccc;
}
.btnBgGreen {
  background-color: #7cce3b;
  /* background-color: #FFAE34; */
}
.btnBgRed {
  background-color: #f95b4a;
}
.admissionTicket {
  margin: auto;
  text-align: 80%;
}
.stepsMain {
  width: 100%;
  background: #ffffff;
  border-radius: 10px;
}
.orderItem {
  margin: 10px;
  background: #ffffff;
  text-align: left;
  border-radius: 10px;
  overflow: hidden;
  font-size: 12px;
}
.ticketMsg {
  padding: 10px;
  background-color: #f5f5f5;
}
.stepTitle {
  margin: auto;
  text-align: left;
  padding: 20px 30px;
  font-size: 30px;
  font-weight: bold;
  color: #03081a;
}

.approveMsg {
  background: #ffffff;
  border-radius: 10px;
  padding: 30px;
}
</style>
