<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="邮件日志" name="0" :disabled="flag1">
        <eamilLog v-if="activeName == 0"></eamilLog>
      </el-tab-pane>
      <el-tab-pane label="短信日志" name="1" :disabled="flag2">
        <sysLog v-if="activeName == 1"></sysLog>
      </el-tab-pane>
    </el-tabs>
     <div
      slot="footer"
      class="dialog-footer"
      style="text-align: center; margin-top: 30px"
    >
      <el-button @click="goBack">取 消</el-button>
    </div>
  </div>
</template>

<script>
import eamilLog from "./eamilLog.vue";
import sysLog from "./sysLog.vue";
export default {
  name: "shareMessage",
  components: {
    eamilLog,
    sysLog,
  },
  data() {
    return {
      activeName: "0",
      flag1: false,
      flag2: true,
    };
  },

  beforeCreate() {},
  created() {
    setTimeout(() => {
      this.flag2 = false;
    }, 1000);
  },
  mounted() {},
  update() {},
  methods: {
      goBack(){
            this.$parent.HandleMessageCancel();
      },

    handleClick(tab) {
      this.activeName = tab.index;
      if (tab.index == 0) {
        this.flag2 = true;
        setTimeout(() => {
          this.flag2 = false;
        }, 1000);
      }
      if (tab.index == 1) {
        this.flag1 = true;
        setTimeout(() => {
          this.flag1 = false;
        }, 1000);
      }
    },
  },
};
</script>