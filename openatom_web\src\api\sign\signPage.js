import request from "@/utils/request";
import qs from "qs";
// 签到
export function signActive(data) {
  return request({
    url: "/sign/sign",
    method: "post",
    data,
  });
}

// 已签到
export function queryHasSignedList(data) {
  return request({
    url: "/sign/list",
    method: "post",
    data: qs.stringify(data),
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}
