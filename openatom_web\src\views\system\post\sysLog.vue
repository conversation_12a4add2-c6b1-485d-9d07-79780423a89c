<template>
  <div class="app-container">
    <div>
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="68px"
        @selection-change="handleSelectionChange"
      >
        <el-form-item label="姓名">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入姓名"
            clearable
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            v-model="queryParams.phone"
            placeholder="手机号"
            clearable
          />
        </el-form-item>
        <el-form-item label="发送状态">
          <el-select
            v-model="queryParams.status"
            placeholder="发送状态"
            clearable
          >
            <el-option key="0" label="成功" value="success" />
            <el-option key="1" label="失败" value="fail" />
          </el-select>
        </el-form-item>
        <el-form-item label="通知类型">
          <el-select
            v-model="queryParams.smsType"
            placeholder="通知类型"
            clearable
          >
            <el-option label="报名成功(包含电子流)" :value="1" />
            <el-option label="报名成功(不包含电子流)" :value="5" />
            <el-option label="报名未通过" :value="2" />
            <el-option label="活动提醒" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="回执状态">
          <el-select
            v-model="queryParams.statusOfIsp"
            placeholder="通知类型"
            clearable
          >
            <el-option key="0" label="成功" value="success" />
            <el-option key="1" label="失败" value="fail" />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="tableData1">
        <el-table-column
          label="序号"
          align="center"
          prop="key"
          key="序号"
          :show-overflow-tooltip="true"
          width="100px"
        />

        <el-table-column
          label="活动"
          align="center"
          prop="activity_id"
          key="activity_id"
          :show-overflow-tooltip="true"
        >
          {{ this.active_Name }}
        </el-table-column>

        <el-table-column
          label="人员姓名/账号"
          align="center"
          prop="name"
          key="name"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="手机号"
          align="center"
          prop="phone"
          key="phone"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="发送状态"
          align="center"
          prop="state_code_of_hw"
          key="state_code_of_hw"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row }">
            {{ row.state_code_of_hw == "000000" ? "成功" : "失败" }}
          </template>
        </el-table-column>
        <el-table-column
          label="失败原因"
          align="center"
          prop="descChOfHw"
          key="descChOfHw"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row }">
            {{
              row.descChOfHw && row.descChOfHw.indexOf("成功") > -1
                ? "--"
                : row.descChOfHw
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="通知类型"
          align="center"
          prop="sms_type"
          key="sms_type"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row }">
            {{
              row.sms_type == 1
                ? "报名成功(包含电子票)"
                : row.sms_type == 5
                ? "报名成功(不包含电子票)"
                : row.sms_type == 2
                ? "报名未通过"
                : row.sms_type == 3
                ? "活动提醒"
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="回执状态"
          align="center"
          prop="state_code_of_isp"
          key="state_code_of_isp"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row }">
            {{
              row.state_code_of_isp && row.state_code_of_isp === "DELIVRD"
                ? "成功"
                : !row.state_code_of_isp
                ? ""
                : "失败"
            }}
            <!-- {{
              row.state_code_of_isp? row.state_code_of_isp == "DELIVRD"
                ? "成功"
                : "失败":""
            }} -->
          </template>
        </el-table-column>
        <el-table-column
          label="回执失败原因"
          align="center"
          prop="descChOfISP"
          key="descChOfISP"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row }">
            {{
              row.descChOfISP && row.descChOfISP.indexOf("成功") > -1
                ? "--"
                : row.descChOfISP
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="发送时间"
          align="center"
          prop="create_time"
          key="create_time"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="100"
        >
          <!-- v-hasPermi="['system:post:edit']" -->
          <!-- v-if="
                row.state_code_of_hw == '000000' &&
                row.state_code_of_isp &&
                row.state_code_of_isp != 'DELIVRD'
              " -->
          <template slot-scope="{ row }">
            <el-button
              v-if="
                row.state_code_of_hw != '000000' ||
                row.state_code_of_isp != 'DELIVRD'
              "
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(row)"
              >短信重发</el-button
            >
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-if="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getTableDataByEmail"
      />
      <el-dialog
        custom-class="selfDialog"
        :title="'短信重发'"
        :visible.sync="sysUserOpen"
        append-to-body
        :destroy-on-close="true"
      >
        <div>
          <div style="margin-bottom: 10px">
            <span style="font-weight: bold">{{
              userList.length > 0 ? userList[0].name : ""
            }}</span
            ><span style="font-weight: bold; padding: 0 10px">{{
              userList.length > 0 ? userList[0].phone : ""
            }}</span
            >接收&nbsp;&nbsp;"<span style="font-weight: bold">{{
              userList.length > 0
                ? userList[0].sms_type == 1
                  ? "报名成功(包含电子票)"
                  : userList[0].sms_type == 5
                  ? "报名成功(不包含电子票)"
                  : userList[0].sms_type == 2
                  ? "报名未通过"
                  : userList[0].sms_type == 3
                  ? "活动提醒"
                  : ""
                : ""
            }}</span
            >"&nbsp;&nbsp;通知消息全部短信记录如下，请确认是否再次发送通知！
          </div>
          <el-table
            :data="sysData"
            size="mini"
            v-loading="sysLoading"
            style="max-height: 500px; overflow: auto"
          >
            <!-- <el-table-column
              label="序号"
              align="center"
              prop="key"
              key="序号"
              :show-overflow-tooltip="true"
              width="100px"
            /> -->
            <el-table-column
              label="人员姓名/账号"
              align="center"
              prop="name"
              key="name"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="手机号"
              align="center"
              prop="phone"
              key="phone"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="发送状态"
              align="center"
              prop="state_code_of_hw"
              key="state_code_of_hw"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                {{ row.state_code_of_hw == "000000" ? "成功" : "失败" }}
              </template>
            </el-table-column>
            <el-table-column
              label="失败原因"
              align="center"
              prop="descChOfHw"
              key="descChOfHw"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                {{
                  row.descChOfHw && row.descChOfHw.indexOf("成功") > -1
                    ? "--"
                    : row.descChOfHw
                }}
              </template>
            </el-table-column>
            <el-table-column
              label="通知类型"
              align="center"
              prop="sms_type"
              key="sms_type"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                {{
                  row.sms_type == 1
                    ? "报名成功(包含电子票)"
                    : row.sms_type == 5
                    ? "报名成功(不包含电子票)"
                    : row.sms_type == 2
                    ? "报名未通过"
                    : row.sms_type == 3
                    ? "活动提醒"
                    : ""
                }}
              </template>
            </el-table-column>
            <el-table-column
              label="回执状态"
              align="center"
              prop="state_code_of_isp"
              key="state_code_of_isp"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                {{
                  row.state_code_of_isp && row.state_code_of_isp == "DELIVRD"
                    ? "成功"
                    : "失败"
                }}
              </template>
            </el-table-column>
            <el-table-column
              label="回执失败原因"
              align="center"
              prop="descChOfISP"
              key="descChOfISP"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                {{
                  row.descChOfISP && row.descChOfISP.indexOf("成功") > -1
                    ? "--"
                    : row.descChOfISP
                }}
              </template>
            </el-table-column>
            <el-table-column
              label="发送时间"
              align="center"
              prop="create_time"
              key="create_time"
              width="150px"
              :show-overflow-tooltip="true"
            />
          </el-table>
          <pagination
            v-if="sysTotal > 0"
            :total="sysTotal"
            :page.sync="sysPageNum"
            :limit.sync="sysPageSize"
            @pagination="getsysTable"
          />
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="sysuserSubForm"
            :loading="btnloading"
            >确 定</el-button
          >
          <el-button @click="sysUsercancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import request from "@/utils/request";
export default {
  name: "sysLog",

  data() {
    return {
      activeName: "0",
      queryParams: {
        name: undefined,
        phone: undefined,
        receiveMailAddress: undefined,
        status: undefined,
        pageNum: 1,
        pageSize: 10,
        activityId: JSON.parse(sessionStorage.getItem("activity")).activityId,
        smsType: undefined,
        isAnotherRecord: "OK",
        statusOfIsp: undefined,
      },
      tableData1: [],
      loading: false,
      total: 0,
      active_Name: "",
      sysUserOpen: false,
      userList: [],
      btnloading: false,
      sysData: [],
      sysLoading: false,
      sysPageNum: 1,
      sysPageSize: 10,
      sysTotal: 0,
    };
  },

  beforeCreate() {},
  created() {
    this.getTableDataByEmail();
    this.active_Name = JSON.parse(
      sessionStorage.getItem("activity")
    ).activityName;
  },
  mounted() {},
  update() {},
  methods: {
    sysuserSubForm() {
      this.btnloading = true;
      request({
        url: "/sms/sendSms",
        method: "post",
        //data: this.queryParams,
        data: {
          smsType: this.userList[0].sms_type,
          phone: this.userList[0].phone,
          name: this.userList[0].name,
          templateid: this.userList[0].template_id,
          templateParas:
            this.userList[0].sms_param && this.userList[0].sms_param != ""
              ? JSON.parse(this.userList[0].sms_param)
              : [],
          activityId: this.userList[0].activity_id,
        },
      })
        .then((response) => {
          if (response.code == 200) {
            this.$message.success("操作成功");
            this.btnloading = false;
            this.sysUsercancel();
            this.getTableDataByEmail();
          } else {
            this.$message.error("操作失败");
            this.btnloading = false;
          }
        })
        .catch((err) => {
          this.btnloading = false;
          console.log(err);
        });
    },
    handleEdit(row) {
      this.sysUserOpen = true;
      this.userList = [{ ...row }];
      this.getsysTable();
    },
    sysUsercancel() {
      this.sysUserOpen = false;
      this.userList = [];
    },
    getsysTable() {
      this.sysLoading = true;
      let obj = {
        // status: "success",
        activityId: this.userList[0].activity_id,
        smsType: this.userList[0].sms_type,
        isAnotherRecord: "OK",
        // statusOfIsp: "fail",
        pageNum: this.sysPageNum,
        pageSize: this.sysPageSize,
        phone: this.userList[0].phone,
        name: this.userList[0].name,
      };
      let formData = new window.FormData();
      Object.keys(obj).map((item) => {
        if (obj[item]) {
          formData.append(item, obj[item]);
        }
      });
      request({
        url: "/sms/smsRecordList",
        method: "post",
        data: formData,
      })
        .then((response) => {
          if (response && response.rows && response.rows.length > 0) {
            response.rows.map((item, index) => {
              return (item.key = index + 1);
            });
            this.sysData = response.rows;

            this.sysTotal = response.total;
          } else {
            this.sysData = [];
            this.sysTotal = 0;
          }
          this.sysLoading = false;
        })
        .catch((err) => {
          this.sysLoading = false;
          console.log(err);
        });
    },
    getTableDataByEmail() {
      this.total = 0;
      this.loading = true;
      let formData = new window.FormData();
      Object.keys(this.queryParams).map((item) => {
        if (this.queryParams[item]){
          formData.append(item, this.queryParams[item]);
         
        }
      });
      //  if (this.queryParams.smsType) {
      //       let active = JSON.parse(sessionStorage.getItem("activity"));
      //       if (active.sendType == 2&&this.queryParams.smsType==1) {
      //         formData.append("smsType", 5);
      //       }else{
      //          formData.append("smsType", this.queryParams.smsType);
      //       }
      //     }
      request({
        url: "/sms/smsRecordList",
        method: "post",
        //data: this.queryParams,
        data: formData,
      })
        .then((response) => {
          if (response && response.rows && response.rows.length > 0) {
            response.rows.map((item, index) => {
              return (item.key = index + 1);
            });
            this.tableData1 = response.rows;

            this.total = response.total;
          } else {
            this.tableData1 = [];
          }
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
          console.log(err);
        });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getTableDataByEmail();
    },
    handleSelectionChange() {},
  },
};
</script>