<template>
  <div class="registEnter">


    <van-overlay :show="showLoading || isFormLoading" style="z-index: 99999; position: absolute;height: 100%;">
      <div class="wrapper">
        <van-loading type="spinner" color="#1989fa" class="block" />
      </div>
    </van-overlay>

    <div>
      <!-- 第一步 -->
      <div v-if="step===1">
        <van-nav-bar
          title="选票"
          left-text="返回"
          left-arrow
          @click-left="canback"
        />
        <div>
            <div v-for="(item, index) in ticketList" :key="index" :offset="index > 0 ? 2 : 0" style="margin:auto;text-align:center">
              <div v-if="item.stock<=0 || item.isSales === 0" class="ticket_noOpen_bg" style="opacity: 0.5">
                <div style="margin: auto; text-align: center; width: 100%;padding-top:20px;">
                  <div class="ticketMsg_left" style="min-width:235px;padding-left:30px">
                    <img src="@/assets/logo.svg" class="image">
                    <div class="ticketName">{{ item.name }}</div>
                    <div style="width:100%">
                      <div class="ticketMsg_left" style="color:#A9B6C8">
                        <van-icon name="warning-o" />权益

                      </div>
                      <div style="padding-left:10px;float:right;font-size:22px">
                        {{ item.isSales === 0 ? "暂未开售" :"已售罄" }}
                      </div>
                      <div style="clear:both"></div>
                    </div>
                  </div>
                  <div class="ticketMsg_right">
                    <img src="@/assets/noSelect.svg" class="image">
                  </div>
                  <div style="clear:both"></div>

                </div>
              </div>

              <div v-else class="ticket_bg">
                <div style="margin: auto; text-align: center; width: 100%;padding-top:20px;">
                  <div class="ticketMsg_left" style="min-width:235px;padding-left: 30px;">
                    <img src="@/assets/logo.svg" class="image">
                    <div class="ticketName">{{ item.name }}</div>
                    <div style="width:100%">
                      <div class="ticketMsg_left" style="color:#A9B6C8" @click="viewRule(item)">
                        <van-icon name="warning-o" />查看权益
                      </div>
                      <!-- <div style="padding-left:10px;float:right" v-if="item.isActivityPrice === 1">
                        <span style="font-size: 12px; color:#FF8300">¥</span>
                        <span style="font-size: 22px; color:#FF8300">{{ item.activityPrice }}</span>
                        <span style="font-size: 11px;text-decoration:line-through;color:#303133">{{ "&nbsp;原价:"+item.price+"&nbsp;" }}</span>
                      </div>
                      <div style="padding-left:10px;float:right" v-else>
                        <span style="font-size: 12px; color:#FF8300">¥</span>
                        <span style="font-size: 22px; color:#FF8300">{{ item.price }}</span>
                      </div> -->
                      <div style="clear:both"></div>
                    </div>
                  </div>
                  <div class="ticketMsg_right" @click="selectedTicket(item.id)">
                    <img v-if="ticketType === item.id " src="@/assets/selected.svg" class="image"/>
                    <img v-else src="@/assets/noSelect.svg" class="image"/>
                  </div>
                  <div style="clear:both"></div>

                </div>
              </div>
            </div>
        </div>
        <div style="padding: 10px;">
          <el-button type="primary" @click="clickNext">下一步</el-button>
        </div>
      </div>
      <!-- 第二步 -->
      <div v-if="step === 2" style="min-height: 500px;">
        <van-nav-bar
          title="填写报名信息/Information"
          left-text="返回/Back"
          left-arrow
          @click-left="canback"
        />
        <div class="formEdite" v-if="!isFormLoading" >
          <el-form :model="regFormData" ref="regFormData" label-width="110px" size="small" label-position="top">

            <el-form-item
              label="用户类型"
              prop="userType"
              v-if="this.baseFormParam.userType.open"
              :rules="{required: this.baseFormParam.userType.open, message: '用户类型不能为空'} "
            >
              <el-select
                placeholder="请选择类型"
                v-model="regFormData.userType"
                class="el-input"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"/>
                </el-select>
            </el-form-item>

            <el-form-item
              label="姓名/Name"
              prop="userName"
              v-if="this.baseFormParam.name.open"
              :rules="{required: this.baseFormParam.name.required, message: '姓名不能为空'} "
            >
              <el-input placeholder="请输入姓名/Please enter your name." v-model="regFormData.userName" size="small"/>
            </el-form-item>

            <el-form-item
              label="性别"
              prop="userSex"
              v-if="this.baseFormParam.sex.open"
              :rules="{required: this.baseFormParam.sex.required, message: '性别不能为空'} "
            >
            <el-radio-group v-model="regFormData.userSex" size="small">
              <el-radio label="男" border style="width: 90px;margin-right: 10px;">男</el-radio>
              <el-radio label="女" border style="width: 90px;">女</el-radio>
            </el-radio-group>
            </el-form-item>

            <el-form-item
              label="手机号/Phone"
              prop="userPhone"
              v-if="this.baseFormParam.phone.open"
              :rules="[
                {required: this.baseFormParam.phone.required, message: '手机号不能为空'},
              ]"
            >
              <el-input v-model="regFormData.userPhone" placeholder="请输入手机号/Please enter your phone number." size="small"></el-input>
            </el-form-item>

            <el-form-item
              label="国籍"
              prop="userNational"
              v-if="this.baseFormParam.nationality.open"
              :rules="{required: this.baseFormParam.nationality.required, message: '国籍不能为空'} "
            >
              <el-radio-group v-model="regFormData.userNational" size="small">
                <el-radio label="中国" border style="width: 90px;margin-right: 10px;">中国</el-radio>
                <el-radio label="外国籍" border style="width: 90px;">外国籍</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item
              label="省份/市"
              v-if="this.baseFormParam.provinceCity.open"
              prop="userCity"
              :rules="{required: this.baseFormParam.provinceCity.required, message: '国籍不能为空'} "
            >
              <el-cascader
                placeholder="请选择省份"
                v-model="regFormData.userCity"
                size="small"
                :options="cityOption"/>
            </el-form-item>

            <el-form-item
              label="证件类型"
              prop="cardType"
              v-if="this.baseFormParam.idCardType.open"
              :rules="{required: this.baseFormParam.idCardType.required, message: '证件类型不能为空'} "
            >
              <el-select placeholder="请选择证件类型" class="el-input" size="small" v-model="regFormData.cardType">
                <el-option
                  v-for="item in cardOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              v-if='regFormData.cardType == "1" && this.baseFormParam.idCardType.open'
              label="身份证号"
              prop="idCard"
              :rules="[
                {required: this.baseFormParam.idCardType.required, message: '身份证号不能为空'},
                {validator:validatorCard,message:'身份证号不正确'}
              ]"
            >
              <el-input placeholder="请输入证件号码" size="small" v-model="regFormData.idCard"></el-input>
            </el-form-item>
            <el-form-item
              v-else-if="this.baseFormParam.idCardType.open"
              label="证件号码"
              prop="idCard"
              :rules="{required: this.baseFormParam.idCardType.required, message: '证件号码不能为空'} "
            >
              <el-input placeholder="请输入证件号码" size="small" v-model="regFormData.idCard"></el-input>
            </el-form-item>

            <el-form-item
              label="邮箱地址/Email"
              prop="email"
              v-if="this.baseFormParam.email.open"
              :rules="[
                {required: this.baseFormParam.email.required, message: '邮箱地址不能为空'},
                { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
              ] "
            >
              <el-input placeholder="请输入邮箱地址/Please enter a valid email address." size="small" v-model="regFormData.email"></el-input>
            </el-form-item>

            <el-form-item
              label="单位名称"
              prop="company"
              v-if="this.baseFormParam.company.open"
              :rules="{required: this.baseFormParam.company.required, message: '单位名称不能为空'}"
            >
              <!-- <el-input placeholder="请输入单位名称" size="small" v-model="regFormData.company"></el-input> -->
              <el-autocomplete
                :style="{ width: '100%' }"
                size="small"
                v-model="regFormData.company"
                :fetch-suggestions="searchCompany"
                placeholder="请输入单位名称"
                :trigger-on-focus="false"
              ></el-autocomplete>
            </el-form-item>

            <el-form-item
              label="职务"
              prop="job"
              v-if="this.baseFormParam.job.open"
              :rules="{required: this.baseFormParam.job.required, message: '职务不能为空'} "
            >
              <el-input placeholder="请输入职务" size="small" v-model="regFormData.job"></el-input>
            </el-form-item>

          </el-form>

          <div id="form-create">
            <form-create v-model="fApi" :rule="formInfo" :option="option" :value.sync="value" ></form-create>
          </div>

          <div style="padding-top: 10px;" v-if="protocolMsg && protocolMsg.length>0">
            <div>
              <el-checkbox v-model="isChecked" />
              <span style="font-size: 12px;color: #808080;" @click="isChecked = !isChecked">
               勾选即代表您已充分理解并同意以下内容/By ticking the box, you are confirming that you have understood and agree to the following content.
              </span>
            </div>
            <div style="padding-left: 18px;" v-for=" (protocol,index) in protocolMsg">
              <a  style="color: #409eff; font-size: 12px;" @click="protocolClick(protocol)">{{protocol.protocolName}} </a>
            </div>
          </div>


          <div style="text-align: center;padding: 10px;">
            <el-button type="primary" v-if="isBuyTicket===1" @click="backTicket">上一步</el-button>
            <el-button type="primary" :disabled="(protocolMsg && protocolMsg.length>0 && !isChecked) || isLoading" @click="submitForm('regFormData')">提交/Submit</el-button>
          </div>
        </div>
      </div>
      <!-- 第三步 -->
      <van-popup
        v-model="showPay"
        closeable
        @close="cancelPay"
        position="bottom"
        :close-on-click-overlay="false"
        round
        :style="{ height: '70%' }"
      >
        <OrderPay  @callbackOrderExpire = "callbackOrderExpire" :orderNum="orderNum"  @callbackSuccess="callbackSuccess" />
      </van-popup>

    </div>

    <van-popup
      v-model="showProtocol"
      closeable
      round
      position="bottom"
      :style="{ height: '70%' }"
    >
      <div style="padding:30px 10px;text-align: left;">
        <div style="padding-top: 10px;padding-bottom: 10px; font-size: 18px; font-weight: bold; margin: auto; text-align: left;">{{ protocolName }}</div>
        <div v-html="htmlMsg"></div>
      </div>
    </van-popup>

    <van-popup
      v-model="showRule"
      closeable
      round
      position="bottom"
      :style="{ height: '70%' }"
    >
      <div style="padding: 30px 10px;text-align: left;">
        <div class="popupTitle">查看权益信息</div>
        <div v-html="ruleContent"></div>
      </div>
    </van-popup>

  </div>
</template>

<script>
import {
  formList,
  ticketList,
  getUserType,
  getCardType,
  orderCreate,
  saveNoTicket,
  orderGoPay,
  protocolGetList,
  getLastInfo,
  searchCompanyInfo,
} from '@/utils/apiManage'
import citys from '@/utils/city.js'
import IdentityCodeValid from '@/utils/checkIdent';
import {Dialog,Notify } from "vant"
import OrderPay from '@/components/OrderPay'
export default {
  name: 'UserRegist',
  props: {
  },
  components:{
    OrderPay
  },
  data(){
    return {
      isLoading:false,
      step:1,
      showPay:false,
      showRule:false,//是否显示权限信息
      activeMsg:null,
      fApi:{},
      ticketList:[],
      orderNum:null,
      ticketType:null,
      orderMsg:null,
      isChecked:false,//协议是否同意勾选
      protocolMsg:null,
      htmlMsg:null,
      ruleContent:null,
      showProtocol:false,
      formInfo:[],
      protocolName:"",
      isBuyTicket:null,
      value: {},
      option: {
          form:{
            size:'small',
            labelPosition:'top',
          },
          submitBtn:{
            show:false
          },
          row:{
            gutter:0
          }

      },
      cityOption:citys,//城市
      cardOption:[],//身份证类型
      options: [],//用户类型
      showLoading:false,
      regFormData:{},
      isFormLoading:true,
      baseFormParam:{
        userType:{open:false,required:false},
        name:{open:false,required:false},
        phone:{open:false,required:false},
        email:{open:false,required:false},
        sex:{open:false,required:false},
        nationality:{open:false,required:false},
        provinceCity:{open:false,required:false},
        idCardType:{open:false,required:false},
        company:{open:false,required:false},
        job:{open:false,required:false},
      }

    }
  },
  async mounted() {
    let activity = JSON.parse(localStorage.getItem("activity"))
    this.isBuyTicket = activity.isBuyTicket
    if(this.isBuyTicket === 1){//购票模式
      this.getTicketList()
    }else{//无票模式
      this.step = 2
    }
    setTimeout(()=>{
      //查询动态表单数据
      this.getFormList()
      //查询用户类型
      this.getUserType()
      //查询票
      this.getCardType()
      //查询协议
      this.protocolGet()
    },1000)

    const lastInfo = await getLastInfo();

    lastInfo.data.userType && this.$set(this.regFormData, 'userType', lastInfo.data.userType);
    lastInfo.data.name && this.$set(this.regFormData, 'userName',  lastInfo.data.name);
    lastInfo.data.sex && this.$set(this.regFormData, 'userSex', lastInfo.data.userSex);
    lastInfo.data.phone && this.$set(this.regFormData, 'userPhone', lastInfo.data.phone);
    lastInfo.data.nationality && this.$set(this.regFormData, 'userNational', lastInfo.data.nationality);
    lastInfo.data.cardType && this.$set(this.regFormData, 'cardType', lastInfo.data.cardType);
    lastInfo.data.idCard && this.$set(this.regFormData, 'idCard', lastInfo.data.idCard);
    lastInfo.data.email && this.$set(this.regFormData, 'email', lastInfo.data.email);
    lastInfo.data.company && this.$set(this.regFormData, 'company', lastInfo.data.company);
    lastInfo.data.job && this.$set(this.regFormData, 'job', lastInfo.data.job);

    if (lastInfo.data.province && lastInfo.data.city) {
      this.$set(this.regFormData, 'userCity', [lastInfo.data.province, lastInfo.data.city]);
    }
  },
  methods:{
    canback(){
      localStorage.removeItem('redirectType')
      this.$router.replace({ path: '/'})
    },
    callbackOrderExpire(){
      localStorage.setItem('showOrder',true)
      localStorage.setItem("redirectType",2)
      this.$router.replace({ path: '/RegistView'})
    },

    //获取该活动下用户需要填写的动态表单设置数据
    getFormList(){
      this.isFormLoading = true
      return formList({activityId:localStorage.getItem("activityNo")}).then(res => {
        this.isFormLoading = false
        if(res && res.data){
          this.activeMsg = res.data
          res.data.formInfo[0].map((item)=>{
            this.baseFormParam[item.defaultName].open = item.open
            this.baseFormParam[item.defaultName].required = item.required
          })
          this.formInfo = res.data.formInfo[1]

        }else{
          Notify({ type: 'warning', message: JSON.stringify(res) });
        }
      })
    },
    //获取该活动下票的信息列表
    getTicketList(){
      this.showLoading = true
      return ticketList(localStorage.getItem("activityNo")).then(res => {
        if(res && res.data){
          this.ticketList = res.data
          this.showLoading = false
        }else{
          this.showLoading = false
          Notify({ type: 'warning', message: JSON.stringify(res) });
        }
      })
    },
    //查询证件类型列表
    getCardType(){
      return getCardType().then(res => {
        if(res && res.data && res.data.length>0){
          this.cardOption=[]
          res.data.map(item=>{
            this.cardOption.push({
              value: item.dictValue,
              label: item.dictLabel
            })
          })
        }else{
          Notify({ type: 'warning', message: JSON.stringify(res) });
        }

      })
    },
    //获取用户类型列表
    getUserType(){
      return getUserType().then(res => {
        if(res && res.data && res.data.length>0){
          this.options=[]
          res.data.map(item=>{
            this.options.push({
              value: item.dictValue,
              label: item.dictLabel
            })
          })
        }else{
          Notify({ type: 'warning', message: JSON.stringify(res) });
        }

      })
    },
    //查看协议内容
    protocolGet(){
      return protocolGetList(localStorage.getItem("activityNo")).then(res => {
        if(res && res.data ){
          this.protocolMsg = res.data
        }else{
          Notify({ type: 'warning', message: JSON.stringify(res) });
        }

      })
    },
    submitForm(formName) {
      if((this.ticketList && this.ticketList.length>0 && this.ticketList[0].activityid === localStorage.getItem("activityNo")) || this.isBuyTicket === -1){

        this.$refs[formName].validate((valid,msg) => {
          if (valid) {//验证通过
            this.fApi.submit(()=>{
              this.saveData()
            });
          } else {//验证不通过
            if(msg){
              let tipMsg = null
              for (const key in msg) {
                if(tipMsg === null){
                  tipMsg = msg[key][0].message
                }
              }
              if(tipMsg){
                Notify({ type: 'warning', message: tipMsg });
              }
            }
            document.body.scrollTop=0;
            document.documentElement.scrollTop = 0;
            this.fApi.submit();
            return false;
          }
        });
      }else{
        Dialog.alert({
          title: '系统提示',
          message: "活动和票种不匹配，请重新提交"
        }).then(() => {
          location.reload()
        });
      }

    },
    saveData () {
      let userInfo = JSON.parse(localStorage.getItem("userInfo"))
      if(this.isBuyTicket === 1){//购票模式

        let userMsg ={
          dynamicsJson:this.value,
          userType : this.regFormData.userType,
          name : this.regFormData.userName,
          sex : this.regFormData.userSex,
          phone : this.regFormData.userPhone,
          nationality : this.regFormData.userNational,
          province:this.regFormData.userCity && this.regFormData.userCity.length===2 ?this.regFormData.userCity[0]:null,
          city:this.regFormData.userCity && this.regFormData.userCity.length===2 ?this.regFormData.userCity[1]:null,
          cardType:this.regFormData.cardType,
          idCard:this.regFormData.idCard,
          email:this.regFormData.email,
          company:this.regFormData.company,
          job:this.regFormData.job,
          regFrom:"2",
          activityId:localStorage.getItem("activityNo"),
          valid:1,
          createBy:userInfo.login,
          channelCode: localStorage.getItem("channelCode") ? localStorage.getItem("channelCode"):null
        }
        let ticktMsg = this.ticketList.filter((val)=>{
          return val.id === this.ticketType
        })
        let param ={
          orderAmount:ticktMsg[0].price,
          sourceId:2,//1.pc 2.手机
          activityid:localStorage.getItem("activityNo"),
          ordersDetails:[{
            productId:ticktMsg[0].id,
            buyCounts:1,
            regInfoList:[userMsg]
          }]
        }
        this.isLoading = true
        return orderCreate(param).then(res => {
          this.isLoading = false
          if(res.code === 200 && res.data){
            this.orderNum = res.data.orderNum
            Notify({ type: 'success', message: '提交订单成功!' });
            this.showPay = true
          }
        })
      }else{
        let userMsg ={
          activityId:localStorage.getItem("activityNo"),
          activityName:localStorage.getItem("activity").activityName,
          dynamicsJson:this.value,
          userType : this.regFormData.userType,
          name : this.regFormData.userName,
          sex : this.regFormData.userSex,
          phone : this.regFormData.userPhone,
          nationality : this.regFormData.userNational,
          province:this.regFormData.userCity && this.regFormData.userCity.length===2 ?this.regFormData.userCity[0]:null,
          city:this.regFormData.userCity && this.regFormData.userCity.length===2 ?this.regFormData.userCity[1]:null,
          cardType:this.regFormData.cardType,
          idCard:this.regFormData.idCard,
          email:this.regFormData.email,
          company:this.regFormData.company,
          job:this.regFormData.job,
          regFrom:"2",//1.pc 2.手机
          valid:1,
          createBy:userInfo.login,
          channelCode: localStorage.getItem("channelCode") ? localStorage.getItem("channelCode"):null
        }
        this.isLoading = true
        return saveNoTicket(userMsg).then(res => {
          this.isLoading = false
          if(res.code === 200 ){
            Notify({ type: 'success', message: '报名提交成功!/Registration Submitted Successfully!' });
            localStorage.setItem("showOrder", false)
            localStorage.setItem("redirectType", 1)
            this.$router.replace({ path: '/RegistView'})
          }else{
            Notify({ type: 'warning', message: JSON.stringify(res) });
          }
        }).catch(()=>{
          this.isLoading = false
        })
      }

    },
    backForm(){
      this.step = 2
    },
    backTicket(){
      this.step = 1
    },
    goPayNow(){
      return orderGoPay({
        "orderNum": this.orderNum
      }).then(res => {

        if(res.code === 200 && res.data){
          this.orderMsg = res.data
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.fApi.resetFields();
    },
    //身份证号校验
    validatorCard (rule, value, callback) {
      let reg = IdentityCodeValid(value);
      if (value && reg == false) {
          callback('请输入正确的身份证号！'); // 身份证号校验未通过
      }
      callback();
    },
    clickNext(){
      if(this.ticketType){
        this.step = 2
      }else{
        Notify({ type: 'warning', message: "请选票！"});
      }
    },
    protocolClick(record){
      this.htmlMsg = record.protocolContent
      this.protocolName = record.protocolName
      this.showProtocol = true
    },
    cancelPay(){//取消付款
      this.showPay = false
      localStorage.setItem("showOrder",true)
      localStorage.setItem("redirectType",1)
      this.$router.replace({ path: '/RegistView'})
    },
    callbackSuccess(){//付款成功
      this.showPay = false
      localStorage.setItem("showOrder",true)
      localStorage.setItem("redirectType",1)
      this.$router.replace({ path: '/RegistView'})
    },
    selectedTicket(id){
      this.ticketType = id
    },
    onSelect(action) {
      Toast(action.text);
    },
    viewRule(record){
      if(record.info){
        this.showRule=true
        this.ruleContent = record.info
      }else{
        Notify({ type: 'warning', message: "无权益信息!"});
      }
    },

    async searchCompany(queryString, cb) {
      const result = await searchCompanyInfo(queryString);

      if (result.code == 200) {
        cb(result.data);
      }
      else {
        cb([]);
      }
    },
  },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>

body{
  padding-right: 0px !important;
  overflow: hidden;
}
.el-form-item__label:before{
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}
.el-form-item__label{
  margin-bottom: 5px !important;
}

h3 {
  margin: 40px 0 0;
}
a {
  color: #42b983;
}
.registEnter{
  padding-bottom: 20px;
  text-align: center;
}
.el-cascader{
  width: 100%;
}
.el-date-editor{
  width: 100% !important;
}
.el-select--smal{
  width: 100% !important;
}
.el-form-item__content,.el-radio-group{
  text-align: left !important;
}
.el-radio{
  margin-right: 10px !important;
}
.el-select, .el-select--small{
  width: 100% !important;

}
.el-form-item{
  margin-bottom: 15px !important;
}
.formEdite{
  text-align: left;
  margin: 10px 5% 0px 5%;
  padding: 20px 5%;
  width: 80%;
  background-color: #ffffff;
  border-radius: 10px;
}
.moneyShow{
  color: red;
  padding: 5px 0px;
}
.productMsg,.orderDetail{
  background:#ffffff;
  margin:5px;
  border-radius:5px
}
.productIcon{
  float:left;
}
.productAttribute{
  float:right;
  margin:auto;
  text-align:left;
  padding:5px;
}
.payMoneyMsg{
  padding:10px;
  margin:auto;
  text-align:right;
  font-size:16px;
}

.ticket_bg,.ticket_noOpen_bg{
  background-image: url("../assets/ticket_bg.png");
  background-size:100% 120px;
  width:100%;
  height:120px;
  margin:auto


}
.ticket_noOpen_bg{
}
.ticketMsg_left{
  padding:0px 5px;
  font-size: 12px;
}
.ticketMsg_left,.ticketMsg_right{
  text-align:left;
  line-height:25px;
  float:left;
}
.ticketMsg_right{
  margin:auto;
  text-align:center;
  width:35px;
  padding-top:5px;
  text-align:left;
  line-height:25px;
  float:right;
  margin-right: 45px;
}
.ticketName{
  font-size:14px;
  font-weight:bold;
  line-height:30px
}
.popupTitle{
  font-size: 16px;
  font-weight: bold;
  padding: 15px;
}
</style>

<style>
#form-create .el-select {
  width: 100%;
}
</style>
