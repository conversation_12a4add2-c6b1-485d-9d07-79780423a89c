import request from '@/utils/request'

// 获取优惠列表
export function getPreferentialList(data){
  return request({
    url: '/coupon/list',
    method: 'post',
    data
  })
}
// 获取优惠码
export function getGenerateCode(){
  return request({
    url: '/coupon/generateCode',
    method: 'get'
  })
}
// 提交优惠码
export function saveGenerateCode(data){
  return request({
    url: '/coupon/save',
    method: 'post',
    data
  })
}
// 删除优惠码
export function deleteGenerateCode(id){
  return request({
    url: `/coupon/delete/${id}`,
    method: 'get',
  })
}
// 修改（失效）优惠码
export function updateGenerateCode(data){
  return request({
    url: '/coupon/update',
    method: 'post',
    data
  })
}
export default { getPreferentialList, getGenerateCode, saveGenerateCode, deleteGenerateCode, updateGenerateCode }
